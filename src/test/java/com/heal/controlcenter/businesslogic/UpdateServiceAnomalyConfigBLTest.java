package com.heal.controlcenter.businesslogic;

import com.heal.configuration.pojos.*;
import com.heal.controlcenter.beans.ServiceSuppPersistenceConfigurationBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.ServiceConfigurationDao;
import com.heal.controlcenter.dao.redis.ServiceRepo;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.ServiceSuppPersistenceConfigPojo;
import com.heal.controlcenter.util.*;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class UpdateServiceAnomalyConfigBLTest {

    @InjectMocks
    private UpdateServiceAnomalyConfigBL updateServiceAnomalyBL;

    @Mock
    private CommonUtils commonUtils;

    @Mock
    private ServiceConfigurationDao serviceConfigurationDao;

    @Mock
    private ServiceRepo serviceRepo;

    @Mock
    private DateTimeUtil dateTimeUtil;

    @Mock
    private ServerValidationUtils serverValidationUtils;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void clientValidation_NullAuthKey_ThrowsClientException() {
        Map<String, ServiceSuppPersistenceConfigPojo> serviceConfigDetails = new HashMap<>();
        String[] params = {null, "accountIdentifier", "serviceIdentifier"};

        ClientException exception = assertThrows(ClientException.class, () ->
                updateServiceAnomalyBL.clientValidation(serviceConfigDetails, params));
        assertEquals("ClientException : " + UIMessages.AUTH_KEY_INVALID, exception.getMessage());
    }

    @Test
    void clientValidation_EmptyAccountIdentifier_ThrowsClientException() {
        Map<String, ServiceSuppPersistenceConfigPojo> serviceConfigDetails = new HashMap<>();
        String[] params = {"authKey", "", "serviceIdentifier"};

        ClientException exception = assertThrows(ClientException.class, () ->
                updateServiceAnomalyBL.clientValidation(serviceConfigDetails, params));
        assertEquals("ClientException : " + UIMessages.ACCOUNT_IDENTIFIER_INVALID, exception.getMessage());
    }

    @Test
    void clientValidation_EmptyServiceIdentifier_ThrowsClientException() {
        Map<String, ServiceSuppPersistenceConfigPojo> serviceConfigDetails = new HashMap<>();
        String[] params = {"authKey", "accountIdentifier", ""};

        ClientException exception = assertThrows(ClientException.class, () ->
                updateServiceAnomalyBL.clientValidation(serviceConfigDetails, params));
        assertEquals("ClientException : " + UIMessages.SERVICE_IDENTIFIER_INVALID, exception.getMessage());
    }

    @Test
    void clientValidation_EmptyServiceConfigDetails_ThrowsClientException() throws Exception {
        Map<String, ServiceSuppPersistenceConfigPojo> serviceConfigDetails = new HashMap<>();
        String[] params = {"authKey", "accountIdentifier", "serviceIdentifier"};
        when(commonUtils.getUserId("authKey")).thenReturn("userId");

        ClientException exception = assertThrows(ClientException.class, () ->
                updateServiceAnomalyBL.clientValidation(serviceConfigDetails, params));
        assertEquals("ClientException : Request object should contain service configuration details.", exception.getMessage());
    }

    @Test
    void clientValidation_ValidInput_ReturnsUtilityBean() throws Exception {
        Map<String, ServiceSuppPersistenceConfigPojo> serviceConfigDetails = new HashMap<>();
        serviceConfigDetails.put("key", new ServiceSuppPersistenceConfigPojo());
        String[] params = {"authKey", "accountIdentifier", "serviceIdentifier"};

        UtilityBean<Map<String, ServiceSuppPersistenceConfigPojo>> result =
                updateServiceAnomalyBL.clientValidation(serviceConfigDetails, params);

        assertNotNull(result);
        assertEquals(serviceConfigDetails, result.getPojoObject());
    }

    @Test
    void serverValidation_ValidInput_ReturnsAccountServiceKey() throws ServerException {
        HashMap<String, String> requestParamsMap = new HashMap<>();
        requestParamsMap.put(Constants.AUTH_KEY, "authKey");
        requestParamsMap.put(Constants.ACCOUNT_IDENTIFIER, "accountIdentifier");
        requestParamsMap.put(Constants.SERVICE_IDENTIFIER, "serviceIdentifier");

        Account account = new Account();
        account.setId(1);
        account.setIdentifier("accountIdentifier");

        when(serverValidationUtils.authKeyValidation(Mockito.anyString())).thenReturn("testUser");
        when(serverValidationUtils.accountValidation(Mockito.anyString())).thenReturn(account);
        when(serverValidationUtils.userAccessDetailsValidation(Mockito.anyString(), any())).thenReturn(new com.heal.configuration.entities.UserAccessDetails());
        when(serverValidationUtils.serviceValidation(anyString(), anyString(), anyString(), any())).thenReturn(Service.builder().id(1).build());

        List<ServiceSuppPersistenceConfigurationBean> serviceConfigList = new ArrayList<>();
        ServiceSuppPersistenceConfigurationBean newBean = new ServiceSuppPersistenceConfigurationBean();
        newBean.setId(2);
        serviceConfigList.add(newBean);
        when(serviceConfigurationDao.getServiceConfiguration(anyInt(), anyInt())).thenReturn(serviceConfigList);

        Map<String, ServiceSuppPersistenceConfigPojo> serviceConfigDetails = new HashMap<>();
        ServiceSuppPersistenceConfigPojo configPojo = new ServiceSuppPersistenceConfigPojo();
        configPojo.setServiceConfigId(2);
        serviceConfigDetails.put("key", configPojo);
        UtilityBean<Map<String, ServiceSuppPersistenceConfigPojo>> result = updateServiceAnomalyBL.serverValidation(UtilityBean.<Map<String, ServiceSuppPersistenceConfigPojo>>builder()
                .pojoObject(serviceConfigDetails)
                .requestParams(requestParamsMap)
                .build());

        assertNotNull(result);
        assertEquals("serviceIdentifier", result.getRequestParams().get(Constants.SERVICE_IDENTIFIER));
    }

    @Test
    void process_ValidInput_Success() throws Exception {
        HashMap<String, String> requestParamsMap = new HashMap<>();
        requestParamsMap.put(Constants.AUTH_KEY, "authKey");
        requestParamsMap.put(Constants.ACCOUNT_IDENTIFIER, "accountIdentifier");
        requestParamsMap.put(Constants.SERVICE_IDENTIFIER, "serviceIdentifier");
        requestParamsMap.put(Constants.SERVICE_ID, "2");
        requestParamsMap.put(Constants.ACCOUNT_ID, "1");

        Map<String, ServiceSuppPersistenceConfigPojo> serviceConfigDetailsMap = getServiceSuppPersistenceConfigPojoMap();

        BasicEntity serviceEntity = BasicEntity.builder().id(2).identifier("serviceIdentifier").build();
        Service service = Service.builder().id(2).identifier("serviceIdentifier").build();
        Account accountBean = Account.builder().id(1).identifier("accountIdentifier").build();

        HashMap<String, Object> metadata = new HashMap<>();
        metadata.put(Constants.ACCOUNT, accountBean);
        metadata.put(Constants.SERVICE, service);

        ServiceConfiguration serviceConfiguration = new ServiceConfiguration();
        PersistenceSuppressionConfiguration persistenceConfig = new PersistenceSuppressionConfiguration();
        persistenceConfig.setEndCollectionInterval(58);

        PersistenceSuppressionConfiguration persistenceConfig2 = new PersistenceSuppressionConfiguration();
        persistenceConfig.setEndCollectionInterval(80);
        serviceConfiguration.setPersistenceSuppressionConfigurations(Arrays.asList(persistenceConfig,persistenceConfig2));

        service.setServiceConfiguration(serviceConfiguration);


        when(commonUtils.getUserId("authKey")).thenReturn("userId");
        when(dateTimeUtil.getTimeInGMT(anyLong())).thenReturn(String.valueOf(System.currentTimeMillis()));
        when(serviceRepo.getAllServicesDetails(anyString())).thenReturn(Collections.singletonList(serviceEntity));
        when(serviceRepo.getServiceConfigurationByIdentifier(anyString(), anyString())).thenReturn(service);
        when(serviceRepo.getAllServicesDetails(anyString())).thenReturn(Collections.singletonList(serviceEntity));

        assertDoesNotThrow(() -> updateServiceAnomalyBL.process(UtilityBean.<Map<String, ServiceSuppPersistenceConfigPojo>>builder()
                .pojoObject(serviceConfigDetailsMap)
                .requestParams(requestParamsMap)
                .metadata(metadata)
                .build()));

    }

    @NotNull
    private static Map<String, ServiceSuppPersistenceConfigPojo> getServiceSuppPersistenceConfigPojoMap() {
        Map<String, ServiceSuppPersistenceConfigPojo> serviceConfigDetailsMap = new HashMap<>();
        ServiceSuppPersistenceConfigPojo configPojo = new ServiceSuppPersistenceConfigPojo();
        configPojo.setServiceConfigId(1);
        configPojo.setLowPersistence(10);
        configPojo.setMediumPersistence(20);
        configPojo.setHighPersistence(30);
        configPojo.setLowSuppression(40);
        configPojo.setMediumSuppression(50);
        configPojo.setHighSuppression(60);
        configPojo.setLowEnable(true);
        configPojo.setMediumEnable(true);
        configPojo.setHighEnable(true);
        serviceConfigDetailsMap.put(Constants.OPERATOR_LESS_THAN, configPojo);
        serviceConfigDetailsMap.put(Constants.OPERATOR_GREATER_THAN, configPojo);
        return serviceConfigDetailsMap;
    }

}
