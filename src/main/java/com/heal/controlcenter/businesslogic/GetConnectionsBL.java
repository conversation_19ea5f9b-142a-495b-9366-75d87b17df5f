package com.heal.controlcenter.businesslogic;

import com.appnomic.appsone.common.enums.DiscoveryStatus;
import com.heal.configuration.pojos.Account;
import com.heal.controlcenter.beans.AutoDiscoveryDiscoveredConnectionsBean;
import com.heal.controlcenter.beans.ConnectionDetailsBean;
import com.heal.controlcenter.beans.ControllerBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.dao.mysql.AutoDiscoveryDao;
import com.heal.controlcenter.dao.mysql.ConnectionDetailsDao;
import com.heal.controlcenter.dao.mysql.ControllerDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.GetConnectionPojo;
import com.heal.controlcenter.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class GetConnectionsBL implements BusinessLogic<Object, Integer, List<GetConnectionPojo>> {

    private final AccountsDao accountDao;
    private final ControllerDao controllerDao;
    private final ConnectionDetailsDao connectionDetailsDao;
    private final AutoDiscoveryDao autoDiscoveryDao;
    private final DateTimeUtil dateTimeUtil;
    private final ClientValidationUtils clientValidationUtils;
    private final ServerValidationUtils serverValidationUtils;

    public GetConnectionsBL(AccountsDao accountDao, ControllerDao controllerDao,
                            ConnectionDetailsDao connectionDetailsDao, AutoDiscoveryDao autoDiscoveryDao,
                            DateTimeUtil dateTimeUtil, ClientValidationUtils clientValidationUtils,
                            ServerValidationUtils serverValidationUtils) {
        this.accountDao = accountDao;
        this.controllerDao = controllerDao;
        this.connectionDetailsDao = connectionDetailsDao;
        this.autoDiscoveryDao = autoDiscoveryDao;
        this.dateTimeUtil = dateTimeUtil;
        this.clientValidationUtils = clientValidationUtils;
        this.serverValidationUtils = serverValidationUtils;
    }

    @Override
    public UtilityBean<Object> clientValidation(Object requestBody, String... requestParams) throws ClientException {
        String authKey = requestParams[0];
        clientValidationUtils.authKeyValidation(authKey);

        String accountIdentifier = requestParams[1];
        clientValidationUtils.accountIdentifierValidation(accountIdentifier);

        HashMap<String, String> requestParamsMap = CommonUtils.buildRequestParams(authKey, accountIdentifier);

        return UtilityBean.builder()
                .requestParams(requestParamsMap)
                .build();
    }

    @Override
    public Integer serverValidation(UtilityBean<Object> utilityBean) throws ServerException {

        String authKey = utilityBean.getRequestParams().get(Constants.AUTH_KEY);
        serverValidationUtils.authKeyValidation(authKey);

        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
        Account account = serverValidationUtils.accountValidation(accountIdentifier);

        List<ControllerBean> controllerBeanList;
        try {
            controllerBeanList = controllerDao.getServicesList(account.getId());
        } catch (HealControlCenterException e) {
            return 0;
        }

        if (controllerBeanList.isEmpty()) {
            return 0;
        }

        return account.getId();
    }

    @Override
    public List<GetConnectionPojo> process(Integer accountId) throws DataProcessingException {
        if (accountId == 0) {
            return Collections.emptyList();
        }

        List<ConnectionDetailsBean> healConnectionDetailsBean;
        List<AutoDiscoveryDiscoveredConnectionsBean> discoveredConnectionsBeanList;
        try {
            healConnectionDetailsBean = connectionDetailsDao.getConnectionsByAccountId(accountId);
            discoveredConnectionsBeanList = autoDiscoveryDao.getDiscoveredConnectionsList();
        } catch (HealControlCenterException e) {
            throw new DataProcessingException(e.getMessage());
        }
        // From Heal UI
        List<GetConnectionPojo> connections = healConnectionDetailsBean.parallelStream()
                .map(c -> GetConnectionPojo.builder()
                        .sourceServiceId(c.getSourceId())
                        .sourceServiceName(c.getSourceName())
                        .sourceServiceIdentifier(c.getSourceIdentifier())
                        .destinationServiceId(c.getDestinationId())
                        .destinationServiceName(c.getDestinationName())
                        .destinationServiceIdentifier(c.getDestinationIdentifier())
                        .process(c.getIsDiscovery() == 0 ? "Manual" : "Auto")
                        .status(DiscoveryStatus.ADDED_TO_SYSTEM)
                        .lastDiscoveryRunTime(dateTimeUtil.getGMTToEpochTime(String.valueOf(c.getUpdatedTime())))
                        .build())
                .collect(Collectors.toList());

        // From Auto Discovery agent
        List<GetConnectionPojo> autoDiscoConnections = discoveredConnectionsBeanList.parallelStream()
                .map(c -> GetConnectionPojo.builder()
                        .sourceServiceId(c.getSourceId())
                        .sourceServiceName(c.getSourceName())
                        .sourceServiceIdentifier(c.getSourceIdentifier())
                        .destinationServiceId(c.getDestinationId())
                        .destinationServiceName(c.getDestinationName())
                        .destinationServiceIdentifier(c.getDestinationIdentifier())
                        .process("Auto")
                        .status(c.getDiscoveryStatus())
                        .lastDiscoveryRunTime(c.getLastUpdatedTime())
                        .build())
                .collect(Collectors.toList());

        connections.addAll(autoDiscoConnections);

        return connections.parallelStream()
                .sorted(Comparator.comparing(GetConnectionPojo::getProcess)
                        .thenComparing(GetConnectionPojo::getLastDiscoveryRunTime, Comparator.reverseOrder()))
                .distinct().collect(Collectors.toList());
    }
}
