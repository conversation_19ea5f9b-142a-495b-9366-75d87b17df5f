package com.heal.controlcenter.utils;

import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.util.ClientValidationUtils;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;

class ClientValidationUtilsTest {

    public static final String REASON_IT_IS_EITHER_NULL_OR_EMPTY = "Reason: It is either NULL or empty";
    private final ClientValidationUtils clientValidationUtils = new ClientValidationUtils();

    @Test
    void testAccountIdentifierValidation_ValidInput() {
        assertDoesNotThrow(() -> clientValidationUtils.accountIdentifierValidation("validAccount"));
    }

    @Test
    void testAccountIdentifierValidation_InvalidInput() {
        ClientException exception = assertThrows(ClientException.class,
                () -> clientValidationUtils.accountIdentifierValidation(null));
        assert exception.getMessage().contains(REASON_IT_IS_EITHER_NULL_OR_EMPTY);

        exception = assertThrows(ClientException.class,
                () -> clientValidationUtils.accountIdentifierValidation("  "));
        assert exception.getMessage().contains(REASON_IT_IS_EITHER_NULL_OR_EMPTY);
    }

    @Test
    void testServiceIdentifierValidation_ValidInput() {
        assertDoesNotThrow(() -> clientValidationUtils.serviceIdentifierValidation("validService"));
    }

    @Test
    void testServiceIdentifierValidation_InvalidInput() {
        ClientException exception = assertThrows(ClientException.class,
                () -> clientValidationUtils.serviceIdentifierValidation(null));
        assert exception.getMessage().contains(REASON_IT_IS_EITHER_NULL_OR_EMPTY);

        exception = assertThrows(ClientException.class,
                () -> clientValidationUtils.serviceIdentifierValidation("  "));
        assert exception.getMessage().contains(REASON_IT_IS_EITHER_NULL_OR_EMPTY);
    }

    @Test
    void testInstanceIdentifierValidation_ValidInput() {
        assertDoesNotThrow(() -> clientValidationUtils.instanceIdentifierValidation("validInstance"));
    }

    @Test
    void testInstanceIdentifierValidation_InvalidInput() {
        ClientException exception = assertThrows(ClientException.class,
                () -> clientValidationUtils.instanceIdentifierValidation(null));
        assert exception.getMessage().contains(REASON_IT_IS_EITHER_NULL_OR_EMPTY);

        exception = assertThrows(ClientException.class,
                () -> clientValidationUtils.instanceIdentifierValidation("  "));
        assert exception.getMessage().contains(REASON_IT_IS_EITHER_NULL_OR_EMPTY);
    }

    @Test
    void testAuthKeyValidation_ValidInput() {
        assertDoesNotThrow(() -> clientValidationUtils.authKeyValidation("validAuthKey"));
    }

    @Test
    void testAuthKeyValidation_InvalidInput() {
        ClientException exception = assertThrows(ClientException.class,
                () -> clientValidationUtils.authKeyValidation(null));
        assert exception.getMessage().contains(REASON_IT_IS_EITHER_NULL_OR_EMPTY);

        exception = assertThrows(ClientException.class,
                () -> clientValidationUtils.authKeyValidation("  "));
        assert exception.getMessage().contains(REASON_IT_IS_EITHER_NULL_OR_EMPTY);
    }
}