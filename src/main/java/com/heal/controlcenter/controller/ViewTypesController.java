package com.heal.controlcenter.controller;

import com.heal.controlcenter.aop.AopCustomAnnotation;
import com.heal.controlcenter.businesslogic.GetViewTypesByNameBL;
import com.heal.controlcenter.pojo.BasicUserDetails;
import com.heal.controlcenter.pojo.ResponsePojo;
import com.heal.controlcenter.pojo.ViewTypeResponse;
import com.heal.controlcenter.swagger.CommonApiResponses;
import com.heal.controlcenter.util.JsonFileParser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Controller for fetching ViewTypes by typeName from Redis.
 */
@Slf4j
@RestController
public class ViewTypesController {

    @Autowired
    GetViewTypesByNameBL getViewTypesByNameBL;
    @Autowired
    JsonFileParser headersParser;

    /**
     * Fetches all ViewTypes objects from Redis where typeName matches the provided argument.
     * Uses standard response structure and AOP logging.
     *
     * @param typeName    The typeName to filter viewtypes (case-insensitive).
     * @param userDetails Basic user details for context (if needed for future expansion)
     * @return ResponseEntity containing a list of ViewTypeResponse with the given typeName.
     */
    @Operation(
            summary = "Get viewtypes by typeName",
            description = "Fetches all ViewTypeResponse objects from Redis where typeName matches the provided argument.",
            responses = {
                    @ApiResponse(responseCode = "200", description = " viewtypes fetched successfully.",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = ViewTypeResponse.class)))
            }
    )
    @CommonApiResponses
    @AopCustomAnnotation
    @GetMapping("/view-types")
    public ResponseEntity<ResponsePojo<List<ViewTypeResponse>>> getViewTypesByTypeName(
            @Parameter(description = "Type name to filter viewtypes", required = true, example = "Environment")
            @RequestParam String typeName,
            BasicUserDetails userDetails) {
        log.info("Received request to fetch viewtypes for typeName: {}", typeName);
        try {
            var utilityBean = getViewTypesByNameBL.clientValidation(typeName);
            utilityBean.getMetadata().put("userId", userDetails.getUserIdentifier());
            var validatedBean = getViewTypesByNameBL.serverValidation(utilityBean);
            List<ViewTypeResponse> result = getViewTypesByNameBL.process(validatedBean);

            ResponsePojo<List<ViewTypeResponse>> responseBean = new ResponsePojo<>(
                    String.format("ViewTypes fetched successfully for typeName '%s'.", typeName),
                    result,
                    HttpStatus.OK
            );
            return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responseBean);
        } catch (Exception e) {
            log.error("Error fetching viewtypes for typeName '{}': {}", typeName, e.getMessage(), e);
            ResponsePojo<List<ViewTypeResponse>> responseBean = new ResponsePojo<>(e.getMessage(), null, HttpStatus.INTERNAL_SERVER_ERROR);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).headers(headersParser.loadHeaderConfiguration()).body(responseBean);
        }
    }
}
