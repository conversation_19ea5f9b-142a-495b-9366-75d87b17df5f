package com.heal.controlcenter.dao.mysql;

import com.heal.configuration.pojos.Tags;
import com.heal.controlcenter.beans.TagDetailsBean;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.pojo.TagMappingDetails;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Slf4j
@Repository
public class TagsDao {

    @Autowired
    JdbcTemplate jdbcTemplate;

    public int addTagMappingDetails(TagMappingDetails bean) throws ControlCenterException {
        String query = "insert into tag_mapping (tag_id, object_id, object_ref_table, tag_key, tag_value, created_time, updated_time, account_id, user_details_id) " +
                "values (?, ?, ?, ?, ?, ?, ?, ?, ?)";
        try{
            log.debug("adding tag mapping details.");
            return jdbcTemplate.update(query, bean.getTagId(), bean.getObjectId(), bean.getObjectRefTable(), bean.getTagKey(), bean.getTagValue(), bean.getCreatedTime(), bean.getUpdatedTime(), bean.getAccountId(), bean.getUserDetailsId());
        } catch (Exception ex) {
            log.error("Error in adding tag mapping details", ex);
            throw new ControlCenterException("Error in adding tag mapping details");
        }
    }

    public TagDetailsBean getTagDetailsByName(String name) throws ControlCenterException {
        String query = "select id,name,tag_type_id tagTypeId,is_predefined isPredefined,ref_table refTable,created_time createdTime,updated_time updatedTime,account_id accountId," +
                "user_details_id lastModifiedBy,ref_where_column_name refWhereColumnName,ref_select_column_name refSelectColumnName from tag_details where name = ?";
        try{
            log.debug("getting user tag.");
            return jdbcTemplate.queryForObject(query, new BeanPropertyRowMapper<>(TagDetailsBean.class), name);
        } catch (Exception ex) {
            log.error("Error in getting tag details.");
            throw new ControlCenterException("Error in getting tag details.");
        }
    }

    public List<TagDetailsBean> getTagDetailsByAccountId(int accountId) {
        String query = "SELECT id, name, tag_type_id tagTypeId, is_predefined isPredefined, ref_table refTable, created_time createdTime, updated_time updatedTime, " +
                "account_id accountId, user_details_id userDetailsId, ref_where_column_name refWhereColumnName, ref_select_column_name refSelectColumnName " +
                "FROM tag_details WHERE account_id IN (1, ?)";
        try{
            log.debug("getting user tag details.");
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(TagDetailsBean.class), accountId);
        } catch (Exception ex) {
            log.error("Error in getting tag details.");
        }
        return Collections.emptyList();
    }

    public List<TagMappingDetails> getTagMappingDetails(int accountId) {
        String query = "SELECT id, tag_id tagId, object_id objectId, object_ref_table objectRefTable, tag_key tagKey, tag_value tagValue, " +
                "created_time createdTime, updated_time updatedTime, account_id accountId, user_details_id lastModifiedBy FROM tag_mapping " +
                "WHERE object_ref_table != 'transaction' AND account_id = ?";
        try{
            log.debug("getting tag mapping details.");
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(TagMappingDetails.class), accountId);
        } catch (Exception ex) {
            log.error("Error in getting tag mapping details.");
        }
        return Collections.emptyList();
    }

    public List<Tags> getTagsByObjectId(int objectId, String refTable) throws ControlCenterException {
        String query = "select td.name, tag_key, tag_value from tag_mapping, tag_details td " +
                "where object_id= ? and object_ref_table=? and tag_id=td.id";

        try {
            return jdbcTemplate.query(query, (rs, rowNum) -> Tags.builder()
                    .key(rs.getString("tag_key"))
                    .value(rs.getString("tag_value"))
                    .type(rs.getString("td.name"))
                    .build(), objectId, refTable);
        } catch (EmptyResultDataAccessException e) {
            return new ArrayList<>();
        } catch (Exception e) {
            log.error("Exception occurred when fetching tags for accountId:{}, refTable:{} ", objectId, refTable, e);
            throw new ControlCenterException("Error in fetching account related tags");
        }
    }
}
