<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CORS Testing Tool</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .log { background: #f8f9fa; padding: 10px; border-radius: 3px; margin: 10px 0; font-family: monospace; white-space: pre-wrap; }
        input[type="text"] { width: 100%; padding: 8px; margin: 5px 0; border: 1px solid #ddd; border-radius: 3px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 CORS Testing Tool</h1>
        
        <div class="test-section">
            <h3>Configuration</h3>
            <label>API Base URL:</label>
            <input type="text" id="apiUrl" value="http://localhost:8080/v2.0/api" placeholder="http://localhost:8080/v2.0/api">
            <small>Try these if default doesn't work:</small>
            <div style="margin: 5px 0;">
                <button type="button" onclick="setApiUrl('http://localhost:8080/v2.0/api')" style="font-size: 12px; padding: 5px;">Default</button>
                <button type="button" onclick="setApiUrl('http://127.0.0.1:8080/v2.0/api')" style="font-size: 12px; padding: 5px;">127.0.0.1</button>
                <button type="button" onclick="setApiUrl('http://localhost:8080')" style="font-size: 12px; padding: 5px;">No /v2.0/api</button>
                <button type="button" onclick="setApiUrl('https://localhost:8080/v2.0/api')" style="font-size: 12px; padding: 5px;">HTTPS</button>
            </div>

            <label>Authorization Token (optional):</label>
            <input type="text" id="authToken" placeholder="Bearer your-token-here">
        </div>

        <div class="test-section">
            <h3>🔍 Connection Test</h3>
            <p>Test if your backend is reachable before testing CORS.</p>
            <button onclick="testConnection()">Test Backend Connection</button>
            <div id="connectionResult" class="log"></div>
        </div>

        <div class="test-section">
            <h3>🧪 Test 1: Simple GET Request (No Preflight)</h3>
            <p>This test uses only simple headers, so no preflight OPTIONS request is sent.</p>
            <button onclick="testSimpleRequest()">Test Simple GET</button>
            <div id="simpleResult" class="log"></div>
        </div>

        <div class="test-section">
            <h3>🧪 Test 2: Complex GET Request (With Preflight)</h3>
            <p>This test includes Authorization header, which triggers a preflight OPTIONS request.</p>
            <button onclick="testComplexRequest()">Test Complex GET</button>
            <div id="complexResult" class="log"></div>
        </div>

        <div class="test-section">
            <h3>🧪 Test 3: POST Request (With Preflight)</h3>
            <p>This test sends a POST request with JSON data, which triggers preflight.</p>
            <button onclick="testPostRequest()">Test POST</button>
            <div id="postResult" class="log"></div>
        </div>

        <div class="test-section">
            <h3>🧪 Test 4: Manual OPTIONS Request</h3>
            <p>This test manually sends an OPTIONS request to check preflight response.</p>
            <button onclick="testOptionsRequest()">Test OPTIONS</button>
            <div id="optionsResult" class="log"></div>
        </div>

        <div class="test-section">
            <h3>📊 CORS Headers Analysis</h3>
            <button onclick="analyzeHeaders()">Analyze CORS Headers</button>
            <div id="headersResult" class="log"></div>
        </div>
    </div>

    <script>
        function log(elementId, message, isError = false) {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            element.textContent = `[${timestamp}] ${message}`;
            element.parentElement.className = `test-section ${isError ? 'error' : 'success'}`;
        }

        function getApiUrl() {
            return document.getElementById('apiUrl').value.trim();
        }

        function getAuthToken() {
            return document.getElementById('authToken').value.trim();
        }

        function setApiUrl(url) {
            document.getElementById('apiUrl').value = url;
        }

        async function testConnection() {
            const baseUrl = getApiUrl();
            log('connectionResult', 'Testing backend connection...');

            // Try a simple endpoint first
            const testUrls = [
                `${baseUrl}/timezones`,
                `${baseUrl}/health`,
                `${baseUrl}`,
                baseUrl.replace('/v2.0/api', '') + '/actuator/health',
                baseUrl.replace('/v2.0/api', '') + '/health'
            ];

            for (let url of testUrls) {
                try {
                    log('connectionResult', `Trying: ${url}`);

                    const response = await fetch(url, {
                        method: 'GET',
                        mode: 'no-cors' // This bypasses CORS for connection testing
                    });

                    log('connectionResult', `✅ CONNECTION SUCCESS!\nBackend is reachable at: ${url}\nYou can now test CORS functionality.`);
                    return;
                } catch (error) {
                    console.log(`Failed ${url}:`, error.message);
                }
            }

            log('connectionResult', `❌ CONNECTION FAILED!\n\nTroubleshooting:\n1. Is your backend running? Check: mvn spring-boot:run\n2. Try different URLs using the buttons above\n3. Check if port 8080 is correct\n4. Try opening ${baseUrl}/timezones directly in a new browser tab`, true);
        }

        async function testSimpleRequest() {
            const url = `${getApiUrl()}/timezones`;
            log('simpleResult', 'Testing simple GET request...');
            
            try {
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json'
                    }
                });
                
                log('simpleResult', `✅ SUCCESS: ${response.status} ${response.statusText}\nCORS Headers:\n${getCorsHeaders(response)}`);
            } catch (error) {
                log('simpleResult', `❌ ERROR: ${error.message}`, true);
            }
        }

        async function testComplexRequest() {
            const url = `${getApiUrl()}/accounts`;
            const token = getAuthToken();
            log('complexResult', 'Testing complex GET request with Authorization header...');
            
            try {
                const headers = {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                };
                
                if (token) {
                    headers['Authorization'] = token;
                }
                
                const response = await fetch(url, {
                    method: 'GET',
                    headers: headers
                });
                
                log('complexResult', `✅ SUCCESS: ${response.status} ${response.statusText}\nCORS Headers:\n${getCorsHeaders(response)}`);
            } catch (error) {
                log('complexResult', `❌ ERROR: ${error.message}`, true);
            }
        }

        async function testPostRequest() {
            const url = `${getApiUrl()}/accounts`;
            const token = getAuthToken();
            log('postResult', 'Testing POST request...');
            
            try {
                const headers = {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                };
                
                if (token) {
                    headers['Authorization'] = token;
                }
                
                const response = await fetch(url, {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify({ test: 'data' })
                });
                
                log('postResult', `✅ SUCCESS: ${response.status} ${response.statusText}\nCORS Headers:\n${getCorsHeaders(response)}`);
            } catch (error) {
                log('postResult', `❌ ERROR: ${error.message}`, true);
            }
        }

        async function testOptionsRequest() {
            const url = `${getApiUrl()}/accounts`;
            log('optionsResult', 'Testing OPTIONS request (preflight)...');
            
            try {
                const response = await fetch(url, {
                    method: 'OPTIONS',
                    headers: {
                        'Origin': window.location.origin,
                        'Access-Control-Request-Method': 'GET',
                        'Access-Control-Request-Headers': 'Content-Type,Authorization'
                    }
                });
                
                log('optionsResult', `✅ SUCCESS: ${response.status} ${response.statusText}\nPreflight Headers:\n${getCorsHeaders(response)}`);
            } catch (error) {
                log('optionsResult', `❌ ERROR: ${error.message}`, true);
            }
        }

        async function analyzeHeaders() {
            const url = `${getApiUrl()}/timezones`;
            log('headersResult', 'Analyzing CORS headers...');
            
            try {
                const response = await fetch(url, {
                    method: 'GET',
                    headers: { 'Accept': 'application/json' }
                });
                
                const corsAnalysis = analyzeCorsHeaders(response);
                log('headersResult', corsAnalysis);
            } catch (error) {
                log('headersResult', `❌ ERROR: ${error.message}`, true);
            }
        }

        function getCorsHeaders(response) {
            const corsHeaders = [
                'access-control-allow-origin',
                'access-control-allow-methods',
                'access-control-allow-headers',
                'access-control-allow-credentials',
                'access-control-max-age'
            ];
            
            let result = '';
            corsHeaders.forEach(header => {
                const value = response.headers.get(header);
                if (value) {
                    result += `${header}: ${value}\n`;
                }
            });
            
            return result || 'No CORS headers found';
        }

        function analyzeCorsHeaders(response) {
            const origin = response.headers.get('access-control-allow-origin');
            const methods = response.headers.get('access-control-allow-methods');
            const headers = response.headers.get('access-control-allow-headers');
            const credentials = response.headers.get('access-control-allow-credentials');
            
            let analysis = '📊 CORS Configuration Analysis:\n\n';
            
            analysis += `Origin Policy: ${origin || 'NOT SET'}\n`;
            if (origin === '*') {
                analysis += '  ✅ Allows all origins\n';
            } else if (origin) {
                analysis += `  ✅ Allows specific origin: ${origin}\n`;
            } else {
                analysis += '  ❌ No origin policy set\n';
            }
            
            analysis += `\nAllowed Methods: ${methods || 'NOT SET'}\n`;
            if (methods && methods.includes('OPTIONS')) {
                analysis += '  ✅ OPTIONS method supported (preflight enabled)\n';
            } else {
                analysis += '  ❌ OPTIONS method missing (preflight may fail)\n';
            }
            
            analysis += `\nAllowed Headers: ${headers || 'NOT SET'}\n`;
            if (headers && headers.includes('Authorization')) {
                analysis += '  ✅ Authorization header supported\n';
            } else {
                analysis += '  ⚠️  Authorization header may not be supported\n';
            }
            
            analysis += `\nCredentials: ${credentials || 'NOT SET'}\n`;
            if (credentials === 'true' && origin === '*') {
                analysis += '  ❌ INVALID: Cannot use credentials with wildcard origin\n';
            }
            
            return analysis;
        }

        // Auto-detect current page origin
        window.onload = function() {
            console.log('🔍 CORS Testing Tool loaded');
            console.log('Current origin:', window.location.origin);
            console.log('Open browser DevTools (F12) to see detailed network requests');
        };
    </script>
</body>
</html>
