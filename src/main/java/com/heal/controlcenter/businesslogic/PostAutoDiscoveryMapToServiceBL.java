package com.heal.controlcenter.businesslogic;

import com.heal.configuration.pojos.Account;
import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.dao.mysql.AutoDiscoveryDao;
import com.heal.controlcenter.dao.mysql.ControllerDao;
import com.heal.controlcenter.enums.Direction;
import com.heal.controlcenter.enums.Entity;
import com.heal.controlcenter.enums.Environment;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.AutoDiscoveryDiscoveredConnectionsPojo;
import com.heal.controlcenter.pojo.AutoDiscoveryMapEntityPojo;
import com.heal.controlcenter.util.ClientValidationUtils;
import com.heal.controlcenter.util.CommonUtils;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.ServerValidationUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class PostAutoDiscoveryMapToServiceBL implements BusinessLogic<AutoDiscoveryMapEntityPojo, AutoDiscoveryMapEntityPojo, List<String>> {

    @Autowired
    CommonUtils commonUtils;
    @Autowired
    AccountsDao accountsDao;
    @Autowired
    AutoDiscoveryDao autoDiscoveryDao;
    @Autowired
    ControllerDao controllerDao;
    @Autowired
    ClientValidationUtils clientValidationUtils;
    @Autowired
    ServerValidationUtils serverValidationUtils;

    List<AutoDiscoveryProcessBean> processesList;

    @Override
    public UtilityBean<AutoDiscoveryMapEntityPojo> clientValidation(AutoDiscoveryMapEntityPojo requestBody, String... requestParams) throws ClientException {

        String authKey = requestParams[0];
        clientValidationUtils.authKeyValidation(authKey);

        String accountIdentifier = requestParams[1];
        clientValidationUtils.accountIdentifierValidation(accountIdentifier);

        Map<String, String> error = requestBody.validate();
        if (!error.isEmpty()) {
            String err = error.toString();
            log.error(err);
            throw new ClientException(err);
        }

        HashMap<String, String> requestParamsMap = CommonUtils.buildRequestParams(authKey, accountIdentifier);

        return UtilityBean.<AutoDiscoveryMapEntityPojo>builder()
                .requestParams(requestParamsMap)
                .pojoObject(requestBody)
                .build();
    }

    @Override
    public AutoDiscoveryMapEntityPojo serverValidation(UtilityBean<AutoDiscoveryMapEntityPojo> utilityBean) throws ServerException {
        String authKey = utilityBean.getRequestParams().get(Constants.AUTH_KEY);
        serverValidationUtils.authKeyValidation(authKey);

        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
        Account account = serverValidationUtils.accountValidation(accountIdentifier);

        AutoDiscoveryMapEntityPojo autoDiscoveryEntity = utilityBean.getPojoObject();

        return AutoDiscoveryMapEntityPojo.builder()
                .entityType(autoDiscoveryEntity.getEntityType())
                .serviceIdentifiers(autoDiscoveryEntity.getServiceIdentifiers())
                .serviceMappingIdentifiers(autoDiscoveryEntity.getServiceMappingIdentifiers())
                .environment(autoDiscoveryEntity.getEnvironment())
                .accountId(account.getId())
                .build();
    }

    @Override
    @Transactional(rollbackFor = {Exception.class, RuntimeException.class})
    public List<String> process(AutoDiscoveryMapEntityPojo mapEntity) throws DataProcessingException {
        try {
            AutoDiscoveryMapEntityPojo entityPojo = new AutoDiscoveryMapEntityPojo();
            List<String> validEntityList = Arrays.asList(mapEntity.getServiceMappingIdentifiers());
            List<String> invalidJsonObjects = new ArrayList<>();
            Map<String, String> errors;
            errors = validateEntityIdentifiers(mapEntity.getEntityType(), validEntityList);
            entityPojo.setErrorMessages(errors);
            String message = mapEntityToService(validEntityList, entityPojo.getErrorMessages(), mapEntity);
            invalidJsonObjects.add(message);
            for (String valid : validEntityList) {
                if (!(entityPojo.getErrorMessages().get(valid) == null)) {
                    invalidJsonObjects.add(valid);
                }
            }
            return invalidJsonObjects;
        } catch (HealControlCenterException e) {
            throw new DataProcessingException(e.getMessage());
        }
    }

    private String mapEntityToService(List<String> validEntities, Map<String, String> errorMessages, AutoDiscoveryMapEntityPojo entityPojo)
            throws HealControlCenterException {

        Entity entityType = entityPojo.getEntityType();
        int[] controllerId = entityPojo.getServiceIdentifiers();
        Environment environment = entityPojo.getEnvironment();
        int accountId = entityPojo.getAccountId();

        List<String> incomingMappings = new ArrayList<>();

        // change the environment in case entity is host
        if (entityType.equals(Entity.Host)) {
            List<String> ve = new ArrayList<>();
            for (String entity : validEntities) {
                if (!errorMessages.containsKey(entity)) {
                    ve.add(entity);
                }
            }
            try {
                autoDiscoveryDao.setEnvironment(ve, environment);
            } catch (HealControlCenterException e) {
                throw new HealControlCenterException(e.getMessage());
            }
        }

        // Retrieve existing mappings
        List<AutoDiscoveryServiceMappingBean> serviceMappingList = autoDiscoveryDao.getServiceMappingList();
        if (serviceMappingList == null) {
            throw new HealControlCenterException("Exception while fetching existing mappings");
        }

        List<ControllerBean> controllerList = validateServiceIdentifier(controllerId, accountId);
        if (controllerList.isEmpty()) {
            throw new HealControlCenterException("Exception while validating service identifiers.");
        }

        for (ControllerBean controller : controllerList) {
            incomingMappings.add(controller.getName());
        }

        // UNMAPPING OPERATION - if controller id is null then all existing mappings for that entity will be deleted
        if (controllerId == null) {
            String key = validateEditOfHostServices(validEntities, entityType, incomingMappings, processesList, serviceMappingList, controllerList);
            if (key.equals("WRONG-EDIT")) {
                return key;
            }
            deleteServiceMappings(validEntities, entityType, serviceMappingList, processesList, errorMessages);
            return "UNMAPPING";
        }

        String key = validateEditOfHostServices(validEntities, entityType, incomingMappings, processesList, serviceMappingList, controllerList);
        if (key.equals("WRONG-EDIT")) {
            return key;
        }

        List<AutoDiscoveryServiceMappingBean> autoDiscoveryEntityBeanList = createEntityBean(validEntities, entityType, controllerList,
                serviceMappingList, processesList, errorMessages);
        // add valid entities to table
        try {
            autoDiscoveryDao.addServiceMapping(autoDiscoveryEntityBeanList);
        } catch (HealControlCenterException e) {
            throw new HealControlCenterException(e.getMessage());
        }
        log.info("Entity/Entities mapped to service(s) successfully.");

        updateLastUpdatedTime(autoDiscoveryEntityBeanList, entityType);

        // add connections related to these entities to autodisco_discovered_connections table
        int added_conn_to_table = addToDiscoveredConnections(entityType, validEntities);
        if (added_conn_to_table != 1) {
            String msg = "Could not add entity related connections to discovered connections table.";
            log.error(msg);
            throw new HealControlCenterException(msg);
        }
        return "PASS";
    }

    public void updateLastUpdatedTime(List<AutoDiscoveryServiceMappingBean> autoDiscoveryEntityBeanList, Entity entityType) throws HealControlCenterException {
        // set last updated time
        long time = System.currentTimeMillis();
        try {
            if (entityType.equals(Entity.Host)) {
                autoDiscoveryDao.setHostLastUpdatedTime(autoDiscoveryEntityBeanList, time);
                autoDiscoveryDao.setNetworkConnectionsLastUpdatedTime(autoDiscoveryEntityBeanList, time);
            } else if (entityType.equals(Entity.CompInstance)) {
                autoDiscoveryDao.setCompInstanceLastUpdateTime(autoDiscoveryEntityBeanList, time);
            }
        } catch (Exception e) {
            throw new HealControlCenterException(e.getMessage());
        }
    }

    private List<AutoDiscoveryServiceMappingBean> createEntityBean(List<String> validEntities, Entity entityType,
                                                               List<ControllerBean> controllerList, List<AutoDiscoveryServiceMappingBean> serviceMappingList,
                                                               List<AutoDiscoveryProcessBean> processList, Map<String, String> errorMessages) throws HealControlCenterException {

        List<AutoDiscoveryServiceMappingBean> autoDiscoveryEntityBeanList = new ArrayList<>();

        // delete old service mappings if required
        if (validEntities.size() < 2) { // mapped services and discovered connections are not deleted for bulk actions
            // delete previously mapped services and related connections
            deleteServiceMappings(validEntities, entityType, serviceMappingList, processList, errorMessages);
        }

        for (String validEntity : validEntities) {
            if (!errorMessages.containsKey(validEntity)) {
                for (ControllerBean sid : controllerList) {
                    AutoDiscoveryServiceMappingBean autoDiscoveryEntityBean = new AutoDiscoveryServiceMappingBean();
                    autoDiscoveryEntityBean.setServiceIdentifier(sid.getIdentifier());
                    autoDiscoveryEntityBean.setEntityType(entityType);
                    autoDiscoveryEntityBean.setServiceMappingIdentifier(validEntity);
                    autoDiscoveryEntityBeanList.add(autoDiscoveryEntityBean);
                    // if CompInstance is mapped to a service then the host of the CompInstance should also be mapped to the service
                    if (entityType.equals(Entity.CompInstance)) {
                        String hostIdentifier = null;

                        Optional<AutoDiscoveryProcessBean> bean = processList.parallelStream()
                                .filter(x -> x.getProcessIdentifier().equals(validEntity))
                                .findFirst();
                        if(bean.isPresent()) {
                            hostIdentifier = bean.get().getHostIdentifier();
                        }

                        autoDiscoveryEntityBean = new AutoDiscoveryServiceMappingBean();
                        autoDiscoveryEntityBean.setServiceIdentifier(sid.getIdentifier());
                        autoDiscoveryEntityBean.setEntityType(Entity.Host);
                        autoDiscoveryEntityBean.setServiceMappingIdentifier(hostIdentifier);
                        autoDiscoveryEntityBeanList.add(autoDiscoveryEntityBean);
                    }
                }
            }
        }
        return autoDiscoveryEntityBeanList;
    }

    private String validateEditOfHostServices(List<String> validEntities, Entity entityType, List<String> incomingMappings,
                                              List<AutoDiscoveryProcessBean> processList, List<AutoDiscoveryServiceMappingBean> serviceMappingList,
                                              List<ControllerBean> controllerList) {
        // checks if user tries to edit mappings from component instances on hosts page
        if (entityType.equals(Entity.Host)) {
            for (String validEntity : validEntities) {
                List<AutoDiscoveryProcessBean> processes = processList.stream().filter(p -> p.getHostIdentifier().equals(validEntity) && p.getComponentId() != 0).collect(Collectors.toList());

                for (AutoDiscoveryProcessBean p : processes) {
                    List<AutoDiscoveryServiceMappingBean> mappings = serviceMappingList.stream().filter(m -> m.getServiceMappingIdentifier().equals(p.getProcessIdentifier()))
                            .distinct().collect(Collectors.toList());

                    List<String> compInstanceMappings = new ArrayList<>();

                    for (AutoDiscoveryServiceMappingBean map : mappings) {
                        compInstanceMappings.add(map.getServiceIdentifier());
                        // if a component instance is already mapped to a service then host also gets automatically mapped to the same service,
                        // so we can omit incoming mappings already present to host
                        if (controllerList != null) {
                            controllerList.removeIf(c -> c.getName().equals(map.getServiceIdentifier()));
                        }
                    }

                    if (!incomingMappings.containsAll(compInstanceMappings)) {
                        log.error("Cannot edit component instance service mappings here.");
                        return "WRONG-EDIT";
                    }
                }
            }
        }
        return "PASS";
    }

    public Map<String, String> validateEntityIdentifiers(Entity entityType, List<String> validHostIds) throws HealControlCenterException {
        Map<String, String> messages = new HashMap<>();
        // checks if host or component instance identifiers are present in the DB
        AutoDiscoveryHostBean hostIdentifiers;
        AutoDiscoveryProcessBean processIdentifiers;
        List<AutoDiscoveryHostBean> hostsList = autoDiscoveryDao.getHostList(validHostIds);
        processesList = autoDiscoveryDao.getProcessList();

        if (entityType.equals(Entity.Host)) {
            if(hostsList.isEmpty()) {
                throw new HealControlCenterException("Error while fetching hosts list.");
            }

            for (String id : validHostIds) {
                hostIdentifiers = hostsList.parallelStream()
                        .filter(identifier -> identifier.getHostIdentifier().equals(id))
                        .findAny().orElse(null);

                if (hostIdentifiers == null) {
                    String msg = String.format("Host identifier [%s] unavailable in autodisco_host table", id);
                    log.error(msg);
                    messages.put(id, msg);
                }
            }
        } else if (entityType.equals(Entity.CompInstance)) {
            if(processesList.isEmpty()) {
                throw new HealControlCenterException("Error while fetching known processes list.");
            }

            for (String id : validHostIds) {
                processIdentifiers = processesList.stream()
                        .filter(identifier -> identifier.getProcessIdentifier().equals(id))
                        .findAny().orElse(null);

                if (processIdentifiers == null) {
                    String msg = "Process identifier not found unavailable in autodisco_process table. Identifier - " + id;
                    log.error(msg);
                    messages.put(id, msg);
                }
            }
        }

        return messages;
    }

    public List<ControllerBean> validateServiceIdentifier(int[] id, Integer accountId) throws HealControlCenterException {
        List<ControllerBean> controllerList;
        try {
            controllerList = controllerDao.getControllerList(accountId);
        } catch (HealControlCenterException e) {
            throw new HealControlCenterException(e.getMessage());
        }
        List<ControllerBean> clist = new ArrayList<>();

        for (int sid : id) {
            ControllerBean controller = controllerList.stream()
                    .filter(control -> control.getId() == sid)
                    .findFirst().orElse(null);
            if (controller == null) {
                String msg = "Service id not found in controller table. Id: " + sid;
                log.error(msg);
                throw new HealControlCenterException(msg);
            }
            clist.add(controller);
        }
        return clist;
    }

    public void deleteServiceMappings(List<String> validEntities, Entity entityType, List<AutoDiscoveryServiceMappingBean> serviceMappingList,
                                      List<AutoDiscoveryProcessBean> processList, Map<String, String> errorMessages) throws HealControlCenterException {

        for (String validEntity : validEntities) {
            if (!serviceMappingList.isEmpty()) {
                List<String> mappings = new ArrayList<>();

                if (errorMessages.get(validEntity) == null) {
                    if (entityType.equals(Entity.Host)) {

                        List<AutoDiscoveryProcessBean> existingCompInstanceList = processList.stream().filter(x -> x.getHostIdentifier().equals(validEntity))
                                .collect(Collectors.toList());
                        List<AutoDiscoveryServiceMappingBean> existingHostMappings = serviceMappingList.stream()
                                .filter(x -> x.getServiceMappingIdentifier().equals(validEntity)).collect(Collectors.toList());
                        // CompInstance's list of a host

                        if (!existingHostMappings.isEmpty()) {
                            for (AutoDiscoveryProcessBean instance : existingCompInstanceList) {
                                // we will not delete CompInstance mappings if a host is mapped to new service
                                List<AutoDiscoveryServiceMappingBean> existingCompMappings = serviceMappingList.stream()
                                        .filter(x -> x.getServiceMappingIdentifier()
                                                .equals(instance.getProcessIdentifier())).collect(Collectors.toList());
                                for (AutoDiscoveryServiceMappingBean mapping : existingCompMappings) {
                                    if (mappings.contains(mapping.getServiceIdentifier())) continue;
                                    mappings.add(mapping.getServiceIdentifier());
                                }
                            }
                            try {
                                autoDiscoveryDao.deleteExistingMappingsAndConnections(validEntity, mappings);
                            } catch (HealControlCenterException e) {
                                throw new HealControlCenterException(e.getMessage());
                            }
                        }
                    } else if (entityType.equals(Entity.CompInstance)) {
                        String hostIdentifier = null;
                        Optional<AutoDiscoveryProcessBean> bean = processList.stream()
                                .filter(x -> x.getProcessIdentifier().equals(validEntity))
                                .findFirst();
                        if(bean.isPresent()) {
                            hostIdentifier = bean.get().getHostIdentifier();
                        }
                        /*
                          fetch existing mappings for the compinstance and delete the same mapped to the host since
                          when a compinstance is mapped to service we also create a mapping from the host to that service
                        */
                        List<AutoDiscoveryServiceMappingBean> existingCompMappings = serviceMappingList.stream().filter(x -> x.getServiceMappingIdentifier().equals(validEntity)).collect(Collectors.toList());
                        if (!existingCompMappings.isEmpty()) {
                            for (AutoDiscoveryServiceMappingBean mapping : existingCompMappings) {
                                if (mappings.contains(mapping.getServiceIdentifier())) continue;
                                mappings.add(mapping.getServiceIdentifier());
                            }
                            try {
                                autoDiscoveryDao.deleteExistingMappingsAndConnections(validEntity, mappings, hostIdentifier);
                            } catch (HealControlCenterException e) {
                                throw new HealControlCenterException(e.getMessage());
                            }
                        }
                    }
                }
            }
        }
    }

    public int addToDiscoveredConnections(Entity entityType, List<String> validEntities) throws HealControlCenterException {
        List<AutoDiscoveryNetworkConnectionsBean> adNetworkConnectionsList = autoDiscoveryDao.getNetworkConnections();
        if (adNetworkConnectionsList.isEmpty()) {
            String CONN_LIST_EMPTY = "No network connections found. List is empty.";
            log.error(CONN_LIST_EMPTY);}

        List<EndpointBean> adEndpointsList = autoDiscoveryDao.getEndpoints();
        if (adEndpointsList.isEmpty()) {
            String ENDPOINTS_LIST_EMPTY = "No endpoints found. List is empty.";
            log.error(ENDPOINTS_LIST_EMPTY);}

        List<NetworkInterfaceBean> adInterfaceList = autoDiscoveryDao.getNetworkInterfacesList();
        if (adInterfaceList.isEmpty()) {
            String INTERFACES_LIST_EMPTY = "No network interfaces found. List is empty.";
            log.error(INTERFACES_LIST_EMPTY);}

        List<AutoDiscoveryServiceMappingBean> adServiceMappingList = autoDiscoveryDao.getServiceMappingList();
        if (adServiceMappingList.isEmpty()) {
            String MAPPING_LIST_EMPTY = "No service mappings found. List is empty.";
            log.error(MAPPING_LIST_EMPTY);}

        if (entityType.equals(Entity.Host)) {
            List<AutoDiscoveryNetworkConnectionsBean> connections = new ArrayList<>();

            for (String hid : validEntities) {
                List<AutoDiscoveryNetworkConnectionsBean> conns = adNetworkConnectionsList.stream()
                        .filter(con -> con.getHostIdentifier().equals(hid))
                        .collect(Collectors.toList());
                connections.addAll(conns);
            }

            for (AutoDiscoveryNetworkConnectionsBean conn : connections) {
                AutoDiscoveryDiscoveredConnectionsPojo alignedByDirection = byDirection(conn);
                EndpointBean endpointIP = adEndpointsList.stream()
                        .filter(IPBean -> (IPBean.getIpAddress().equals(alignedByDirection.getDestinationIp()) && (IPBean.getPortNo() == alignedByDirection.getDestinationPort())))
                        .findFirst().orElse(null);
                if (endpointIP == null) {
                    String msg = "Endpoint (destination) not found to add connection. IP- " + alignedByDirection.getDestinationIp() +
                            ", Port- " + alignedByDirection.getDestinationPort();
                    log.warn(msg);
                    continue;
                }

                NetworkInterfaceBean interfaceIP = adInterfaceList.stream()
                        .filter(IPBean -> (IPBean.getInterfaceIP().equals(alignedByDirection.getSourceIp())))
                        .findFirst().orElse(null);
                if (interfaceIP == null) {
                    String msg = "Network interface (source) not found to add connection. IP- " + alignedByDirection.getSourceIp();
                    log.warn(msg);
                    continue;
                }

                List<AutoDiscoveryServiceMappingBean> sourceIdentifiersList = adServiceMappingList.stream()
                        .filter(mappingBean -> (mappingBean.getServiceMappingIdentifier().equals(interfaceIP.getHostIdentifier())))
                        .collect(Collectors.toList());
                if (sourceIdentifiersList.isEmpty()) {
                    String msg = "Service mapping not found for source to add connection. Identifier- " + interfaceIP.getHostIdentifier();
                    log.warn(msg);
                    continue;
                }

                List<AutoDiscoveryServiceMappingBean> destinationIdentifiersList = adServiceMappingList.stream()
                        .filter(mappingBean -> (mappingBean.getServiceMappingIdentifier().equals(endpointIP.getHostIdentifier())))
                        .collect(Collectors.toList());
                if (destinationIdentifiersList.isEmpty()) {
                    String msg = "Service mapping not found for destination to add connection. Identifier- " + endpointIP.getHostIdentifier();
                    log.warn(msg);
                    continue;
                }
                allConnections(conn.getHostIdentifier(), alignedByDirection, sourceIdentifiersList, destinationIdentifiersList);
            }
        } else if (entityType.equals(Entity.CompInstance)) {
            List<AutoDiscoveryNetworkConnectionsBean> connections = new ArrayList<>();
            List<String> validProcess = new ArrayList<>();

            for (String cid : validEntities) {
                processesList.stream().filter(process -> process.getProcessIdentifier().equals(cid))
                        .findFirst().ifPresent(pro -> validProcess.add(pro.getHostIdentifier()));
            }

            for (String cid : validProcess) {
                List<AutoDiscoveryNetworkConnectionsBean> conns = adNetworkConnectionsList.stream()
                        .filter(con -> con.getHostIdentifier().equals(cid))
                        .collect(Collectors.toList());
                connections.addAll(conns);
            }

            for (AutoDiscoveryNetworkConnectionsBean conn : connections) {
                AutoDiscoveryDiscoveredConnectionsPojo alignedByDirection = byDirection(conn);
                EndpointBean endpointIP = adEndpointsList.stream()
                        .filter(IPBean -> (IPBean.getIpAddress().equals(alignedByDirection.getDestinationIp()) && (IPBean.getPortNo() == alignedByDirection.getDestinationPort())))
                        .findFirst().orElse(null);
                if (endpointIP == null) {
                    String msg = "Endpoint (destination) not found to add connection. IP- " + alignedByDirection.getDestinationIp() +
                            ", Port- " + alignedByDirection.getDestinationPort();
                    log.warn(msg);
                    continue;
                }

                NetworkInterfaceBean interfaceIP = adInterfaceList.stream()
                        .filter(IPBean -> (IPBean.getInterfaceIP().equals(alignedByDirection.getSourceIp())))
                        .findFirst().orElse(null);
                if (interfaceIP == null) {
                    String msg = "Network interface (source) not found to add connection. IP- " + alignedByDirection.getSourceIp();
                    log.warn(msg);
                    continue;
                }

                List<AutoDiscoveryServiceMappingBean> sourceIdentifiersList = adServiceMappingList.stream()
                        .filter(mappingBean -> (mappingBean.getServiceMappingIdentifier().equals(interfaceIP.getHostIdentifier())))
                        .collect(Collectors.toList());
                if (sourceIdentifiersList.isEmpty()) {
                    String msg = "Service mapping not found for source to add connection. Identifier- " + interfaceIP.getHostIdentifier();
                    log.warn(msg);
                    continue;
                }

                List<AutoDiscoveryServiceMappingBean> destinationIdentifiersList = adServiceMappingList.stream()
                        .filter(mappingBean -> (mappingBean.getServiceMappingIdentifier().equals(endpointIP.getHostIdentifier())))
                        .collect(Collectors.toList());
                if (destinationIdentifiersList.isEmpty()) {
                    String msg = "Service mapping not found for destination to add connection. Identifier- " + endpointIP.getHostIdentifier();
                    log.warn(msg);
                    continue;
                }
                allConnections(conn.getHostIdentifier(), alignedByDirection, sourceIdentifiersList, destinationIdentifiersList);
            }
        }
        return 1;
    }

    public void allConnections(String hostIdentifier, AutoDiscoveryDiscoveredConnectionsPojo adConnectionsPojo,
                               List<AutoDiscoveryServiceMappingBean> sourceIdentifiersList,
                               List<AutoDiscoveryServiceMappingBean> destinationIdentifiersList) throws HealControlCenterException {
        /*
          Creates a cartesian product of source identifier and destination identifier
          in case there is more than one mapping present for a particular host in autodisco_service_mapping
        */
        List<AutoDiscoveryDiscoveredConnectionsBean> discoveredConnectionsList = new ArrayList<>();
        List<AutoDiscoveryDiscoveredConnectionsBean> existingDiscoveredConnections = autoDiscoveryDao.getDiscoveredConnectionsList();

        for (int i = 0; i < sourceIdentifiersList.size(); i++) {
            for (int j = 0; j < destinationIdentifiersList.size(); j++) {

                int finalI = i;
                int finalJ = j;
                // Check if the source identifier -> destination identifier is already present
                // in autodisco_discovered_connections table
                if (existingDiscoveredConnections.stream()
                        .anyMatch(discon -> (discon.getSourceIdentifier().equals(sourceIdentifiersList.get(finalI).getServiceIdentifier())
                                && discon.getDestinationIdentifier().equals(destinationIdentifiersList.get(finalJ).getServiceIdentifier())))) {
                    continue;
                }
                if (sourceIdentifiersList.get(i).getServiceIdentifier().equals(destinationIdentifiersList.get(j).getServiceIdentifier())) {
                    continue;
                }
                AutoDiscoveryDiscoveredConnectionsBean discoveredConnections = new AutoDiscoveryDiscoveredConnectionsBean();
                discoveredConnections.setSourceIdentifier(sourceIdentifiersList.get(i).getServiceIdentifier());
                discoveredConnections.setHostIdentifier(hostIdentifier);
                discoveredConnections.setDestinationIdentifier(destinationIdentifiersList.get(j).getServiceIdentifier());
                discoveredConnections.setLastUpdatedTime(adConnectionsPojo.getLastUpdatedTime());
                discoveredConnections.setIsDiscovery(1); // 1 for auto discovery & 0 for manual
                discoveredConnectionsList.add(discoveredConnections);
            }
        }

        // adds all the combinations to the autodisco_discovered_connections table
        if (discoveredConnectionsList.isEmpty()) {
            log.warn("No connections found related to entity.");
        } else {
            try {
                autoDiscoveryDao.addDiscoveredConnection(discoveredConnectionsList);
            } catch (HealControlCenterException e) {
                throw new HealControlCenterException(e.getMessage());
            }
        }
    }

    public AutoDiscoveryDiscoveredConnectionsPojo byDirection(AutoDiscoveryNetworkConnectionsBean adConnection) throws HealControlCenterException {
        /*
          Sets source and destination IPs by direction of the connection [INCOMING_CONNECTION, OUTGOING_CONNECTION]
          If direction == OUTGOING_CONNECTION
              Source_identifier = Service mapped to local ip / port
              dest_identifier = Service mapped to remote ip / port
          else
              Source_identifier = Service mapped to remote ip / port
              dest_identifier = Service mapped to local ip / port
        */
        Direction direction = adConnection.getDirection();
        AutoDiscoveryDiscoveredConnectionsPojo adConnectionsPojo = new AutoDiscoveryDiscoveredConnectionsPojo();
        if (direction.equals(Direction.OUTGOING_CONNECTION)) {
            adConnectionsPojo.setSourceIp(adConnection.getLocalIP());
            adConnectionsPojo.setSourcePort(adConnection.getLocalPort());
            adConnectionsPojo.setDestinationIp(adConnection.getRemoteIP());
            adConnectionsPojo.setDestinationPort(adConnection.getRemotePort());
            adConnectionsPojo.setLastUpdatedTime(adConnection.getLastUpdatedTime());
            return adConnectionsPojo;

        } else if (direction.equals(Direction.INCOMING_CONNECTION)) {
            adConnectionsPojo.setSourceIp(adConnection.getRemoteIP());
            adConnectionsPojo.setSourcePort(adConnection.getRemotePort());
            adConnectionsPojo.setDestinationIp(adConnection.getLocalIP());
            adConnectionsPojo.setDestinationPort(adConnection.getLocalPort());
            adConnectionsPojo.setLastUpdatedTime(adConnection.getLastUpdatedTime());
            return adConnectionsPojo;

        } else {
            String msg = "Please check direction of connection in autodisco_network_connection table. " +
                    "Local IP- " + adConnection.getLocalIP() + ", Local port- " + adConnection.getLocalPort() +
                    ", Remote IP- " + adConnection.getRemoteIP() + ", Remote port- " + adConnection.getRemotePort();
            log.error(msg);
            throw new HealControlCenterException(msg);
        }
    }
}
