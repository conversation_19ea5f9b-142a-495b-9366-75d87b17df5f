package com.heal.controlcenter.businesslogic;

import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.dao.mysql.MasterDataDao;
import com.heal.controlcenter.dao.mysql.NotificationsDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.SMSDetailsPojo;
import com.heal.controlcenter.pojo.SMSParameterPojo;
import com.heal.controlcenter.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
@Component
public class PostSMSConfigurationsBL implements BusinessLogic<SMSDetailsPojo, UtilityBean<SMSDetailsPojo>, Object> {

    @Autowired
    CommonUtils commonUtils;
    @Autowired
    AccountsDao accountsDao;
    @Autowired
    NotificationsDao notificationsDao;
    @Autowired
    MasterDataDao masterDataDao;
    @Autowired
    DateTimeUtil dateTimeUtil;
    @Autowired
    ClientValidationUtils clientValidationUtils;
    @Autowired
    ServerValidationUtils serverValidationUtils;

    private ViewTypesBean protocolTypes;
    private List<ViewTypesBean> parameterTypes;
    private static final String ADD_LIST = "AddList";

    @Override
    public UtilityBean<SMSDetailsPojo> clientValidation(SMSDetailsPojo smsDetails, String... requestParams) throws ClientException {

        String authKey = requestParams[0];
        clientValidationUtils.authKeyValidation(authKey);

        String accountIdentifier = requestParams[1];
        clientValidationUtils.accountIdentifierValidation(accountIdentifier);

        Map<String, String> error = smsDetails.validate();
        if (!error.isEmpty()) {
            String err = error.toString();
            log.error(err);
            throw new ClientException(err);
        }

        if (smsDetails.getParameters().stream().anyMatch(obj -> !obj.getAction().equalsIgnoreCase("add"))) {
            log.error("SMS parameters with action other than ‘add’ are not allowed.");
            throw new ClientException("SMS parameters with action other than ‘add’ are not allowed.");
        }

        HashMap<String, String> requestParamsMap = CommonUtils.buildRequestParams(authKey, accountIdentifier);

        return UtilityBean.<SMSDetailsPojo>builder()
                .requestParams(requestParamsMap)
                .pojoObject(smsDetails)
                .build();
    }

    @Override
    public UtilityBean<SMSDetailsPojo> serverValidation(UtilityBean<SMSDetailsPojo> utilityBean) throws ServerException {

        String authKey = utilityBean.getRequestParams().get(Constants.AUTH_KEY);
        serverValidationUtils.authKeyValidation(authKey);

        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
        AccountBean account = accountsDao.getAccountByIdentifier(accountIdentifier);
        if (account == null) {
            log.error(UIMessages.ACCOUNT_IDENTIFIER_INVALID);
            throw new ServerException(UIMessages.ACCOUNT_IDENTIFIER_INVALID);
        }
        utilityBean.setAccount(account);

        SMSDetailsBean smsDetailsBean = notificationsDao.getSMSDetails(account.getId());
        if (smsDetailsBean != null) {
            log.error("SMS settings are already available for accountId [{}].", account.getId());
            throw new ServerException("SMS settings already available");
        }

        try {
            validationsForSMSDetailsPojo(utilityBean);
        } catch (ControlCenterException e) {
            throw new ServerException(e.getMessage());
        }

        return utilityBean;
    }

    @Override
    @Transactional(rollbackFor = {Exception.class, RuntimeException.class})
    public Object process(UtilityBean<SMSDetailsPojo> smsDetailsPojo) throws DataProcessingException {
        int smsDetailsId;
        Date time = dateTimeUtil.getCurrentTimestampInGMT();
        DateFormat dateFormat = new SimpleDateFormat(Constants.DATE_TIME);
        String createdTime = dateFormat.format(time);

        SMSDetailsPojo smsDetails = smsDetailsPojo.getPojoObject();

        SMSDetailsBean smsDetailsBean = SMSDetailsBean.builder()
                .accountId(smsDetailsPojo.getAccount().getId())
                .address(smsDetails.getAddress())
                .countryCode(smsDetails.getCountryCode())
                .httpMethod(smsDetails.getHttpMethod())
                .httpRelativeUrl(smsDetails.getHttpRelativeUrl())
                .port(smsDetails.getPort())
                .postData(smsDetails.getHttpMethod().equalsIgnoreCase("POST") ? smsDetails.getPostData() : null)
                .postDataFlag(smsDetails.getHttpMethod().equalsIgnoreCase("POST") ? 1 : 0)
                .protocolId(protocolTypes.getSubTypeId())
                .lastModifiedBy(smsDetailsPojo.getRequestParams().get(Constants.USER_ID_KEY))
                .createdTime(createdTime)
                .updatedTime(createdTime)
                .status(1)
                .isMultiRequest(smsDetails.getIsMultiRequest())
                .build();

        try {
            smsDetailsId = notificationsDao.addSMSDetails(smsDetailsBean);
        } catch (ControlCenterException e) {
            log.error("Unable to add SMS details to account. Details: ", e);
            throw new DataProcessingException(e.getMessage());
        }
        log.info("SMS detail id [{}] for account id [{}]", smsDetailsId, smsDetailsPojo.getAccount().getId());

        Map<String, List<SMSParameterBean>> smsParametersBeanMap = createSMSParameterList(smsDetailsId, smsDetails.getParameters(), smsDetailsPojo.getRequestParams().get(Constants.USER_ID_KEY), createdTime);

        List<SMSParameterBean> smsParametersBeansAdd = smsParametersBeanMap.getOrDefault(ADD_LIST, new ArrayList<>());
        if (!smsParametersBeansAdd.isEmpty()) {
            try {
                notificationsDao.addSMSParameter(smsParametersBeansAdd);
            } catch (ControlCenterException e) {
                log.error("Unable to add SMS parameters. Details: ", e);
                throw new DataProcessingException(e.getMessage());
            }
        }

        return null;
    }

    private Map<String, List<SMSParameterBean>> createSMSParameterList(int smsDetailsId, List<SMSParameterPojo> smsParameterList,
                                                                       String userId, String time) throws DataProcessingException {
        Map<String, List<SMSParameterBean>> result = new HashMap<>();
        List<SMSParameterBean> addSmsParamList = new ArrayList<>();

        for (SMSParameterPojo smsParameter : smsParameterList) {
            ViewTypesBean parameterType = parameterTypes.parallelStream().filter(type -> type.getSubTypeName().equals(smsParameter.getParameterType()))
                    .findAny().orElse(null);
            if (parameterType == null) {
                log.error("Parameter type details unavailable for parameter type [{}].", smsParameter.getParameterType());
                throw new DataProcessingException("Parameter type details unavailable.");
            }

            SMSParameterBean smsBean = SMSParameterBean.builder()
                    .smsDetailsId(smsDetailsId)
                    .parameterName(smsParameter.getParameterName())
                    .parameterValue(smsParameter.getParameterValue())
                    .parameterTypeId(parameterType.getSubTypeId())
                    .createdTime(time)
                    .updatedTime(time)
                    .lastModifiedBy(userId)
                    .isPlaceholder(smsParameter.getIsPlaceholder().equals(Boolean.TRUE) ? 1 : 0)
                    .build();

            if (smsParameter.getAction().equalsIgnoreCase(Constants.SMS_ACTION_ADD)) {
                addSmsParamList.add(smsBean);
            }
        }
        result.put(ADD_LIST, addSmsParamList);

        return result;
    }

    private void validationsForSMSDetailsPojo(UtilityBean<SMSDetailsPojo> utilityBean) throws ServerException, ControlCenterException {
        SMSDetailsPojo smsDetails = utilityBean.getPojoObject();

        protocolTypes = masterDataDao.getViewTypesFromMstTypeAndSubTypeName(Constants.SMS_PROTOCOLS, smsDetails.getProtocolName());
        if (protocolTypes == null) {
            log.error("SMS protocol details unavailable for protocol name [{}].", smsDetails.getProtocolName());
            throw new ServerException("Error occurred while getting SMS protocol details.");
        }

        if (protocolTypes.getSubTypeName().equalsIgnoreCase("HTTP")) {
            ViewTypesBean httpMethodTypes = masterDataDao.getViewTypesFromMstTypeAndSubTypeName(Constants.SMS_HTTP_METHODS, smsDetails.getHttpMethod());
            if (httpMethodTypes == null) {
                log.error("HTTP method type details unavailable for http method [{}]", smsDetails.getHttpMethod());
                throw new ServerException("Error occurred while getting HTTP method details.");
            }
            if (httpMethodTypes.getSubTypeName().equalsIgnoreCase("GET")) {
                smsDetails.setPostData("");
            }
        } else if (protocolTypes.getSubTypeName().equalsIgnoreCase("TCP")) {
            smsDetails.setPostData("");
            smsDetails.setHttpMethod("");
            smsDetails.setHttpRelativeUrl("");
            smsDetails.setIsMultiRequest(0);
        }

        validateSMSParameters(smsDetails.getParameters());
    }

    private void validateSMSParameters(List<SMSParameterPojo> smsParameters) throws ServerException {
        List<ViewTypesBean> placeholdersTypes = masterDataDao.getMstTypeByTypeName(Constants.SMS_PLACEHOLDERS);
        parameterTypes = masterDataDao.getMstTypeByTypeName(Constants.SMS_PARAMETER_TYPE_NAME);

        for (SMSParameterPojo smsParameter : smsParameters) {
            ViewTypesBean parameterType = parameterTypes.parallelStream()
                    .filter(type -> type.getSubTypeName().equals(smsParameter.getParameterType())).findAny().orElse(null);
            if (parameterType == null) {
                log.error("Parameter type details unavailable for parameter type [{}].", smsParameter.getParameterType());
                throw new ServerException("Error occurred while getting SMS parameter type.");
            }

            if (smsParameter.getIsPlaceholder().equals(Boolean.TRUE)) {
                Optional<ViewTypesBean> parameterValueType = placeholdersTypes.parallelStream()
                        .filter(p -> smsParameter.getParameterValue().contains(p.getSubTypeName()))
                        .findAny();

                if (!parameterValueType.isPresent()) {
                    log.error("Sub type is not found for the given parameter value type [{}].", smsParameter.getParameterName());
                    throw new ServerException("Sub type details not found.");
                }
            }
        }
    }
}
