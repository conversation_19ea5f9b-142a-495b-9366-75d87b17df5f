package com.heal.controlcenter.businesslogic;

import com.heal.configuration.pojos.Account;
import com.heal.controlcenter.beans.SMSDetailsBean;
import com.heal.controlcenter.beans.SMSParameterBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.dao.mysql.MasterDataDao;
import com.heal.controlcenter.dao.mysql.NotificationsDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.SMSDetailsPojo;
import com.heal.controlcenter.pojo.SMSParameterPojo;
import com.heal.controlcenter.util.ClientValidationUtils;
import com.heal.controlcenter.util.CommonUtils;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.ServerValidationUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class GetSMSConfigurationsBL implements BusinessLogic<Object, Integer, SMSDetailsPojo> {

    @Autowired
    AccountsDao accountDao;
    @Autowired
    CommonUtils commonUtils;
    @Autowired
    MasterDataDao masterDataDao;
    @Autowired
    NotificationsDao notificationsDao;
    @Autowired
    ClientValidationUtils clientValidationUtils;
    @Autowired
    ServerValidationUtils serverValidationUtils;

    @Override
    public UtilityBean<Object> clientValidation(Object requestBody, String... requestParams) throws ClientException {
        String authKey = requestParams[0];
        clientValidationUtils.authKeyValidation(authKey);

        String accountIdentifier = requestParams[1];
        clientValidationUtils.accountIdentifierValidation(accountIdentifier);

        HashMap<String, String> requestParamsMap = CommonUtils.buildRequestParams(authKey, accountIdentifier);

        return UtilityBean.builder()
                .requestParams(requestParamsMap)
                .build();
    }

    @Override
    public Integer serverValidation(UtilityBean<Object> utilityBean) throws ServerException {
        String authKey = utilityBean.getRequestParams().get(Constants.AUTH_KEY);
        serverValidationUtils.authKeyValidation(authKey);

        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
        Account account = serverValidationUtils.accountValidation(accountIdentifier);

        return account.getId();
    }

    @Override
    public SMSDetailsPojo process(Integer accountId) throws DataProcessingException {
        SMSDetailsBean smsDetails;
        List<SMSParameterBean> smsParametersList;
        try {
            smsDetails = notificationsDao.getSMSDetails(accountId);
            if (smsDetails == null) {
                log.info("SMS Configurations not found for accountId [{}].", accountId);
                return null;
            }
            smsParametersList = notificationsDao.getSMSParameters(smsDetails.getId());
        } catch (HealControlCenterException e) {
            throw new DataProcessingException(e.getMessage());
        }
        if (smsParametersList.isEmpty()) {
            log.info("SMS parameters not found for SMS details id [{}].", smsDetails.getId());
            return null;
        }

        List<SMSParameterPojo> smsParameters = smsParametersList.stream().map(parameter -> SMSParameterPojo.builder()
                        .parameterId(parameter.getId())
                        .parameterName(parameter.getParameterName())
                        .parameterValue(parameter.getParameterValue())
                        .parameterType(masterDataDao.getMstSubTypeBySubTypeId(parameter.getParameterTypeId()).getSubTypeName())
                        .isPlaceholder(parameter.getIsPlaceholder() == 1)
                        .build())
                .collect(Collectors.toList());

        return SMSDetailsPojo.builder()
                .address(smsDetails.getAddress())
                .id(smsDetails.getId())
                .countryCode(smsDetails.getCountryCode())
                .port(smsDetails.getPort())
                .protocolName(masterDataDao.getMstSubTypeBySubTypeId(smsDetails.getProtocolId()).getSubTypeName())
                .httpMethod(smsDetails.getHttpMethod())
                .httpRelativeUrl(smsDetails.getHttpRelativeUrl())
                .isMultiRequest(smsDetails.getIsMultiRequest())
                .postData(smsDetails.getPostData())
                .parameters(smsParameters)
                .build();
    }
}
