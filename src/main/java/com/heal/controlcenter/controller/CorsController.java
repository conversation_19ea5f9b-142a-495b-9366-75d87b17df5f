package com.heal.controlcenter.controller;

import com.heal.controlcenter.util.JsonFileParser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * Controller to handle CORS preflight OPTIONS requests.
 * This ensures that all endpoints properly respond to preflight requests
 * with appropriate CORS headers.
 */
@Slf4j
@RestController
public class CorsController {

    @Autowired
    JsonFileParser headersParser;

    /**
     * Handle all OPTIONS requests (CORS preflight) for any endpoint.
     * This is a catch-all handler that responds to preflight requests
     * with appropriate CORS headers.
     */
    @RequestMapping(value = "/**", method = RequestMethod.OPTIONS)
    public ResponseEntity<Void> handleOptions() {
        log.debug("Handling CORS preflight OPTIONS request");
        return ResponseEntity
                .status(HttpStatus.OK)
                .headers(headersParser.loadHeaderConfiguration())
                .build();
    }
}
