package com.heal.controlcenter.businesslogic;

import com.appnomic.appsone.common.util.StringUtils;
import com.heal.configuration.pojos.*;
import com.heal.controlcenter.aop.LogExecutionAnnotation;
import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.dao.mysql.AccountServiceDao;
import com.heal.controlcenter.dao.mysql.ControllerDao;
import com.heal.controlcenter.dao.mysql.MasterDataDao;
import com.heal.controlcenter.dao.redis.AccountRepo;
import com.heal.controlcenter.dao.redis.ApplicationRepo;
import com.heal.controlcenter.dao.redis.MasterDataRepo;
import com.heal.controlcenter.dao.redis.ServiceRepo;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.RulesHelperPojo;
import com.heal.controlcenter.pojo.ServicePojo;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.DateTimeUtil;
import com.heal.controlcenter.util.ServerValidationUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PutAccountServicesBL implements BusinessLogic<List<ServicePojo>, UtilityBean<List<ServiceBean>>, List<IdPojo>> {

    @Autowired
    private AccountServiceDao accountServiceDao;
    @Autowired
    private ControllerDao controllerDao;
    @Autowired
    private ServerValidationUtils serverValidationUtils;
    @Autowired
    private ControllerBL controllerBL;
    @Autowired
    private TagMappingBL tagMappingBL;
    @Autowired
    private MasterDataDao masterDataDao;
    @Autowired
    private ServiceRepo serviceRepo;
    @Autowired
    private AccountRepo accountRepo;
    @Autowired
    private MasterDataRepo masterDataRepo;
    @Autowired
    private ApplicationRepo applicationRepo;


    /**
     * Validates the client request for updating account services.
     * Checks for null/empty payload and required identifiers.
     * @param servicePojos List of service payloads
     * @param requestParams Request parameters (account identifier)
     * @return UtilityBean containing validated request data
     * @throws ClientException if validation fails
     */
    @Override
    @LogExecutionAnnotation
    public UtilityBean<List<ServicePojo>> clientValidation(List<ServicePojo> servicePojos, String... requestParams) throws ClientException {
        String accountIdentifier = requestParams[0];
        if (servicePojos == null || servicePojos.isEmpty()) {
            throw new ClientException("Service payload cannot be null or empty");
        }
        for (ServicePojo servicePojo : servicePojos) {
            if (servicePojo.getIdentifier() == null || servicePojo.getIdentifier().isEmpty()) {
                throw new ClientException("Service identifier cannot be null or empty in the payload");
            }
        }
        if (accountIdentifier == null || accountIdentifier.isEmpty()) {
            throw new ClientException("Account identifier cannot be null or empty");
        }
        // Add more validations as needed
        HashMap<String, String> requestParamsMap = new HashMap<>();
        requestParamsMap.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier);
        return UtilityBean.<List<ServicePojo>>builder()
                .requestParams(requestParamsMap)
                .pojoObject(servicePojos)
                .metadata(new HashMap<>())
                .build();
    }

    /**
     * Performs server-side validation for updating account services.
     * Validates account existence and service existence for update.
     * @param utilityBean UtilityBean containing client-validated data
     * @return UtilityBean with ServiceBean objects for processing
     * @throws ServerException if validation fails
     */
    @Override
    @LogExecutionAnnotation
    public UtilityBean<List<ServiceBean>> serverValidation(UtilityBean<List<ServicePojo>> utilityBean) throws ServerException {
        try {
            String userId = (String) utilityBean.getMetadata().get(Constants.USER_ID_KEY);
            String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
            List<ServicePojo> servicePojos = utilityBean.getPojoObject();
            List<ServiceBean> serviceBeans = new ArrayList<>();

            // Validate account using AddAccountServiceBL logic
            Account account = serverValidationUtils.accountValidation(accountIdentifier);
            if (account == null) {
                throw new ServerException("Account with identifier '" + accountIdentifier + "' does not exist.");
            }
            int accountId = account.getId();
            for (ServicePojo servicePojo : servicePojos) {
                // Validate service existence for update
                ControllerBean existingController = controllerDao.getControllerByIdentifierOrName(servicePojo.getIdentifier(), null);
                if (existingController == null || existingController.getAccountId() != accountId) {
                    throw new ServerException("Service with identifier '" + servicePojo.getIdentifier() + "' does not exist for this account.");
                }

                // Build ServiceBean for update
                String type = servicePojo.getType() != null ? servicePojo.getType() : existingController.getType();
                String layer = servicePojo.getLayer() != null ? servicePojo.getLayer().trim() : existingController.getLayer();
                List<String> appIdentifiers = servicePojo.getAppIdentifiers();
                List<Integer> appIds = new java.util.ArrayList<>();
                if (appIdentifiers != null) {
                    for (String appIdentifier : appIdentifiers) {
                        ControllerBean appController = controllerDao.getControllerByIdentifierOrName(appIdentifier.trim(), null);
                        if (appController == null) {
                            throw new ServerException("App Identifier '" + appIdentifier + "' does not exist.");
                        }
                        appIds.add(appController.getId());
                    }
                }

                String identifier = existingController.getIdentifier();
                String envSuffix = "";
                if (identifier != null) {
                    if (identifier.endsWith("_DC")) {
                        envSuffix = "_DC";
                    } else if (identifier.endsWith("_DR")) {
                        envSuffix = "_DR";
                    }
                }

                String suffixedName = servicePojo.getName().trim() + envSuffix;

                String linkedIdentifier = null;
                String mappedIds = null;
                if (servicePojo.getLinkedEnvironment() != null) {
                    linkedIdentifier = servicePojo.getLinkedEnvironment().getAccount();
                    mappedIds = servicePojo.getLinkedEnvironment().getMappedServiceIdentifiers();
                }

                ServiceBean serviceBean = ServiceBean.builder()
                        .id(existingController.getId())
                        .name(suffixedName)
                        .identifier(existingController.getIdentifier())
                        .layer(layer)
                        .appIds(appIds)
                        .appIdentifier(appIdentifiers != null && !appIdentifiers.isEmpty() ? appIdentifiers.get(0) : null)
                        .accountId(accountId)
                        .accountIdentifier(accountIdentifier)
                        .userId(userId)
                        .type(type)
                        .status(1)
                        .entryPointService(servicePojo.getIsEntryPointService() == 1)
                        .environment(servicePojo.getEnvironment() != null ? servicePojo.getEnvironment() : existingController.getEnvironment())
                        .serviceGroup(servicePojo.getServiceGroup())
                        .mappedServiceIdentifiers(mappedIds)
                        .linkedIdentifier(linkedIdentifier)
                        .build();
                serviceBeans.add(serviceBean);
            }

            return UtilityBean.<List<ServiceBean>>builder()
                    .pojoObject(serviceBeans)
                    .requestParams(utilityBean.getRequestParams())
                    .metadata(utilityBean.getMetadata())
                    .build();
        } catch (Exception e) {
            log.error("Error while validating server-side constraints for updating account service.", e);
            throw new ServerException("Failed to validate server-side constraints for updating account service.");
        }
    }

    /**
     * Processes the update of account services after validation.
     * Updates service details, mappings, tags, linked services, and Redis cache.
     * @param utilityBean UtilityBean containing validated ServiceBean objects
     * @return List of IdPojo representing updated services
     * @throws DataProcessingException if update fails
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogExecutionAnnotation
    public List<IdPojo> process(UtilityBean<List<ServiceBean>> utilityBean) throws DataProcessingException {
        List<ServiceBean> serviceBeans = utilityBean.getPojoObject();
        List<IdPojo> results = new ArrayList<>();
        try {
            for (ServiceBean serviceBean : serviceBeans) {
                // Retrieve the old service name before updating
                ControllerBean oldServiceController = controllerDao.getControllerById(serviceBean.getId());
                String oldServiceName = null;
                if (oldServiceController != null) {
                    oldServiceName = oldServiceController.getName();
                }

                // Check if the service was previously linked and is now being unlinked
                ServiceAliases existingLinkedAlias = accountServiceDao.getLinkedServiceByIdentifier(serviceBean.getIdentifier());
                if (existingLinkedAlias != null && (serviceBean.getLinkedIdentifier() == null || StringUtils.isEmpty(serviceBean.getMappedServiceIdentifiers()))) {
                    log.info("Service [{}] was previously linked and is now being unlinked. Deleting existing alias.", serviceBean.getIdentifier());
                    accountServiceDao.deleteServiceAliasesByIdentifiers(existingLinkedAlias.getDcServiceIdentifier(), existingLinkedAlias.getDrServiceIdentifier());
                }

                updateService(serviceBean);
                handleServiceGroupMapping(serviceBean);
                handleEntryPointTag(serviceBean);
                if (serviceBean.getLinkedIdentifier() != null && !StringUtils.isEmpty(serviceBean.getMappedServiceIdentifiers())) {
                    handleLinkedServices(serviceBean);
                }
                handleServiceAliasesUpdate(oldServiceName, serviceBean.getName(), serviceBean); // New method call
                updateServiceInRedis(serviceBean);

                results.add(IdPojo.builder()
                        .id(serviceBean.getId())
                        .name(serviceBean.getName())
                        .build());
            }
            return results;
        } catch (Exception e) {
            log.error("Error while processing update for account service.", e);
            throw new DataProcessingException("Failed to process update for account service: " + e.getMessage());
        }
    }

    /**
     * Updates the service details in the controller table.
     * @param bean ServiceBean containing updated service data
     * @throws HealControlCenterException if update fails
     */
    public void updateService(ServiceBean bean) throws HealControlCenterException, DataProcessingException {
        ViewTypesBean serviceControllerType = masterDataDao.getViewTypesFromMstTypeAndSubTypeName(
                Constants.CONTROLLER_TYPE_NAME_DEFAULT,
                Constants.SERVICES_CONTROLLER_TYPE);

        if (serviceControllerType == null) {
            throw new HealControlCenterException("Configuration error: 'Service' controller type not found.");
        }

        ControllerBean controllerBean = controllerBL.updateControllerDetails(bean, serviceControllerType.getSubTypeId());

        if (controllerBean.getId() == -1) {
            throw new HealControlCenterException("Unable to update the service in the controller table.");
        }
    }

    /**
     * Handles mapping of the service to a service group.
     * @param bean ServiceBean containing service group info
     * @throws HealControlCenterException if mapping fails
     */
    public void handleServiceGroupMapping(ServiceBean bean) throws HealControlCenterException {
        accountServiceDao.deleteServiceGroupMapping(bean.getId());

        if (bean.getServiceGroup() != null && !StringUtils.isEmpty(bean.getServiceGroup().getIdentifier())) {
            ServiceGroupBean existingGroup = accountServiceDao.findServiceGroupByIdentifier(
                    bean.getServiceGroup().getIdentifier(), bean.getAccountId());

            if (existingGroup != null) {
                int result = accountServiceDao.createServiceGroupMapping(bean.getId(), existingGroup.getId(), bean.getUserId());
                if (result <= 0) {
                    throw new HealControlCenterException("Failed to map service to service group");
                }
            } else {
                throw new HealControlCenterException("Service group with identifier [" + bean.getServiceGroup().getIdentifier() + "] not found for this account.");
            }
        }
    }

    /**
     * Handles entry point tag mapping for the service.
     * @param bean ServiceBean containing entry point info
     * @throws HealControlCenterException if tag mapping fails
     */
    public void handleEntryPointTag(ServiceBean bean) throws HealControlCenterException {
        TagDetailsBean entryPointTagDetails = accountServiceDao.getTagDetails(Constants.ENTRY_POINT, bean.getAccountId());
        if (entryPointTagDetails == null) {
            throw new HealControlCenterException("Unable to fetch tag details for Entry-Point.");
        }

        accountServiceDao.deleteTagMapping(entryPointTagDetails.getId(), bean.getId(), Constants.CONTROLLER, bean.getAccountId());

        if (bean.isEntryPointService()) {
            int id = tagMappingBL.addTagMapping(entryPointTagDetails.getId(), bean.getId(),
                    Constants.CONTROLLER, "Type", "1", bean.getUserId(), bean.getAccountId());

            if (id == -1) {
                throw new HealControlCenterException("Unable to save entry point tag mapping details.");
            }
        }
    }

    /**
     * Handles linking of services across environments (DC/DR).
     * @param serviceBean ServiceBean containing linked service info
     * @throws HealControlCenterException if linking fails
     */
    public void handleLinkedServices(ServiceBean serviceBean) throws HealControlCenterException {
        if (serviceBean.getLinkedIdentifier() == null || StringUtils.isEmpty(serviceBean.getMappedServiceIdentifiers())) {
            return;
        }

        String commonName = serviceBean.getName();
        String updatedTime = DateTimeUtil.getTimeInGMT(System.currentTimeMillis());

        ControllerBean linkedController = controllerDao.getControllerByIdentifierOrName(serviceBean.getMappedServiceIdentifiers(), null);
        if (linkedController == null) {
            throw new HealControlCenterException("The specified service to link with identifier does not exist.");
        }

        String dcIdentifier = "DC".equalsIgnoreCase(serviceBean.getEnvironment()) ? serviceBean.getIdentifier() : serviceBean.getMappedServiceIdentifiers();
        String drIdentifier = "DR".equalsIgnoreCase(serviceBean.getEnvironment()) ? serviceBean.getIdentifier() : serviceBean.getMappedServiceIdentifiers();

        // First, delete any existing linked service entries that involve these identifiers
        accountServiceDao.deleteServiceAliasesByIdentifiers(serviceBean.getIdentifier(), serviceBean.getMappedServiceIdentifiers());

        // Then, insert the new linked service entry
        int result = accountServiceDao.insertLinkedService(commonName, dcIdentifier, drIdentifier, serviceBean.getUserId(), updatedTime, updatedTime, serviceBean.getStatus());
        if (result <= 0) {
            throw new HealControlCenterException("Failed to insert new service alias for common name [" + commonName + "].");
        }
        log.info("Successfully inserted new service alias for common name [{}]. Rows affected: {}", commonName, result);
    }

    /**
     * Handles the update of service aliases when a service name changes.
     * This method updates the common_name of an existing service alias entry
     * if the service's name has changed and a linked entry exists.
     *
     * @param oldName The old name of the service (suffixed).
     * @param newName The new name of the service (suffixed).
     * @param serviceBean The ServiceBean containing updated service data.
     * @throws HealControlCenterException if an error occurs during the update.
     */
    public  void handleServiceAliasesUpdate(String oldName, String newName, ServiceBean serviceBean) throws HealControlCenterException {
        log.info("handleServiceAliasesUpdate invoked. oldName: {}, newName: {}", oldName, newName);

        if (oldName == null || oldName.isEmpty() || oldName.equals(newName)) {
            log.debug("Old service name is null, empty, or same as new name. Skipping service alias update.");
            return;
        }

        // Find the existing service alias using the service's identifier (which is stored in dc_service_identifier or dr_service_identifier)
        ServiceAliases existingAlias = accountServiceDao.getLinkedServiceByIdentifier(serviceBean.getIdentifier());

        if (existingAlias != null) {
            log.info("Found existing service alias for service identifier [{}]. Existing alias details: {}", serviceBean.getIdentifier(), existingAlias);

            String oldCommonName = existingAlias.getCommonName(); // This will be the suffixed name from DB
            String updatedTime = DateTimeUtil.getTimeInGMT(System.currentTimeMillis());
            String dcIdentifier = existingAlias.getDcServiceIdentifier();
            String drIdentifier = existingAlias.getDrServiceIdentifier();

            // Update DC/DR identifiers if the current service is the one being updated
            if (dcIdentifier != null && dcIdentifier.equals(serviceBean.getIdentifier())) {
                dcIdentifier = serviceBean.getIdentifier();
            }
            if (drIdentifier != null && drIdentifier.equals(serviceBean.getIdentifier())) {
                drIdentifier = serviceBean.getIdentifier();
            }

            log.info("Updating service alias from old commonName: {} to new commonName: {}, dcIdentifier: {}, drIdentifier: {}, userId: {}, updatedTime: {}, status: {}",
                    oldCommonName, newName, dcIdentifier, drIdentifier, serviceBean.getUserId(), updatedTime, serviceBean.getStatus());

            int result = accountServiceDao.updateServiceAliasCommonName(newName, dcIdentifier, drIdentifier, serviceBean.getUserId(), updatedTime, serviceBean.getStatus(), serviceBean.getIdentifier());
            if (result <= 0) {
                throw new HealControlCenterException("Failed to update service alias for old common name [" + oldCommonName + "] to new common name [" + newName + "].");
            }
            log.info("Successfully updated service alias from old common name [{}] to new common name [{}]. Rows affected: {}", oldCommonName, newName, result);
        } else {
            log.debug("No existing service alias found for service identifier [{}]. No update needed.", serviceBean.getIdentifier());
        }
    }

    /**
     * Updates the service configuration and rules in Redis cache.
     * @param serviceBean ServiceBean containing updated service data
     */
    void updateServiceInRedis(ServiceBean serviceBean) {
        List<PersistenceSuppressionConfiguration> suppressionConfigs = accountServiceDao.getAnomalySuppressionForService(serviceBean.getId());
        ServiceConfiguration config = buildServiceConfigurations(serviceBean);
        config.setPersistenceSuppressionConfigurations(suppressionConfigs);

        List<Tags> tags = buildTags(serviceBean);

        com.heal.configuration.pojos.Service serviceObject = com.heal.configuration.pojos.Service.builder()
                .id(serviceBean.getId())
                .status(serviceBean.getStatus())
                .createdTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                .updatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                .name(serviceBean.getName())
                .type(serviceBean.getType())
                .lastModifiedBy(serviceBean.getUserId())
                .serviceConfiguration(config)
                .tags(tags)
                .build();

        List<Rule> rules = buildRulesForService(serviceBean);

        serviceRepo.updateServiceRules(serviceBean.getAccountIdentifier(), serviceBean.getIdentifier(), rules);
        serviceRepo.updateServiceConfigurationByServiceIdentifier(serviceBean.getAccountIdentifier(), String.valueOf(serviceBean.getId()), serviceObject);

        BasicEntity basicEntityObject = updateServiceInList(serviceRepo, serviceBean);
        updateServiceMappedToApplications(serviceBean, basicEntityObject);
        updateApplicationsToService(serviceBean);
    }

    /**
     * Builds the service configuration object for Redis update.
     * @param serviceBean ServiceBean containing service data
     * @return ServiceConfiguration object
     */
    public ServiceConfiguration buildServiceConfigurations(ServiceBean serviceBean) {
        String currentTime = DateTimeUtil.getTimeInGMT(System.currentTimeMillis());
        return ServiceConfiguration.builder()
                .lastModifiedBy(serviceBean.getUserId())
                .createdTime(currentTime)
                .updatedTime(currentTime)
                .lowEnable(true)
                .mediumEnable(true)
                .highEnable(true)
                .build();
    }

    /**
     * Builds the list of tags for the service.
     * @param serviceBean ServiceBean containing tag data
     * @return List of Tags
     */
    public List<Tags> buildTags(ServiceBean serviceBean) {
        List<Tags> tags = new ArrayList<>();
        if (serviceBean.getLayer() != null && !serviceBean.getLayer().trim().isEmpty()) {
            tags.add(Tags.builder().type(Constants.LAYER_TAG).value(serviceBean.getLayer().trim()).key(Constants.LAYER_DEFAULT).build());
        }
        if (serviceBean.getType() != null && !serviceBean.getType().trim().isEmpty()) {
            tags.add(Tags.builder().type(Constants.SERVICE_TYPE_TAG).value(serviceBean.getType().trim()).key(Constants.DEFAULT_TAG_VALUE).build());
        }
        if (serviceBean.isEntryPointService()) {
            tags.add(Tags.builder().type(Constants.ENTRY_POINT).value("1").key(Constants.LAYER_DEFAULT).build());
        }
        return tags;
    }

    /**
     * Builds the list of rules for the service from DB and master data.
     * @param serviceBean ServiceBean containing rule data
     * @return List of Rule objects
     */
    public List<Rule> buildRulesForService(ServiceBean serviceBean) {
        List<RulesHelperPojo> rulesHelperList = accountServiceDao.getRulesHelperPojo(serviceBean.getAccountId(), serviceBean.getId());
        if (rulesHelperList.isEmpty()) return Collections.emptyList();
        List<ViewTypes> viewTypesList = masterDataRepo.getViewTypes();
        return rulesHelperList.stream().map(rule -> {
            RegexTypeDetail regexDetail = RegexTypeDetail.builder().id(rule.getTcpId()).initialPattern(rule.getTcpInitialPattern()).endPattern(rule.getTcpLastPattern()).length(rule.getTcpLength()).build();
            List<PairData> pairDataList = accountServiceDao.getDataBeans(rule.getId(), rule.getHttpId());
            RequestTypeDetail requestTypeDetail = RequestTypeDetail.builder().id(rule.getHttpId()).firstUriSegments(rule.getHttpFirstUriSegments()).lastUriSegments(rule.getHttpLastUriSegments()).completeURI(rule.getHttpCompleteURI() != 0).payloadTypeId(rule.getHttpPayloadTypeId()).payloadTypeName(viewTypesList.stream().filter(vt -> vt.getSubTypeId() == rule.getHttpPayloadTypeId()).map(ViewTypes::getSubTypeName).findFirst().orElse("")).pairData(pairDataList).build();
            return Rule.builder().id(rule.getId()).name(rule.getName()).monitoringEnabled(rule.getEnabled() != 0).discoveryEnabled(rule.getDiscoveryEnabled() == 1).order(rule.getOrder()).ruleTypeId(rule.getRuleTypeId()).ruleType(viewTypesList.stream().filter(vt -> vt.getSubTypeId() == rule.getRuleTypeId()).map(ViewTypes::getSubTypeName).findFirst().orElse("")).isDefault(rule.getIsDefault()).maxTags(rule.getMaxTags()).regexTypeDetails(regexDetail).requestTypeDetails(requestTypeDetail).transactionGroups(new ArrayList<>()).build();
        }).collect(Collectors.toList());
    }

    /**
     * Updates the service in the list of all services in Redis.
     * @param serviceRepo ServiceRepo instance
     * @param serviceBean ServiceBean containing updated service data
     * @return BasicEntity representing the updated service
     */
    public BasicEntity updateServiceInList(ServiceRepo serviceRepo, ServiceBean serviceBean) {
        List<BasicEntity> allServicesDetails = serviceRepo.getAllServicesDetails(serviceBean.getAccountIdentifier());
        allServicesDetails.removeIf(s -> s.getId() == serviceBean.getId());
        BasicEntity updatedService = BasicEntity.builder().id(serviceBean.getId()).name(serviceBean.getName()).identifier(serviceBean.getIdentifier()).status(serviceBean.getStatus()).createdTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis())).updatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis())).lastModifiedBy(serviceBean.getUserId()).build();
        allServicesDetails.add(updatedService);
        serviceRepo.updateServiceConfiguration(serviceBean.getAccountIdentifier(), allServicesDetails);
        return updatedService;
    }

    /**
     * Updates the mapping of the service to applications in Redis.
     * @param serviceBean ServiceBean containing mapping info
     * @param serviceBasicEntity BasicEntity of the updated service
     */
    public void updateServiceMappedToApplications(ServiceBean serviceBean, BasicEntity serviceBasicEntity) {
        if (StringUtils.isEmpty(serviceBean.getMappedServiceIdentifiers())) {
            return;
        }
        String appIdentifier = serviceBean.getMappedServiceIdentifiers();
        List<BasicEntity> mappedServices = applicationRepo.getServicesMappedToApplication(serviceBean.getAccountIdentifier(), appIdentifier);
        mappedServices.removeIf(s -> s.getId() == serviceBasicEntity.getId());
        mappedServices.add(serviceBasicEntity);
        applicationRepo.updateServiceApplication(serviceBean.getAccountIdentifier(), appIdentifier, mappedServices);
    }

    /**
     * Updates the applications mapped to the service in Redis.
     * @param serviceBean ServiceBean containing mapping info
     */
    public void updateApplicationsToService(ServiceBean serviceBean) {
        List<BasicEntity> allApplications = accountRepo.getAllApplications(serviceBean.getAccountIdentifier());
        List<BasicEntity> filteredApps = new ArrayList<>();
        for (Integer appId : serviceBean.getAppIds()) {
            allApplications.stream().filter(app -> app.getId() == appId).findAny().ifPresent(filteredApps::add);
        }
        serviceRepo.updateApplicationsByServiceIdentifier(serviceBean.getAccountIdentifier(), serviceBean.getIdentifier(), filteredApps);
    }
}