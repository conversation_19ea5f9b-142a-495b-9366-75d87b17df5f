<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CORS Testing Tool</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .log { background: #f8f9fa; padding: 10px; border-radius: 3px; margin: 10px 0; font-family: monospace; white-space: pre-wrap; }
        input[type="text"] { width: 100%; padding: 8px; margin: 5px 0; border: 1px solid #ddd; border-radius: 3px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 CORS Testing Tool</h1>
        
        <div class="test-section">
            <h3>Configuration</h3>
            <label>API Base URL:</label>
            <input type="text" id="apiUrl" value="http://localhost:8080/v2.0/api/public/health" placeholder="http://localhost:8080/v2.0/api/public/health">
            <small>Try these if default doesn't work:</small>
            <div style="margin: 5px 0;">
                <button type="button" onclick="setApiUrl('http://localhost:8080/v2.0/api/public/health')" style="font-size: 12px; padding: 5px;">Working URL</button>
                <button type="button" onclick="setApiUrl('http://127.0.0.1:8080/v2.0/api/public/health')" style="font-size: 12px; padding: 5px;">127.0.0.1</button>
                <button type="button" onclick="setApiUrl('http://localhost:8080/v2.0/api')" style="font-size: 12px; padding: 5px;">Original API</button>
                <button type="button" onclick="setApiUrl('https://localhost:8080/v2.0/api/public/health')" style="font-size: 12px; padding: 5px;">HTTPS</button>
            </div>

            <label>Authorization Header (for access token testing):</label>
            <input type="text" id="authToken" placeholder="Bearer your-access-token-here">
            <small>Example: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...</small>
        </div>

        <div class="test-section">
            <h3>🔍 Connection Test</h3>
            <p>Test if your backend is reachable before testing CORS.</p>
            <button onclick="testConnection()">Test Backend Connection</button>
            <div id="connectionResult" class="log"></div>
        </div>

        <div class="test-section">
            <h3>🧪 Test 1: Simple GET Request (No Preflight)</h3>
            <p>This test uses only simple headers, so no preflight OPTIONS request is sent.</p>
            <button onclick="testSimpleRequest()">Test Simple GET</button>
            <div id="simpleResult" class="log"></div>
        </div>

        <div class="test-section">
            <h3>🧪 Test 2: Complex GET Request (With Authorization Header)</h3>
            <p>This test includes Authorization header for access token, which triggers a preflight OPTIONS request.</p>
            <button onclick="testComplexRequest()">Test with Authorization Header</button>
            <div id="complexResult" class="log"></div>
        </div>

        <div class="test-section">
            <h3>🧪 Test 3: POST Request (With Preflight)</h3>
            <p>This test sends a POST request with JSON data, which triggers preflight.</p>
            <button onclick="testPostRequest()">Test POST</button>
            <div id="postResult" class="log"></div>
        </div>

        <div class="test-section">
            <h3>🧪 Test 4: Manual OPTIONS Request</h3>
            <p>This test manually sends an OPTIONS request to check preflight response.</p>
            <button onclick="testOptionsRequest()">Test OPTIONS</button>
            <div id="optionsResult" class="log"></div>
        </div>

        <div class="test-section">
            <h3>� Authorization Header Test</h3>
            <p>Specifically test if Authorization header is allowed by CORS.</p>
            <button onclick="testAuthorizationHeader()">Test Authorization Header</button>
            <div id="authResult" class="log"></div>
        </div>

        <div class="test-section">
            <h3>�📊 CORS Headers Analysis</h3>
            <button onclick="analyzeHeaders()">Analyze CORS Headers</button>
            <div id="headersResult" class="log"></div>
        </div>

        <div class="test-section">
            <h3>🔧 CORS Diagnostics</h3>
            <p>Detailed analysis of what's working and what's not.</p>
            <button onclick="runDiagnostics()">Run Full Diagnostics</button>
            <div id="diagnosticsResult" class="log"></div>
        </div>
    </div>

    <script>
        function log(elementId, message, isError = false) {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            element.textContent = `[${timestamp}] ${message}`;
            element.parentElement.className = `test-section ${isError ? 'error' : 'success'}`;
        }

        function getApiUrl() {
            return document.getElementById('apiUrl').value.trim();
        }

        function getAuthToken() {
            return document.getElementById('authToken').value.trim();
        }

        function setApiUrl(url) {
            document.getElementById('apiUrl').value = url;
        }

        async function testConnection() {
            const baseUrl = getApiUrl();
            log('connectionResult', 'Testing backend connection...');

            // Try a simple endpoint first
            const testUrls = [
                `${baseUrl}/accounts`,
                `${baseUrl}/health`,
                `${baseUrl}`,
                baseUrl.replace('/v2.0/api', '') + '/actuator/health',
                baseUrl.replace('/v2.0/api', '') + '/health'
            ];

            for (let url of testUrls) {
                try {
                    log('connectionResult', `Trying: ${url}`);

                    const response = await fetch(url, {
                        method: 'GET',
                        mode: 'no-cors' // This bypasses CORS for connection testing
                    });

                    log('connectionResult', `✅ CONNECTION SUCCESS!\nBackend is reachable at: ${url}\nYou can now test CORS functionality.`);
                    return;
                } catch (error) {
                    console.log(`Failed ${url}:`, error.message);
                }
            }

            log('connectionResult', `❌ CONNECTION FAILED!\n\nTroubleshooting:\n1. Is your backend running? Check: mvn spring-boot:run\n2. Try different URLs using the buttons above\n3. Check if port 8080 is correct\n4. Try opening ${baseUrl}/timezones directly in a new browser tab`, true);
        }

        async function testSimpleRequest() {
            const baseUrl = getApiUrl();
            log('simpleResult', 'Testing simple GET request...');

            // Test the working URL first, then try variations
            const testUrls = [
                baseUrl, // The exact URL that worked in connection test
                `${baseUrl}/accounts`,
                baseUrl.replace('/public/health', '') + '/accounts', // Try without /public/health
                baseUrl.replace('/public/health', '') + '/timezones'
            ];

            for (let url of testUrls) {
                try {
                    log('simpleResult', `Trying: ${url}`);

                    const response = await fetch(url, {
                        method: 'GET',
                        headers: {
                            'Accept': 'application/json'
                        }
                    });

                    log('simpleResult', `✅ SUCCESS: ${response.status} ${response.statusText}\nURL: ${url}\nCORS Headers:\n${getCorsHeaders(response)}`);
                    return; // Success, exit the loop
                } catch (error) {
                    console.log(`Failed ${url}:`, error.message);
                    // Continue to next URL
                }
            }

            // If we get here, all URLs failed
            log('simpleResult', `❌ ALL ENDPOINTS FAILED\n\nThis suggests a CORS configuration issue.\nThe backend is reachable (connection test passed) but CORS headers are missing or incorrect.\n\nCheck:\n1. HealWebMvcConfiguration is loaded\n2. CORS headers in response\n3. Browser console for detailed error`, true);
        }

        async function testComplexRequest() {
            const baseUrl = getApiUrl();
            const token = getAuthToken();
            log('complexResult', 'Testing complex GET request with Authorization header...');

            const testUrls = [
                baseUrl, // The working URL
                baseUrl.replace('/public/health', '') + '/accounts',
                baseUrl.replace('/public/health', '') + '/timezones'
            ];

            for (let url of testUrls) {
                try {
                    log('complexResult', `Trying complex request: ${url}`);

                    const headers = {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    };

                    if (token) {
                        headers['Authorization'] = token;
                        console.log('🔑 Testing with Authorization header:', token.substring(0, 20) + '...');
                    } else {
                        // Use a test token to trigger preflight even without user input
                        headers['Authorization'] = 'Bearer test-token-for-cors-testing';
                        console.log('🔑 Using test Authorization header to trigger CORS preflight');
                    }

                    const response = await fetch(url, {
                        method: 'GET',
                        headers: headers
                    });

                    log('complexResult', `✅ SUCCESS: ${response.status} ${response.statusText}\nURL: ${url}\nCORS Headers:\n${getCorsHeaders(response)}`);
                    return;
                } catch (error) {
                    console.log(`Failed complex request ${url}:`, error.message);
                }
            }

            log('complexResult', `❌ COMPLEX REQUEST FAILED\n\nThis indicates CORS preflight issues.\nComplex requests (with Authorization header) require:\n1. OPTIONS request to succeed\n2. Proper CORS headers in OPTIONS response\n3. Backend to handle preflight requests\n\nCheck browser Network tab for OPTIONS request details.`, true);
        }

        async function testPostRequest() {
            const url = `${getApiUrl()}/accounts`;
            const token = getAuthToken();
            log('postResult', 'Testing POST request...');
            
            try {
                const headers = {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                };
                
                if (token) {
                    headers['Authorization'] = token;
                }
                
                const response = await fetch(url, {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify({ test: 'data' })
                });
                
                log('postResult', `✅ SUCCESS: ${response.status} ${response.statusText}\nCORS Headers:\n${getCorsHeaders(response)}`);
            } catch (error) {
                log('postResult', `❌ ERROR: ${error.message}`, true);
            }
        }

        async function testOptionsRequest() {
            const baseUrl = getApiUrl();
            log('optionsResult', 'Testing OPTIONS request (preflight)...');

            const testUrls = [
                baseUrl, // The working URL
                baseUrl.replace('/public/health', '') + '/accounts'
            ];

            for (let url of testUrls) {
                try {
                    log('optionsResult', `Testing OPTIONS on: ${url}`);

                    const response = await fetch(url, {
                        method: 'OPTIONS',
                        headers: {
                            'Origin': window.location.origin,
                            'Access-Control-Request-Method': 'GET',
                            'Access-Control-Request-Headers': 'Content-Type,Authorization,Accept'
                        }
                    });

                    log('optionsResult', `✅ SUCCESS: ${response.status} ${response.statusText}\nURL: ${url}\nPreflight Headers:\n${getCorsHeaders(response)}`);
                    return;
                } catch (error) {
                    console.log(`Failed OPTIONS ${url}:`, error.message);
                }
            }

            log('optionsResult', `❌ OPTIONS REQUEST FAILED\n\nThis means preflight requests are not working.\nWithout working OPTIONS requests, complex CORS requests will fail.\n\nCheck:\n1. Backend handles OPTIONS method\n2. CORS configuration includes OPTIONS\n3. No firewall blocking OPTIONS requests`, true);
        }

        async function testAuthorizationHeader() {
            const baseUrl = getApiUrl();
            log('authResult', 'Testing Authorization header specifically...');

            try {
                // First test OPTIONS to see if Authorization is allowed
                const optionsResponse = await fetch(baseUrl, {
                    method: 'OPTIONS',
                    headers: {
                        'Origin': window.location.origin,
                        'Access-Control-Request-Method': 'GET',
                        'Access-Control-Request-Headers': 'Authorization'
                    }
                });

                const allowedHeaders = optionsResponse.headers.get('access-control-allow-headers');
                console.log('Allowed headers from OPTIONS:', allowedHeaders);

                if (allowedHeaders && allowedHeaders.toLowerCase().includes('authorization')) {
                    // Now test actual request with Authorization header
                    const response = await fetch(baseUrl, {
                        method: 'GET',
                        headers: {
                            'Authorization': 'Bearer test-access-token-12345',
                            'Content-Type': 'application/json'
                        }
                    });

                    log('authResult', `✅ AUTHORIZATION HEADER SUCCESS!\n\nOPTIONS Response: ${optionsResponse.status}\nAllowed Headers: ${allowedHeaders}\n\nGET Response: ${response.status} ${response.statusText}\nCORS Origin: ${response.headers.get('access-control-allow-origin')}\n\n🎉 Your API properly supports Authorization headers for access tokens!`);
                } else {
                    log('authResult', `❌ AUTHORIZATION HEADER NOT ALLOWED\n\nOPTIONS Response: ${optionsResponse.status}\nAllowed Headers: ${allowedHeaders || 'None'}\n\n❌ The Authorization header is not in the allowed headers list.\nThis means access tokens won't work with CORS requests.`, true);
                }
            } catch (error) {
                log('authResult', `❌ AUTHORIZATION TEST FAILED: ${error.message}\n\nThis means either:\n1. OPTIONS request failed\n2. Authorization header is blocked\n3. CORS configuration issue\n\nCheck browser console for details.`, true);
            }
        }

        async function analyzeHeaders() {
            const url = `${getApiUrl()}/accounts`;
            log('headersResult', 'Analyzing CORS headers...');

            try {
                const response = await fetch(url, {
                    method: 'GET',
                    headers: { 'Accept': 'application/json' }
                });

                const corsAnalysis = analyzeCorsHeaders(response);
                log('headersResult', corsAnalysis);
            } catch (error) {
                log('headersResult', `❌ ERROR: ${error.message}`, true);
            }
        }

        function getCorsHeaders(response) {
            const corsHeaders = [
                'access-control-allow-origin',
                'access-control-allow-methods',
                'access-control-allow-headers',
                'access-control-allow-credentials',
                'access-control-max-age'
            ];
            
            let result = '';
            corsHeaders.forEach(header => {
                const value = response.headers.get(header);
                if (value) {
                    result += `${header}: ${value}\n`;
                }
            });
            
            return result || 'No CORS headers found';
        }

        function analyzeCorsHeaders(response) {
            const origin = response.headers.get('access-control-allow-origin');
            const methods = response.headers.get('access-control-allow-methods');
            const headers = response.headers.get('access-control-allow-headers');
            const credentials = response.headers.get('access-control-allow-credentials');
            
            let analysis = '📊 CORS Configuration Analysis:\n\n';
            
            analysis += `Origin Policy: ${origin || 'NOT SET'}\n`;
            if (origin === '*') {
                analysis += '  ✅ Allows all origins\n';
            } else if (origin) {
                analysis += `  ✅ Allows specific origin: ${origin}\n`;
            } else {
                analysis += '  ❌ No origin policy set\n';
            }
            
            analysis += `\nAllowed Methods: ${methods || 'NOT SET'}\n`;
            if (methods && methods.includes('OPTIONS')) {
                analysis += '  ✅ OPTIONS method supported (preflight enabled)\n';
            } else {
                analysis += '  ❌ OPTIONS method missing (preflight may fail)\n';
            }
            
            analysis += `\nAllowed Headers: ${headers || 'NOT SET'}\n`;
            if (headers && headers.includes('Authorization')) {
                analysis += '  ✅ Authorization header supported\n';
            } else {
                analysis += '  ⚠️  Authorization header may not be supported\n';
            }
            
            analysis += `\nCredentials: ${credentials || 'NOT SET'}\n`;
            if (credentials === 'true' && origin === '*') {
                analysis += '  ❌ INVALID: Cannot use credentials with wildcard origin\n';
            }
            
            return analysis;
        }

        // Auto-detect current page origin
        window.onload = function() {
            console.log('🔍 CORS Testing Tool loaded');
            console.log('Current origin:', window.location.origin);
            console.log('Open browser DevTools (F12) to see detailed network requests');
        };
    </script>
</body>
</html>
