//package com.heal.controlcenter.businesslogic;
//
//import com.heal.configuration.entities.UserAccessDetails;
//import com.heal.configuration.pojos.*;
//import com.heal.controlcenter.beans.ControllerBean;
//import com.heal.controlcenter.beans.UtilityBean;
//import com.heal.controlcenter.beans.ViewTypesBean;
//import com.heal.controlcenter.dao.mysql.ControllerDao;
//import com.heal.controlcenter.dao.mysql.ServiceDao;
//import com.heal.controlcenter.dao.mysql.entity.ServiceKpiThreshold;
//import com.heal.controlcenter.dao.opensearch.ServiceOpensearchRepo;
//import com.heal.controlcenter.dao.redis.ServiceRepo;
//import com.heal.controlcenter.exception.ClientException;
//import com.heal.controlcenter.exception.ControlCenterException;
//import com.heal.controlcenter.exception.DataProcessingException;
//import com.heal.controlcenter.exception.ServerException;
//import com.heal.controlcenter.pojo.StaticThresholdRules;
//import com.heal.controlcenter.service.InstallationAttributeService;
//import com.heal.controlcenter.util.*;
//import org.apache.commons.lang3.tuple.Pair;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.MockitoAnnotations;
//import org.mockito.Spy;
//
//import java.sql.Timestamp;
//import java.util.*;
//
//import static org.junit.jupiter.api.Assertions.*;
//import static org.mockito.Mockito.*;
//
//class PostServiceStaticThresholdBLTest {
//
//    @InjectMocks
//    private PostServiceStaticThresholdBL postServiceStaticThresholdBL;
//
//    @Mock
//    private CacheWrapper cacheWrapper;
//
//    @Mock
//    private CommonUtils commonUtils;
//
//
//    @Mock
//    private ControllerDao controllerDao;
//
//    @Mock
//    private GetServiceStaticThresholdBL getServiceStaticThresholdBL;
//
//    @Mock
//    private ServiceDao serviceDao;
//
//    @Mock
//    private ServiceOpensearchRepo serviceOpensearchRepo;
//
//    @Mock
//    private ServiceRepo serviceRepo;
//
//    @Mock
//    private InstallationAttributeService installationAttributeService;
//
//    @Mock
//    private DateTimeUtil dateTimeUtil;
//
//    @Spy
//    private ClientValidationUtils clientValidationUtils;
//
//    @Mock
//    private ServerValidationUtils serverValidationUtils;
//
//    @BeforeEach
//    void setUp() throws ControlCenterException {
//        MockitoAnnotations.openMocks(this);
//
//        // Mock cacheWrapper methods
//        HashMap<Integer, ViewTypesBean> viewTypesIdMap = new HashMap<>();
//        ViewTypesBean lowThresholdViewType = new ViewTypesBean();
//        lowThresholdViewType.setTypeId(1);
//        lowThresholdViewType.setTypeName(Constants.THRESHOLD_SEVERITY_TYPE);
//        lowThresholdViewType.setSubTypeName(Constants.THRESHOLD_SEVERITY_TYPE_LOW);
//        lowThresholdViewType.setSubTypeId(1);
//
//        ViewTypesBean mediumThresholdViewType = new ViewTypesBean();
//        mediumThresholdViewType.setTypeId(1);
//        mediumThresholdViewType.setTypeName(Constants.THRESHOLD_SEVERITY_TYPE);
//        mediumThresholdViewType.setSubTypeName(Constants.THRESHOLD_SEVERITY_TYPE_MEDIUM);
//        mediumThresholdViewType.setSubTypeId(2);
//
//        ViewTypesBean highThresholdViewType = new ViewTypesBean();
//        highThresholdViewType.setTypeId(1);
//        highThresholdViewType.setTypeName(Constants.THRESHOLD_SEVERITY_TYPE);
//        highThresholdViewType.setSubTypeName(Constants.THRESHOLD_SEVERITY_TYPE_HIGH);
//        highThresholdViewType.setSubTypeId(3);
//
//        ViewTypesBean kpiTypeView = new ViewTypesBean();
//        kpiTypeView.setTypeId(2);
//        kpiTypeView.setTypeName(Constants.KPI_TYPE);
//        kpiTypeView.setSubTypeName("validKpiType");
//        kpiTypeView.setSubTypeId(4);
//
//        ViewTypesBean lesserOperationType = new ViewTypesBean();
//        lesserOperationType.setTypeId(3);
//        lesserOperationType.setTypeName(Constants.OPERATIONS_TYPE);
//        lesserOperationType.setSubTypeName(Constants.OPERATIONS_TYPE_LESSER_THAN);
//        lesserOperationType.setSubTypeId(5);
//
//        ViewTypesBean greaterOperationType = new ViewTypesBean();
//        greaterOperationType.setTypeId(3);
//        greaterOperationType.setTypeName(Constants.OPERATIONS_TYPE);
//        greaterOperationType.setSubTypeName(Constants.OPERATIONS_TYPE_GREATER_THAN);
//        greaterOperationType.setSubTypeId(6);
//
//        ViewTypesBean notBetweenOperationType = new ViewTypesBean();
//        notBetweenOperationType.setTypeId(3);
//        notBetweenOperationType.setTypeName(Constants.OPERATIONS_TYPE);
//        notBetweenOperationType.setSubTypeName(Constants.OPERATIONS_TYPE_NOT_BETWEEN);
//        notBetweenOperationType.setSubTypeId(7);
//
//        ViewTypesBean coreKpiTypeView = new ViewTypesBean();
//        coreKpiTypeView.setTypeId(2);
//        coreKpiTypeView.setTypeName(Constants.KPI_TYPE);
//        coreKpiTypeView.setSubTypeName("Core");
//        coreKpiTypeView.setSubTypeId(8);
//
//        viewTypesIdMap.put(1, lowThresholdViewType);
//        viewTypesIdMap.put(2, mediumThresholdViewType);
//        viewTypesIdMap.put(3, highThresholdViewType);
//        viewTypesIdMap.put(4, kpiTypeView);
//        viewTypesIdMap.put(5, lesserOperationType);
//        viewTypesIdMap.put(6, greaterOperationType);
//        viewTypesIdMap.put(7, notBetweenOperationType);
//        viewTypesIdMap.put(8, coreKpiTypeView);
//        when(cacheWrapper.getAllViewTypesIdMap()).thenReturn(viewTypesIdMap);
//
//        HashMap<Pair<String, String>, ViewTypesBean> viewTypesPairMap = new HashMap<>();
//        viewTypesPairMap.put(Pair.of(Constants.THRESHOLD_SEVERITY_TYPE, Constants.THRESHOLD_SEVERITY_TYPE_LOW), lowThresholdViewType);
//        viewTypesPairMap.put(Pair.of(Constants.THRESHOLD_SEVERITY_TYPE, Constants.THRESHOLD_SEVERITY_TYPE_MEDIUM), mediumThresholdViewType);
//        viewTypesPairMap.put(Pair.of(Constants.THRESHOLD_SEVERITY_TYPE, Constants.THRESHOLD_SEVERITY_TYPE_HIGH), highThresholdViewType);
//        viewTypesPairMap.put(Pair.of(Constants.OPERATIONS_TYPE, Constants.OPERATIONS_TYPE_LESSER_THAN), lesserOperationType);
//        viewTypesPairMap.put(Pair.of(Constants.OPERATIONS_TYPE, Constants.OPERATIONS_TYPE_GREATER_THAN), greaterOperationType);
//        viewTypesPairMap.put(Pair.of(Constants.OPERATIONS_TYPE, Constants.OPERATIONS_TYPE_NOT_BETWEEN), notBetweenOperationType);
//        viewTypesPairMap.put(Pair.of(Constants.KPI_TYPE, "validKpiType"), kpiTypeView);
//        viewTypesPairMap.put(Pair.of(Constants.KPI_TYPE, Constants.CORE_KPI_TYPE), coreKpiTypeView);
//
//        when(cacheWrapper.getAllViewTypesPairMap()).thenReturn(viewTypesPairMap);
//        when(commonUtils.getUserId(anyString())).thenReturn("testUserId");
//    }
//
//    @Test
//    void testClientValidation_ValidInputs() throws ClientException {
//        String authKey = "validAuthKey";
//        String accountIdentifier = "validAccount";
//        String serviceIdentifier = "validService";
//        String kpiType = "validKpiType";
//        String thresholdType = "static";
//        String isSystem = "true";
//
//        List<StaticThresholdRules> sorRuleList = new ArrayList<>();
//        sorRuleList.add(new StaticThresholdRules());
//
//        String[] requestParams = {authKey, accountIdentifier, serviceIdentifier, kpiType, thresholdType, isSystem};
//
//        UtilityBean<List<StaticThresholdRules>> result = postServiceStaticThresholdBL.clientValidation(sorRuleList, requestParams);
//
//        assertNotNull(result);
//        assertEquals(accountIdentifier, result.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER));
//        assertEquals(serviceIdentifier, result.getRequestParams().get(Constants.SERVICE_IDENTIFIER));
//        assertEquals(kpiType, result.getRequestParams().get(Constants.KPI_TYPE));
//        assertEquals(thresholdType, result.getRequestParams().get(Constants.THRESHOLD_TYPE));
//    }
//
//    @Test
//    void testClientValidation_InvalidThresholdType() {
//        String[] requestParams = {"validAuthKey", "validAccount", "validService", "validKpiType", "", "true"};
//        List<StaticThresholdRules> sorRuleList = new ArrayList<>();
//        sorRuleList.add(new StaticThresholdRules());
//
//        ClientException exception = assertThrows(ClientException.class, () ->
//                postServiceStaticThresholdBL.clientValidation(sorRuleList, requestParams));
//
//        assertEquals("ClientException : " + UIMessages.THRESHOLD_TYPE_INVALID, exception.getMessage());
//    }
//
//    @Test
//    void testClientValidation_EmptyStaticThresholdRules() {
//        String[] requestParams = {"validAuthKey", "validAccount", "validService", "validKpiType", "static", "true"};
//        List<StaticThresholdRules> sorRuleList = new ArrayList<>();
//
//        ClientException exception = assertThrows(ClientException.class, () ->
//                postServiceStaticThresholdBL.clientValidation(sorRuleList, requestParams));
//
//        assertEquals("ClientException : Static thresholds are unavailable", exception.getMessage());
//    }
//
//    @Test
//    void testClientValidation_DuplicateStaticThresholdRules() {
//        String[] requestParams = {"validAuthKey", "validAccount", "validService", "validKpiType", "static", "true"};
//        StaticThresholdRules rule = new StaticThresholdRules();
//        List<StaticThresholdRules> sorRuleList = Arrays.asList(rule, rule);
//
//        ClientException exception = assertThrows(ClientException.class, () ->
//                postServiceStaticThresholdBL.clientValidation(sorRuleList, requestParams));
//
//        assertEquals("ClientException : duplicate static thresholds are defined for the same KPI", exception.getMessage());
//    }
//
//    // server validation test cases
//    @Test
//    void testServerValidation_ValidInputs() throws ServerException, ControlCenterException {
//        UtilityBean<List<StaticThresholdRules>> utilityBean = UtilityBean.<List<StaticThresholdRules>>builder()
//                .requestParams(new HashMap<String,String>() {{
//                    put(Constants.KPI_TYPE, "validKpiType");
//                    put(Constants.ACCOUNT_IDENTIFIER, "validAccount");
//                    put(Constants.SERVICE_IDENTIFIER, "validService");
//                }})
//                .userId("testUserId")
//                .pojoObject(Collections.emptyList())
//                .build();
//
//        Account accountBean = new Account();
//        accountBean.setIdentifier("validAccount");
//        accountBean.setName("Valid Account");
//        accountBean.setId(1);
//        accountBean.setLastModifiedBy("testUserId");
//        Service controllerBean = new Service();
//        controllerBean.setId(1);
//        com.heal.configuration.entities.UserAccessDetails userAccessDetails = new UserAccessDetails();
//
//        when(serverValidationUtils.accountValidation("validAccount")).thenReturn(accountBean);
//        when(serverValidationUtils.serviceValidation(anyString(), anyString(), any())).thenReturn(controllerBean);
//        when(serverValidationUtils.userAccessDetailsValidation("testUserId", "validAccount")).thenReturn(userAccessDetails);
//
//        UtilityBean<List<StaticThresholdRules>> result = postServiceStaticThresholdBL.serverValidation(utilityBean);
//
//        assertNotNull(result);
//        assertEquals("validAccount", result.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER));
//        assertEquals("validService", result.getRequestParams().get(Constants.SERVICE_IDENTIFIER));
//    }
//
//    @Test
//    void testServerValidation_InvalidKpiType() throws ServerException {
//        UtilityBean<List<StaticThresholdRules>> utilityBean = UtilityBean.<List<StaticThresholdRules>>builder()
//                .requestParams(new HashMap<String,String>() {{
//                    put(Constants.KPI_TYPE, "invalidKpiType");
//                    put(Constants.ACCOUNT_IDENTIFIER, "validAccount");
//                    put(Constants.SERVICE_IDENTIFIER, "validService");
//                }})
//                .userId("testUserId")
//                .pojoObject(Collections.emptyList())
//                .build();
//
//        Account accountBean = new Account();
//        accountBean.setIdentifier("validAccount");
//        accountBean.setName("Valid Account");
//        accountBean.setId(1);
//        accountBean.setLastModifiedBy("testUserId");
//        Service controllerBean = new Service();
//        controllerBean.setId(1);
//        com.heal.configuration.entities.UserAccessDetails userAccessDetails = new UserAccessDetails();
//
//        when(serverValidationUtils.accountValidation("validAccount")).thenReturn(accountBean);
//        when(serverValidationUtils.serviceValidation(anyString(), anyString(), any())).thenReturn(controllerBean);
//        when(serverValidationUtils.userAccessDetailsValidation("testUserId", "validAccount")).thenReturn(userAccessDetails);
//
//        when(cacheWrapper.getAllViewTypesPairMap()).thenReturn(new HashMap<>());
//
//        ServerException exception = assertThrows(ServerException.class, () ->
//                postServiceStaticThresholdBL.serverValidation(utilityBean));
//
//        assertEquals("ServerException : Invalid kpiType. Reason: kpiType is undefined in the request.", exception.getMessage());
//    }
//
//
////     Process test
//    @Test
//    void testProcess_ValidInputs() throws ControlCenterException, DataProcessingException {
//        StaticThresholdRules staticThresholdRules = new StaticThresholdRules();
//        staticThresholdRules.setDataId(1);
//        staticThresholdRules.setKpiId("1");
//        staticThresholdRules.setLowThreshold(ThresholdConfig.builder()
//                .operationType("lesser than").status(1).min(10.0).build());
//        staticThresholdRules.setWarningThreshold(ThresholdConfig.builder()
//                .operationType("greater than").status(1).min(10.0).build());
//        staticThresholdRules.setErrorThreshold(ThresholdConfig.builder()
//                .operationType("greater than").status(1).min(10.0).build());
//        staticThresholdRules.setKpiLevel("clusters");
//        staticThresholdRules.setUserDefinedSOR(true);
//        staticThresholdRules.setGenerateAnomaly(true);
//
//        StaticThresholdRules staticThresholdRules2 = new StaticThresholdRules();
//        staticThresholdRules2.setDataId(2);
//        staticThresholdRules2.setKpiId("2");
//        staticThresholdRules2.setLowThreshold(ThresholdConfig.builder()
//                .operationType("lesser than").status(1).min(10.0).dataId(3).build());
//        staticThresholdRules2.setWarningThreshold(ThresholdConfig.builder()
//                .operationType("greater than").status(1).min(10.0).dataId(4).build());
//        staticThresholdRules2.setErrorThreshold(ThresholdConfig.builder()
//                .operationType("greater than").status(1).min(10.0).dataId(5).build());
//        staticThresholdRules2.setKpiLevel("clusters");
//        staticThresholdRules2.setUserDefinedSOR(false);
//        staticThresholdRules2.setGenerateAnomaly(true);
//
//        UtilityBean<List<StaticThresholdRules>> utilityBean = UtilityBean.<List<StaticThresholdRules>>builder()
//                .requestParams(new HashMap<String,String>() {{
//                    put(Constants.KPI_TYPE, "Core");
//                    put(Constants.ACCOUNT_IDENTIFIER, "validAccount");
//                    put(Constants.SERVICE_IDENTIFIER, "validService");
//                    put(Constants.IS_SYSTEM, "true");
//                }})
//                .userId("testUserId")
//                .pojoObject(Arrays.asList(staticThresholdRules, staticThresholdRules2))
//                .build();
//
//        ControllerBean controllerBean = new ControllerBean();
//        controllerBean.setId(1);
//        controllerBean.setIdentifier("validService");
//        controllerBean.setAccountId(1);
//
//        ServiceKpiThreshold serviceKpiThreshold = new ServiceKpiThreshold();
//        serviceKpiThreshold.setId(1);
//        serviceKpiThreshold.setKpiId(1);
//        serviceKpiThreshold.setApplicableTo("ALL");
//        serviceKpiThreshold.setKpiAttribute("clusters");
//        serviceKpiThreshold.setStatus(1);
//        serviceKpiThreshold.setThresholdSeverityId(1);
//        serviceKpiThreshold.setOperationTypeId(5);
//        serviceKpiThreshold.setMinThreshold(10.0);
//
//        ServiceKpiThreshold serviceKpiThreshold1 = new ServiceKpiThreshold();
//        serviceKpiThreshold1.setId(1);
//        serviceKpiThreshold1.setKpiId(1);
//        serviceKpiThreshold1.setApplicableTo("ALL");
//        serviceKpiThreshold1.setKpiAttribute("clusters");
//        serviceKpiThreshold1.setStatus(1);
//        serviceKpiThreshold1.setThresholdSeverityId(2);
//        serviceKpiThreshold1.setOperationTypeId(7);
//        serviceKpiThreshold1.setMinThreshold(10.0);
//
//        ServiceKpiThreshold serviceKpiThreshold2 = new ServiceKpiThreshold();
//        serviceKpiThreshold2.setId(1);
//        serviceKpiThreshold2.setKpiId(1);
//        serviceKpiThreshold2.setApplicableTo("ALL");
//        serviceKpiThreshold2.setKpiAttribute("clusters");
//        serviceKpiThreshold2.setStatus(1);
//        serviceKpiThreshold2.setThresholdSeverityId(3);
//        serviceKpiThreshold2.setOperationTypeId(7);
//        serviceKpiThreshold2.setMinThreshold(10.0);
//
//        KpiDetails kpiDetails = new KpiDetails();
//        KpiViolationConfig kpiViolationConfig = new KpiViolationConfig();
//        kpiViolationConfig.setKpiId(1);
//        kpiViolationConfig.setAttributeValue("clusters");
//        kpiViolationConfig.setStatus(1);
//        kpiViolationConfig.setThresholdSeverityId(1);
//        kpiViolationConfig.setThresholdSeverity("Low");
//        kpiViolationConfig.setOperation("lesser than");
//        kpiViolationConfig.setMinThreshold(10.0);
//
//        KpiViolationConfig kpiViolationConfig1 = new KpiViolationConfig();
//        kpiViolationConfig1.setKpiId(1);
//        kpiViolationConfig1.setAttributeValue("clusters");
//        kpiViolationConfig1.setStatus(1);
//        kpiViolationConfig1.setThresholdSeverityId(2);
//        kpiViolationConfig1.setThresholdSeverity("Medium");
//        kpiViolationConfig1.setOperation("lesser than");
//        kpiViolationConfig1.setMinThreshold(10.0);
//
//        KpiViolationConfig kpiViolationConfig3 = new KpiViolationConfig();
//        kpiViolationConfig3.setKpiId(1);
//        kpiViolationConfig3.setAttributeValue("clusters");
//        kpiViolationConfig3.setStatus(1);
//        kpiViolationConfig3.setThresholdSeverityId(3);
//        kpiViolationConfig3.setThresholdSeverity("High");
//        kpiViolationConfig3.setOperation("lesser than");
//        kpiViolationConfig3.setMinThreshold(10.0);
//
//        Map<String, List<KpiViolationConfig>> kpiViolationConfigMap = new HashMap<>();
//        kpiViolationConfigMap.put("ALL", Arrays.asList(kpiViolationConfig, kpiViolationConfig1, kpiViolationConfig3));
//
//        kpiDetails.setKpiViolationConfig(kpiViolationConfigMap);
//
//        when(controllerDao.getServiceByIdentifier("validService")).thenReturn(controllerBean);
//        when(getServiceStaticThresholdBL.process(any())).thenReturn(Arrays.asList(staticThresholdRules));
//        when(serviceDao.getKpiThresholdsByApplicableToAndAttribute(1,1,"clusters","ALL")).thenReturn(Arrays.asList(serviceKpiThreshold, serviceKpiThreshold1, serviceKpiThreshold2));
//        when(serviceDao.addSystemThreshold(any())).thenReturn(new int[]{});
//        when(serviceDao.addThreshold(any())).thenReturn(new int[]{});
//        doNothing().when(serviceOpensearchRepo).createThreshold(anyString(), anyString(), any(), any(), anyString());
//        when(serviceOpensearchRepo.getServiceLevelThresholdDetails(anyString(), anyString(), anyString(), anyString()))
//                .thenReturn(Collections.emptyList());
//        doNothing().when(serviceOpensearchRepo).updateThreshold(anyString(), anyString(), any(), any(), any());
//        when(serviceDao.updateSystemThreshold(any())).thenReturn(new int[]{});
//        when(serviceDao.updateThreshold(any())).thenReturn(new int[]{});
//        when(serviceRepo.getServiceKPI(anyString(), anyString(), anyInt())).thenReturn(kpiDetails);
//        doNothing().when(serviceRepo).updateServiceKpiById(anyString(), anyString(), anyInt(), any());
//        doNothing().when(serviceRepo).updateServiceKpiByIdentifier(anyString(), anyString(), anyString(), any());
//        when(installationAttributeService.checkForOfflineHeal(anyString())).thenReturn(false);
//        when(dateTimeUtil.getCurrentTimestampInGMT()).thenReturn(new Timestamp(System.currentTimeMillis()));
//
//        List<StaticThresholdRules> result = postServiceStaticThresholdBL.process(utilityBean);
//        assert(result.isEmpty());
//    }
//
//}
