package com.heal.controlcenter.dao.redis;

import com.fasterxml.jackson.core.type.TypeReference;
import com.heal.configuration.pojos.Application;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.controlcenter.util.CommonUtils;
import com.heal.controlcenter.util.RedisUtilities;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Repository
@Slf4j
public class ApplicationRepo {

    private static final String ACCOUNTS_KEY = "/accounts";
    private static final String ACCOUNTS_HASH = "ACCOUNTS";
    private static final String SERVICES_KEY = "/services";
    private static final String SERVICES_HASH = "_SERVICES";
    private static final String APPLICATION_KEY = "/applications";
    private static final String APPLICATION_HASH = "_APPLICATIONS";

    @Autowired
    private RedisUtilities redisUtilities;

    /**
     * Fetches services mapped to a given application from Redis.
     */
    public List<BasicEntity> getServicesMappedToApplication(String accountIdentifier, String applicationIdentifier) {
        String key = ACCOUNTS_KEY + "/" + accountIdentifier + APPLICATION_KEY + "/" + applicationIdentifier + SERVICES_KEY;
        String hashKey = ACCOUNTS_HASH + "_" + accountIdentifier + APPLICATION_HASH + "_" + applicationIdentifier + SERVICES_HASH;

        try {
            String serviceApplicationDetails = redisUtilities.getKey(key, hashKey);
            if (serviceApplicationDetails == null) {
                log.debug("No services mapped to application [{}] for account [{}]", applicationIdentifier, accountIdentifier);
                return new ArrayList<>();
            }

            return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(
                    serviceApplicationDetails, new TypeReference<List<BasicEntity>>() {
                    }
            );
        } catch (Exception e) {
            log.error("Error while fetching services for application [{}], account [{}]", applicationIdentifier, accountIdentifier, e);
            return new ArrayList<>();
        }
    }

    /**
     * Updates Redis with service list mapped to a given application.
     */
    public void updateServiceApplication(String accountIdentifier, String applicationIdentifier, List<BasicEntity> serviceDetails) {
        String key = ACCOUNTS_KEY + "/" + accountIdentifier + APPLICATION_KEY + "/" + applicationIdentifier + SERVICES_KEY;
        String hashKey = ACCOUNTS_HASH + "_" + accountIdentifier + APPLICATION_HASH + "_" + applicationIdentifier + SERVICES_HASH;

        try {
            redisUtilities.updateKey(key, hashKey, serviceDetails);
        } catch (Exception e) {
            log.error("Error while updating services for application [{}], account [{}]", applicationIdentifier, accountIdentifier, e);
        }
    }

    /**
     * Updates Redis with the list of applications for a given account.
     */
    public void updateApplicationDetailsForAccount(String accountIdentifier, List<Application> applicationsList) {
        try {
            redisUtilities.updateKey(ACCOUNTS_KEY + "/" + accountIdentifier + APPLICATION_KEY,
                    ACCOUNTS_HASH + "_" + accountIdentifier + APPLICATION_HASH, applicationsList);
        } catch (Exception e) {
            log.error("Error occurred while updating application Details for the accountIdentifier: {} ", accountIdentifier, e);
        }
    }

    /**
     * Updates Redis with a single application for a given account.
     */
    public void updateApplication(String accountIdentifier, Application application) {
        try {
            redisUtilities.updateKey(ACCOUNTS_KEY + "/" + accountIdentifier + APPLICATION_KEY + "/" + application.getIdentifier(),
                    ACCOUNTS_HASH + "_" + accountIdentifier + APPLICATION_HASH + "_" + application.getIdentifier(), application);
        } catch (Exception e) {
            log.error("Error occurred while updating application Details for the accountIdentifier: {} and applicationIdentifier: {} ", accountIdentifier, application.getIdentifier(), e);
        }
    }

    /**
     * Retrieves the list of applications for a given account from Redis.
     */
    public List<Application> getApplicationsForAccount(String accountIdentifier) {
        try {
            String serviceApplicationDetails = redisUtilities.getKey(ACCOUNTS_KEY + "/" + accountIdentifier + APPLICATION_KEY,
                    ACCOUNTS_HASH + "_" + accountIdentifier + APPLICATION_HASH);
            if (serviceApplicationDetails == null) {
                log.debug("Applications details not found for the accountIdentifier : [{}]", accountIdentifier);
                return Collections.emptyList();
            }
            return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(serviceApplicationDetails, new TypeReference<List<Application>>() {
            });
        } catch (Exception e) {
            log.error("Error occurred while getting  application details for accountIdentifier:{}.", accountIdentifier, e);
            return Collections.emptyList();
        }
    }
}
