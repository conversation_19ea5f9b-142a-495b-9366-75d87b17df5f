package com.heal.controlcenter.util;

import com.heal.controlcenter.exception.ClientException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class ClientValidationUtils {

    public void nullOrEmptyCheck(String value, String errorMessage) throws ClientException {
        if (value == null || value.trim().isEmpty()) {
            log.error(errorMessage);
            throw new ClientException(errorMessage);
        }
    }

    public void accountIdentifierValidation(String accountIdentifier) throws ClientException {
        if (accountIdentifier == null || accountIdentifier.trim().isEmpty()) {
            log.error(UIMessages.ACCOUNT_IDENTIFIER_INVALID);
            throw new ClientException(UIMessages.ACCOUNT_IDENTIFIER_INVALID);
        }
    }

    public void serviceIdentifierValidation(String serviceIdentifier) throws ClientException {
        if (serviceIdentifier == null || serviceIdentifier.trim().isEmpty()) {
            log.error(UIMessages.SERVICE_IDENTIFIER_INVALID);
            throw new ClientException(UIMessages.SERVICE_IDENTIFIER_INVALID);
        }
    }

    public void instanceIdentifierValidation(String instanceIdentifier) throws ClientException {
        if (instanceIdentifier == null || instanceIdentifier.trim().isEmpty()) {
            log.error(UIMessages.INSTANCE_IDENTIFIER_INVALID);
            throw new ClientException(UIMessages.INSTANCE_IDENTIFIER_INVALID);
        }
    }

    /**
     * TODO: Remove this method after all existing API usages are migrated to AOP-based authentication.
     * Validates the authentication key for null or empty values.
     *
     * @param authKey the authentication key to validate
     * @throws ClientException if the authKey is null or empty
     */
    public void authKeyValidation(String authKey) throws ClientException {
        if (authKey == null || authKey.trim().isEmpty()) {
            log.error(UIMessages.AUTH_KEY_INVALID);
            throw new ClientException(UIMessages.AUTH_KEY_INVALID);
        }
    }
}
