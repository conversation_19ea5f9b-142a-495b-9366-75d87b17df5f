package com.heal.controlcenter.dao.mysql;

import com.heal.controlcenter.beans.AutoDiscoveryDiscoveredConnectionsBean;
import com.heal.controlcenter.beans.AutoDiscoveryKnownComponentAttributesBean;
import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.enums.Entity;
import com.heal.controlcenter.enums.Environment;
import com.heal.controlcenter.pojo.HostProcessCountPojo;
import com.heal.controlcenter.pojo.InstanceComponentMappingDetails;
import com.heal.controlcenter.pojo.InstanceServiceApplicationDetailsPojo;
import com.heal.controlcenter.exception.HealControlCenterException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.Collections;
import java.util.List;

@Slf4j
@Repository
public class AutoDiscoveryDao {

    @Autowired
    JdbcTemplate jdbcTemplate;

    public List<AutoDiscoveryDiscoveredConnectionsBean> getDiscoveredConnectionsList() throws HealControlCenterException {
        String query = "select adc.id, adc.source_identifier sourceIdentifier, adc.destination_identifier destinationIdentifier, ah.last_discovery_run_time " +
                "lastUpdatedTime, adc.is_discovery isDiscovery, adc.discovery_status discoveryStatus, c1.id sourceId, c1.name sourceName, c2.id destinationId, c2.name destinationName " +
                "from autodisco_discovered_connections adc, autodisco_host ah, controller c1, controller c2 " +
                "where adc.host_identifier = ah.host_identifier and ah.is_ignored = 0 and c1.identifier=adc.source_identifier and c2.identifier=adc.destination_identifier";
        try {
            log.debug("getting discovered connections [Auto Discovery].");
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(AutoDiscoveryDiscoveredConnectionsBean.class));
        } catch (Exception e) {
            log.error("Error occurred while fetching connections from 'autodisco_discovered_connections' table. Details: ", e);
            throw new HealControlCenterException("Error occurred while fetching discovered connections.");
        }
    }

    public List<AutoDiscoveryHostBean> getIgnoredHosts() throws HealControlCenterException {
        String query = "SELECT hostname hostName, host_identifier hostIdentifier, last_discovery_run_time lastDiscoveryRunTime, " +
                "last_updated_time lastUpdatedTime, ignored_by ignoredBy FROM autodisco_host " +
                "WHERE is_ignored = 1";
        try {
            log.debug("getting ignored hosts [Auto Discovery].");
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(AutoDiscoveryHostBean.class));
        } catch (Exception e) {
            log.error("Error occurred while fetching ignored hosts from 'autodisco_host' table. Details: ", e);
            throw new HealControlCenterException("Error occurred while fetching ignored hosts.");
        }
    }

    public List<AutoDiscoveryDiscoveredAttributesBean> getDiscoveredAttributesList() throws HealControlCenterException {
        String query = "SELECT id, discovered_attributes_identifier discoveredAttributesIdentifier, entity_type entityType, attribute_name attributeName, " +
                "attribute_value attributeValue FROM autodisco_discovered_attributes";
        try {
            log.debug("getting discovered attributes [Auto Discovery].");
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(AutoDiscoveryDiscoveredAttributesBean.class));
        } catch (Exception e) {
            log.error("Error occurred while fetching discovered attributes from 'autodisco_discovered_attributes' table. Details: ", e);
            throw new HealControlCenterException("Error occurred while fetching discovered attributes.");
        }
    }

    public List<AutoDiscoveryProcessBean> getIgnoredProcesses() throws HealControlCenterException {
        String query = "SELECT adp.process_name processName, adp.process_identifier processIdentifier, adh.last_discovery_run_time lastDiscoveryRunTime, " +
                "adp.last_updated_time lastUpdatedTime, adp.ignored_by ignoredBy, adp.host_identifier hostIdentifier " +
                "FROM autodisco_process adp JOIN autodisco_host adh ON adh.host_identifier = adp.host_identifier " +
                "WHERE adp.is_ignored = 1 AND adp.component_id != 0 AND adp.is_blacklisted = '0'";
        try {
            log.debug("getting ignored processes [Auto Discovery].");
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(AutoDiscoveryProcessBean.class));
        } catch (Exception e) {
            log.error("Error occurred while fetching ignored processes from 'autodisco_process' table. Details: ", e);
            throw new HealControlCenterException("Error occurred while fetching ignored processes.");
        }
    }

    public List<AutoDiscoveryHostBean> getHostList(List<String> validHostIds) throws HealControlCenterException {
        String query = "SELECT host_identifier hostIdentifier, operating_system operatingSystem, serial_number operatingSystemVersion, hostname, platform, " +
                "last_updated_time lastUpdatedTime, environment, last_discovery_run_time lastDiscoveryRunTime, discovery_status discoveryStatus, " +
                "is_ignored isIgnored FROM autodisco_host where host_identifier in (?)";
        try {
            log.debug("getting hosts [Auto Discovery].");
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(AutoDiscoveryHostBean.class), validHostIds);
        } catch (Exception e) {
            log.error("Error occurred while fetching hosts from 'autodisco_host' table. Details: ", e);
            throw new HealControlCenterException("Error occurred while fetching hosts.");
        }
    }

    public List<AutoDiscoveryProcessBean> getProcessList() throws HealControlCenterException {
        String query = "SELECT process_identifier processIdentifier, host_identifier hostIdentifier, process_name processName, process_args processArgs, " +
                "process_id pid, last_updated_time lastUpdatedTime, is_blacklisted isBlacklisted, component_id componentId, " +
                "component_version_id componentVersionId, component_type_id componentTypeId, current_working_directory processCurrentWorkingDirectory, " +
                "discovery_status discoveryStatus FROM autodisco_process WHERE component_id != 0";
        try {
            log.debug("getting processes [Auto Discovery].");
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(AutoDiscoveryProcessBean.class));
        } catch (Exception e) {
            log.error("Error occurred while fetching processes from 'autodisco_process' table. Details: ", e);
            throw new HealControlCenterException("Error occurred while fetching processes.");
        }
    }

    public List<AutoDiscoveryKnownComponentAttributesBean> getADComponentAttributeDetails() throws HealControlCenterException {
        String query = "SELECT DISTINCT mc.id id, mc.name name, mcadm.mst_component_type_id mstComponentTypeId, mcadm.path relativePath, mc.discovery_pattern discoveryPattern, mca.attribute_name attributeName, is_mandatory isMandatory, mcadm.method, mcadm.value, mcadm.priority " +
                "FROM (mst_component_attribute_auto_discovery_mapping mcadm JOIN mst_component mc on mc.id = mcadm.mst_component_id) " +
                "JOIN mst_common_attributes mca on mcadm.mst_common_attributes_id = mca.id " +
                "WHERE (mst_component_type_id = 1 or mst_component_type_id = 2 or mst_component_type_id = 3 or mst_component_type_id = 4 or mst_component_type_id = 5 or mst_component_type_id = 7) and path is not null and discovery_pattern is not null ORDER BY id, attributeName";
        try {
            log.debug("getting component attribute details [Auto Discovery].");
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(AutoDiscoveryKnownComponentAttributesBean.class));
        } catch (Exception e) {
            log.error("Error occurred while fetching component attribute details from 'mst_component_attribute_auto_discovery_mapping' table. Details: ", e);
            throw new HealControlCenterException("Error occurred while fetching component attribute details.");
        }
    }

    public List<AutoDiscoveryServiceMappingBean> getServiceMappingList() throws HealControlCenterException {
        String query = "SELECT service_mapping_identifier serviceMappingIdentifier, service_identifier serviceIdentifier, entity_type entityType " +
                "FROM autodisco_service_mapping";
        try {
            log.debug("getting service mappings [Auto Discovery].");
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(AutoDiscoveryServiceMappingBean.class));
        } catch (Exception e) {
            log.error("Error occurred while fetching service mappings from 'autodisco_service_mapping' table. Details: ", e);
            throw new HealControlCenterException("Error occurred while fetching service mappings.");
        }
    }

    public List<EndpointBean> getEndpoints() throws HealControlCenterException {
        String query = "SELECT ip ipAddress, port portNo, host_identifier hostIdentifier FROM autodisco_endpoint";
        try {
            log.debug("getting endpoints [Auto Discovery].");
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(EndpointBean.class));
        } catch (Exception e) {
            log.error("Error occurred while fetching endpoints from 'autodisco_endpoint' table. Details: ", e);
            throw new HealControlCenterException("Error occurred while fetching endpoints.");
        }
    }

    public List<NetworkInterfaceBean> getNetworkInterfacesList() throws HealControlCenterException {
        String query = "SELECT network_identifier networkInterfaceIdentifier, host_identifier hostIdentifier, interface_name interfaceName, " +
                "ip_address interfaceIP, interface_type interfaceType, hardware_address hardwareAddress FROM autodisco_network_interfaces";
        try {
            log.debug("getting network interfaces [Auto Discovery].");
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(NetworkInterfaceBean.class));
        } catch (Exception e) {
            log.error("Error occurred while fetching network interfaces from 'autodisco_network_interfaces' table. Details: ", e);
            throw new HealControlCenterException("Error occurred while fetching network interfaces.");
        }
    }

    public List<AutoDiscoveryNetworkConnectionsBean> getNetworkConnections() throws HealControlCenterException {
        String query = "SELECT connection_identifier connectionIdentifier, host_identifier hostIdentifier, direction, local_ip localIP, local_port localPort, " +
                "remote_ip remoteIP, remote_port remotePort, last_updated_time lastUpdatedTime FROM autodisco_network_connection";
        try {
            BeanPropertyRowMapper<AutoDiscoveryNetworkConnectionsBean> beanBeanPropertyRowMapper = new BeanPropertyRowMapper<>(AutoDiscoveryNetworkConnectionsBean.class);
            beanBeanPropertyRowMapper.setPrimitivesDefaultedForNullValue(true);
            log.debug("getting network connections [Auto Discovery].");
            return jdbcTemplate.query(query, beanBeanPropertyRowMapper);
        } catch (Exception e) {
            log.error("Error occurred while fetching network connections from 'autodisco_network_connection' table. Details: ", e);
            throw new HealControlCenterException("Error occurred while fetching network connections.");
        }
    }

    // If hosts service is modified
    public void deleteExistingMappingsAndConnections(String validEntity, List<String> existingCompMappings) throws HealControlCenterException {
        try {
            if (!existingCompMappings.isEmpty()) {
                deleteHostMapKeepCompMappings(validEntity, existingCompMappings);
            } else {
                deleteServiceMappings(validEntity);
            }
            deleteDiscoveredConnections(validEntity);
        } catch (Exception e) {
            throw new HealControlCenterException(e.getMessage());
        }
    }

    void deleteHostMapKeepCompMappings(String validEntity, List<String> existingCompMappings) throws HealControlCenterException {
        String query = "DELETE FROM autodisco_service_mapping " +
                "WHERE service_mapping_identifier = ? AND service_identifier NOT IN (?)";
        try {
            log.debug("deleting service mapping [Auto Discovery].");
            jdbcTemplate.update(query, validEntity, existingCompMappings.toString());
        } catch (Exception e) {
            log.error("Exception encountered while deleting service mapping from 'autodisco_service_mapping' table. Details: ", e);
            throw new HealControlCenterException("Error occurred while deleting service mapping(s).");
        }
    }

    // If component instance service is modified
    public void deleteExistingMappingsAndConnections(String validEntity, List<String> mappings, String hostIdentifier) throws HealControlCenterException {
        try {
            deleteServiceMappings(validEntity);
            deleteHostMapToCompService(hostIdentifier, mappings);
            deleteDiscoveredConnections(hostIdentifier);
        } catch (Exception e) {
            throw new HealControlCenterException(e.getMessage());
        }
    }

    public void deleteServiceMappings(String validEntity) throws HealControlCenterException {
        String query = "DELETE FROM autodisco_service_mapping WHERE service_mapping_identifier = ?";
        try {
            log.debug("deleting service mapping [Auto Discovery].");
            jdbcTemplate.update(query, validEntity);
        } catch (Exception e) {
            log.error("Exception encountered while deleting service mapping from 'autodisco_service_mapping' table. Details: ", e);
            throw new HealControlCenterException("Error occurred while deleting service mapping(s).");
        }
    }

    public void deleteHostMapToCompService(String hostIdentifier, List<String> mappings) throws HealControlCenterException {
        String query = "DELETE FROM autodisco_service_mapping " +
                "WHERE service_mapping_identifier = ? AND service_identifier = ? LIMIT 1";
        try {
            log.debug("deleting host service mappings [Auto Discovery].");
            jdbcTemplate.batchUpdate(query, new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps, int i) throws SQLException {
                    String map = mappings.get(i);
                    ps.setString(1, hostIdentifier);
                    ps.setString(2, map);
                }
                @Override
                public int getBatchSize() {
                    return mappings.size();
                }
            });
        } catch (Exception e) {
            log.error("Exception encountered while deleting service mappings from 'autodisco_service_mapping' table. Details: ", e);
            throw new HealControlCenterException("Error occurred while deleting service mapping(s).");
        }
    }

    public void deleteDiscoveredConnections(String validEntity) throws HealControlCenterException {
        String query = "DELETE FROM autodisco_discovered_connections WHERE host_identifier = ?";
        try {
            log.debug("deleting connections [Auto Discovery].");
            jdbcTemplate.update(query, validEntity);
        } catch (Exception e) {
            log.error("Exception encountered while deleting connections from 'autodisco_discovered_connections' table. Details: ", e);
            throw new HealControlCenterException("Error occurred while deleting discovered connections.");
        }
    }

    public void addDiscoveredConnection(List<AutoDiscoveryDiscoveredConnectionsBean> discoveredConnectionsList) throws HealControlCenterException {
        String query = "INSERT INTO autodisco_discovered_connections (source_identifier, host_identifier, destination_identifier, " +
                "last_updated_time, is_discovery) VALUES (?, ?, ?, ?, ?)";
        try {
            log.debug("adding discovered connection(s) [Auto Discovery].");
            jdbcTemplate.batchUpdate(query, new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps, int i) throws SQLException {
                    AutoDiscoveryDiscoveredConnectionsBean discoveredConnectionsBean = discoveredConnectionsList.get(i);
                    ps.setString(1, discoveredConnectionsBean.getSourceIdentifier());
                    ps.setString(2, discoveredConnectionsBean.getHostIdentifier());
                    ps.setString(3, discoveredConnectionsBean.getDestinationIdentifier());
                    ps.setLong(4, discoveredConnectionsBean.getLastUpdatedTime());
                    ps.setInt(5, discoveredConnectionsBean.getIsDiscovery());
                }

                @Override
                public int getBatchSize() {
                    return discoveredConnectionsList.size();
                }
            });
        } catch (Exception e) {
            log.error("Exception encountered while adding discovered connection(s) to 'autodisco_discovered_connections' table. Details: ", e);
            throw new HealControlCenterException("Error occurred while adding discovered connection(s).");
        }
    }

    public void addServiceMapping(List<AutoDiscoveryServiceMappingBean> serviceMappingBeanList) throws HealControlCenterException {
        String query = "INSERT INTO autodisco_service_mapping (service_mapping_identifier, service_identifier, entity_type) " +
                "VALUES (?, ?, ?)";
        try {
            log.debug("adding service mapping(s) [Auto Discovery].");
            jdbcTemplate.batchUpdate(query, new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps, int i) throws SQLException {
                    AutoDiscoveryServiceMappingBean serviceMappingBean = serviceMappingBeanList.get(i);
                    ps.setString(1, serviceMappingBean.getServiceMappingIdentifier());
                    ps.setString(2, serviceMappingBean.getServiceIdentifier());
                    ps.setString(3, serviceMappingBean.getEntityType().name());
                }

                @Override
                public int getBatchSize() {
                    return serviceMappingBeanList.size();
                }
            });
        } catch (Exception e) {
            log.error("Exception encountered while adding service mapping(s) to 'autodisco_service_mapping' table. Details: ", e);
            throw new HealControlCenterException("Error occurred while adding service mapping(s).");
        }
    }

    public void setHostLastUpdatedTime(List<AutoDiscoveryServiceMappingBean> serviceMappingBeanList, long time) throws HealControlCenterException {
        String query = "UPDATE autodisco_host SET last_updated_time = ? WHERE host_identifier = ?";
        try {
            log.debug("updating host(s) last updated time [Auto Discovery].");
            jdbcTemplate.batchUpdate(query, new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps, int i) throws SQLException {
                    AutoDiscoveryServiceMappingBean serviceMappingBean = serviceMappingBeanList.get(i);
                    ps.setLong(1, time);
                    ps.setString(2, serviceMappingBean.getServiceMappingIdentifier());
                }

                @Override
                public int getBatchSize() {
                    return serviceMappingBeanList.size();
                }
            });
        } catch (Exception e) {
            log.error("Exception encountered while updating last updated time in 'autodisco_host' table. Details: ", e);
            throw new HealControlCenterException("Error occurred while updating last updated time of host(s).");
        }
    }

    public void setNetworkConnectionsLastUpdatedTime(List<AutoDiscoveryServiceMappingBean> serviceMappingBeanList, long time) throws HealControlCenterException {
        String query = "UPDATE autodisco_network_connection SET last_updated_time = ? WHERE host_identifier = ?";
        try {
            log.debug("updating network connection(s) last updated time [Auto Discovery].");
            jdbcTemplate.batchUpdate(query, new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps, int i) throws SQLException {
                    AutoDiscoveryServiceMappingBean serviceMappingBean = serviceMappingBeanList.get(i);
                    ps.setLong(1, time);
                    ps.setString(2, serviceMappingBean.getServiceMappingIdentifier());
                }

                @Override
                public int getBatchSize() {
                    return serviceMappingBeanList.size();
                }
            });
        } catch (Exception e) {
            log.error("Exception encountered while updating last updated time in 'autodisco_network_connection' table. Details: ", e);
            throw new HealControlCenterException("Error occurred while updating last updated time of network connection(s).");
        }
    }

    public void setCompInstanceLastUpdateTime(List<AutoDiscoveryServiceMappingBean> serviceMappingBeanList, long time) throws HealControlCenterException {
        String query = "UPDATE autodisco_process SET last_updated_time = ? WHERE process_identifier = ?";
        try {
            log.debug("updating process(s) last updated time [Auto Discovery].");
            jdbcTemplate.batchUpdate(query, new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps, int i) throws SQLException {
                    AutoDiscoveryServiceMappingBean serviceMappingBean = serviceMappingBeanList.get(i);
                    if (!serviceMappingBean.getEntityType().equals(Entity.Host)) {
                        ps.setLong(1, time);
                        ps.setString(2, serviceMappingBean.getServiceMappingIdentifier());
                    }
                }

                @Override
                public int getBatchSize() {
                    return serviceMappingBeanList.size();
                }
            });
        } catch (Exception e) {
            log.error("Exception encountered while updating last updated time in 'autodisco_process' table. Details: ", e);
            throw new HealControlCenterException("Error occurred while updating last updated time of process(s).");
        }
    }

    public void setEnvironment(List<String> validEntities, Environment environment) throws HealControlCenterException {
        String query = "UPDATE autodisco_host SET environment = ? WHERE host_identifier = ?";
        try {
            log.debug("updating host(s) environment [Auto Discovery].");
            jdbcTemplate.batchUpdate(query, new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps, int i) throws SQLException {
                    String validEntity = validEntities.get(i);
                    ps.setString(1, environment.toString());
                    ps.setString(2, validEntity);
                }

                @Override
                public int getBatchSize() {
                    return validEntities.size();
                }
            });
        } catch (Exception e) {
            log.error("Exception encountered while updating environment in 'autodisco_host' table. Details: ", e);
            throw new HealControlCenterException("Error occurred while updating environment of host(s).");
        }
    }

    public List<HostWiseConnections> getNumberOfConnectionsForHostIpAddress(int accountId, int healHealthAccountId) throws HealControlCenterException {
        String query = "SELECT local_ip AS hostAddress, " +
                "COUNT(DISTINCT remote_ip) AS noOfConnections, " +
                "GROUP_CONCAT(DISTINCT remote_ip) AS listOfRemoteIps " +
                "FROM autodisco_network_connection " +
                "WHERE local_ip NOT IN ('127.0.0.1', '::', '0.0.0.0') " +
                "AND remote_ip NOT IN ('127.0.0.1', '::', '0.0.0.0') " +
                "AND remote_ip NOT IN (SELECT host_address FROM comp_instance " +
                "WHERE is_cluster != 1 AND host_id = 0 AND account_id IN (?, ?)) " +
                "GROUP BY local_ip";

        try {
            log.debug("Fetching number of connections for each host IP address.");
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(HostWiseConnections.class),
                    healHealthAccountId, accountId); // Matches ? ? order
        } catch (Exception e) {
            log.error("Error fetching host-wise connections. Details: ", e);
            throw new HealControlCenterException("Error fetching host-wise connections.");
        }
    }

    public List<HostWiseConnections> getNumberOfConnectionsForHostIdentifier(int accountId, int healHealthAccountId) throws HealControlCenterException {
        String query = "SELECT host_identifier AS hostIdentifier, " +
                "COUNT(DISTINCT remote_ip) AS noOfConnections, " +
                "GROUP_CONCAT(DISTINCT remote_ip) AS listOfRemoteIps " +
                "FROM autodisco_network_connection " +
                "WHERE local_ip NOT IN ('127.0.0.1', '::', '0.0.0.0') " +
                "AND remote_ip NOT IN ('127.0.0.1', '::', '0.0.0.0') " +
                "AND remote_ip NOT IN (" +
                "SELECT host_address FROM comp_instance " +
                "WHERE is_cluster != 1 AND host_id = 0 AND account_id IN (?, ?)" +
                ") " +
                "GROUP BY host_identifier";

        try {
            log.debug("Fetching number of connections for each host identifier.");
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(HostWiseConnections.class),
                    healHealthAccountId, accountId); // Order matches the query
        } catch (Exception e) {
            log.error("Error fetching host-wise connections for hostIdentifier. Details: ", e);
            throw new HealControlCenterException("Error fetching host-wise connections for hostIdentifier.");
        }
    }

    public List<HostProcessCountPojo> getNewProcessCount() throws HealControlCenterException {
        String query = "SELECT ah.host_identifier AS hostIdentifier, " +
                "COUNT(DISTINCT ap.process_identifier) AS countOfNewProcess " +
                "FROM autodisco_host ah " +
                "JOIN autodisco_process ap ON ah.host_identifier = ap.host_identifier " +
                "WHERE ah.is_ignored = 0 " +
                "AND ah.discovery_status = 'ADDED_TO_SYSTEM' " +
                "AND ap.discovery_status = 'DISCOVERED_NOT_ADDED_TO_SYSTEM' " +
                "AND ap.is_blacklisted = '0' " +
                "AND ap.component_id != 0 " +
                "AND ap.is_ignored = 0 " +
                "GROUP BY ah.host_identifier";

        try {
            log.debug("Fetching new process count per host [Auto Discovery]");
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(HostProcessCountPojo.class));
        } catch (Exception e) {
            log.error("Error while fetching new process count from AutoDiscovery. Details:", e);
            throw new HealControlCenterException("Error while fetching new process count from AutoDiscovery.");
        }
    }

    public List<InstanceServiceApplicationDetailsPojo> getHostInstanceServiceAppDetails() throws HealControlCenterException {
        String query = "SELECT ah.hostname AS instanceName, " +
                "ah.host_identifier AS instanceIdentifier, " +
                "ada.attribute_value AS hostAddress, " +
                "ah.environment AS isDR, " +
                "ah.discovery_status AS status, " +
                "ah.last_discovery_run_time AS lastDiscoveryRunTime, " +
                "vc.component_id AS componentId, " +
                "ah.operating_system AS componentName, " +
                "vc.component_version_id AS componentVersionId, " +
                "ah.serial_number AS componentVersionName, " +
                "vc.common_version_id AS commonVersionId, " +
                "vc.common_version_name AS commonVersionName, " +
                "vc.component_type_id AS componentTypeId, " +
                "vc.component_type_name AS componentTypeName, " +
                "vasm.service_id AS serviceId, " +
                "vasm.service_name AS serviceName, " +
                "asm.service_identifier AS serviceIdentifier, " +
                "vasm.application_id AS applicationId, " +
                "vasm.application_name AS applicationName, " +
                "vasm.application_identifier AS applicationIdentifier " +
                "FROM autodisco_host ah " +
                "JOIN autodisco_discovered_attributes ada ON ah.host_identifier = ada.discovered_attributes_identifier " +
                "AND LOWER(ada.entity_type) = 'host' AND LOWER(ada.attribute_name) = 'hostaddress' " +
                "LEFT JOIN view_components vc ON vc.component_name = ah.operating_system AND vc.component_version_name = ah.serial_number " +
                "LEFT JOIN autodisco_service_mapping asm ON ah.host_identifier = asm.service_mapping_identifier " +
                "AND LOWER(asm.entity_type) = 'host' " +
                "LEFT JOIN view_application_service_mapping vasm ON asm.service_identifier = vasm.service_identifier " +
                "WHERE ah.is_ignored = 0 AND ah.account_id = 0 AND ah.discovery_status = 'DISCOVERED_NOT_ADDED_TO_SYSTEM'";

        try {
            log.debug("Fetching host instance service-application details [Auto Discovery]");
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(InstanceServiceApplicationDetailsPojo.class));
        } catch (Exception e) {
            log.error("Error occurred while fetching host instance service-application details. Details:", e);
            throw new HealControlCenterException("Error occurred while fetching host instance service-application details.");
        }
    }

    public List<InstanceComponentMappingDetails> getInstCompAttributeMapping() throws HealControlCenterException {
        String query = "SELECT ah.hostname AS hostName, " +
                "ah.host_identifier AS hostIdentifier, " +
                "ap.process_name AS compInstanceName, " +
                "ap.process_identifier AS compInstanceIdentifier, " +
                "ap.component_id AS componentId, " +
                "vc.component_name AS componentName, " +
                "ap.component_version_id AS componentVersionId, " +
                "vc.component_version_name AS componentVersionName, " +
                "vc.common_version_id AS commonVersionId, " +
                "vc.common_version_name AS commonVersionName, " +
                "ap.component_type_id AS componentTypeId, " +
                "vc.component_type_name AS componentTypeName, " +
                "ada.attribute_name AS attributeName, " +
                "ada.attribute_value AS attributeValue " +
                "FROM autodisco_host ah, autodisco_process ap, view_components vc, autodisco_discovered_attributes ada " +
                "WHERE ah.is_ignored = 0 " +
                "AND ah.host_identifier = ap.host_identifier " +
                "AND ap.component_id = vc.component_id " +
                "AND ap.process_identifier = ada.discovered_attributes_identifier " +
                "AND ada.entity_type = 'CompInstance' " +
                "AND ap.component_version_id = vc.component_version_id " +
                "AND ap.component_type_id = vc.component_type_id " +
                "AND ap.is_ignored = 0 " +
                "AND ah.account_id = 0";

        try {
            log.debug("Fetching instance-component-attribute mappings [Auto Discovery]");
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(InstanceComponentMappingDetails.class));
        } catch (Exception e) {
            log.error("Error occurred while fetching instance-component-attribute mappings. Details:", e);
            throw new HealControlCenterException("Error occurred while fetching instance-component-attribute mappings.");
        }
    }

}
