package com.heal.controlcenter.dao.mysql;

import com.heal.configuration.pojos.KpiCategoryDetails;
import com.heal.controlcenter.dao.mysql.entity.KpiDetailsBean;
import com.heal.controlcenter.exception.ControlCenterException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;

@Slf4j
@Repository
public class KPIDao {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    public Integer getKpiCountForAccount(int accountId) throws ControlCenterException {
        try {
            String query = "select count(1) from mst_kpi_details where account_id in (1, " + accountId + ")";
            return jdbcTemplate.queryForObject(query, Integer.class);
        } catch (Exception e) {
            log.error("Exception while getting KPI count for accountId [{}]. Details: ", accountId, e);
            throw new ControlCenterException("Error occurred while getting KPI count.");
        }
    }

    public List<KpiDetailsBean> getKpiList(int commonVersionId, int componentId) {
        String query = "SELECT distinct kvm.mst_kpi_details_id id, mk.name name,kvm.mst_component_id componentId,mk.kpi_type_id typeId," +
                "mk.cluster_operation clusterOperation, mk.measure_units measureUnits,mk.data_type dataType, formula computedFormula " +
                "FROM mst_component_version_kpi_mapping kvm, mst_kpi_details mk, mst_computed_kpi_details mckd " +
                "where mckd.computedKpiId=mk.id and kvm.mst_kpi_details_id = mk.id " +
                "and kvm.status = 1 and kvm.mst_common_version_id = ? " +
                "and kvm.mst_component_id = ? ";
        try {
            log.debug("getting kpi list.");
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(KpiDetailsBean.class), commonVersionId, componentId);
        } catch (Exception e) {
            log.error("Error occurred while fetching kpi list for commonVersionId [{}] and componentId: [{}], Stack trace: {}", commonVersionId, componentId, e.getStackTrace());
        }
        return null;
    }

    public KpiCategoryDetails getKpiCategoryDetails(int kpiId) {
        String query = "select category_id id, name, category_identifier categoryId, if(is_workload=1,true,false) isWorkLoad " +
                "from view_kpi_category_details where kpi_id = ?";
        try {
            log.debug("getting computed expression.");
            return jdbcTemplate.queryForObject(query, new BeanPropertyRowMapper<>(KpiCategoryDetails.class), kpiId);
        } catch (Exception e) {
            log.error("Error occurred while fetching ComputerExpression [{}], Stack trace: {}", kpiId, e.getStackTrace());
        }
        return null;
    }

    public List<KpiCategoryDetails> getAllKpiCategoryDetails() {
        String query = "select category_id id, name, category_identifier categoryId, if(is_workload=1,true,false) isWorkLoad " +
                "from view_kpi_category_details";
        try {
            log.trace("Fetching kpi category details");
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(KpiCategoryDetails.class));
        } catch (Exception e) {
            log.error("Error occurred while fetching kpi category details", e);
        }
        return Collections.emptyList();
    }
}
