package com.heal.controlcenter.controller;

import com.heal.controlcenter.aop.AopCustomAnnotation;
import com.heal.controlcenter.beans.AccountServiceValidationBean;
import com.heal.controlcenter.beans.ServiceBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.AddAccountServicesBL;
import com.heal.controlcenter.businesslogic.DeleteAccountServicesBL;
import com.heal.controlcenter.businesslogic.GetAccountServicesBL;
import com.heal.controlcenter.businesslogic.PutAccountServicesBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.*;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.HealthMetrics;
import com.heal.controlcenter.util.JsonFileParser;
import com.heal.configuration.pojos.IdPojo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@Configuration
@RestController
public class AccountServiceController {

    @Autowired
    GetAccountServicesBL getAccountServicesBL;
    @Autowired
    JsonFileParser headersParser;
    @Autowired
    AddAccountServicesBL addAccountServicesBL;
    @Autowired
    DeleteAccountServicesBL deleteAccountServiceBL;
    @Autowired
    PutAccountServicesBL putAccountServicesBL;
    @Autowired
    HealthMetrics healthMetrics;

    /**
     * Adds or updates one or more services for the specified account.
     * <p>
     * Validates the request, performs business logic for service creation or update, and returns the result.
     * </p>
     *
     * @param accountIdentifier Identifier of the account for which services are being added/updated.
     * @param body              List of ServicePojo objects representing the services to add or update.
     * @return ResponseEntity containing a ResponsePojo with the list of IdPojo for the processed services.
     */
    @Operation(
            summary = "Add or update a service for an account",
            description = "Validates and adds or updates one or more services for the specified account, including database persistence, linking, and Redis updates.",
            security = @io.swagger.v3.oas.annotations.security.SecurityRequirement(name = "bearerAuth"),
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "List of service details to add or update for the account.",
                    required = true,
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ServicePojo.class)
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Service(s) added/updated successfully.",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = ResponsePojo.class))),
                    @ApiResponse(responseCode = "400", description = "Invalid request payload or data.",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = ResponsePojo.class))),
                    @ApiResponse(responseCode = "401", description = "Unauthorized - Missing or invalid authentication token.",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = ResponsePojo.class))),
                    @ApiResponse(responseCode = "500", description = "Internal server error during service add/update.",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = ResponsePojo.class)))
            }
    )
    @AopCustomAnnotation
    @PostMapping(value = "/accounts/{accountIdentifier}/services")
    public ResponseEntity<ResponsePojo<List<IdPojo>>> addServices(
            @PathVariable String accountIdentifier,
            @RequestBody List<ServicePojo> body, BasicUserDetails userDetails) {
        try {
            log.trace("Method Invoked: addOrUpdateService");
            UtilityBean<List<ServicePojo>> utilityBean = addAccountServicesBL.clientValidation(body, accountIdentifier);
            utilityBean.getMetadata().put(Constants.USER_ID_KEY, userDetails.getUserIdentifier());
            UtilityBean<List<ServiceBean>> updatedUtilityBean = addAccountServicesBL.serverValidation(utilityBean);
            List<IdPojo> result = addAccountServicesBL.process(updatedUtilityBean);
            ResponsePojo<List<IdPojo>> responseBean = new ResponsePojo<>("Service(s) added/updated successfully", result, HttpStatus.OK);
            return ResponseEntity.ok(responseBean);
        } catch (ClientException | ServerException | DataProcessingException e) {
            log.error("Error occurred while adding services.", e);
            healthMetrics.updateHealApiServiceErrors();
            healthMetrics.addStatusCodes(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE, 1);
            return ResponseEntity.badRequest().body(new ResponsePojo<>(e.getMessage(), null, HttpStatus.BAD_REQUEST));
        } catch (Exception e) {
            log.error("Error occurred while POST addServices.", e);
            healthMetrics.updateHealApiServiceErrors();
            healthMetrics.addStatusCodes(Constants.INTERNAL_SERVER_ERROR_STATUS_CODE, 1);
            return ResponseEntity.internalServerError().body(new ResponsePojo<>(e.getMessage(), null, HttpStatus.INTERNAL_SERVER_ERROR));
        }
    }

    /**
     * Retrieves a paginated list of services for a given account, with optional search and sorting.
     *
     * @param accountIdentifier The unique identifier of the account.
     * @param pageable Pagination and sorting information.
     * @param searchTerm An optional term to filter services by name or identifier.
     * @return A {@link ResponseEntity} containing a paginated list of {@link ServiceListPage} objects.
     */
    @Operation(
            summary = "Retrieve Services for Account",
            responses = {
                    @ApiResponse(
                            responseCode = "200", description = "Services retrieved successfully",
                            content = @Content(
                                    schema = @Schema(
                                            implementation = Page.class
                                    )
                            )
                    ),
                    @ApiResponse(
                            responseCode = "400", description = "Error in retrieving services",
                            content = @Content(schema = @Schema(implementation = ErrorResponsePojo.class))
                    ),
                    @ApiResponse(
                            responseCode = "500", description = "Internal Server Exception encountered while retrieving services",
                            content = @Content(schema = @Schema(implementation = ErrorResponsePojo.class))
                    )
            }
    )
    @AopCustomAnnotation
    @GetMapping(value = "/accounts/{accountIdentifier}/services")
    public ResponseEntity<ResponsePojo<Page<ServiceListPage>>> getAccountServices(
            @Parameter(
                    name = "accountIdentifier",
                    description = "Unique identifier of the account",
                    required = true,
                    example = "account-001"
            )
            @PathVariable("accountIdentifier") String accountIdentifier,
            @Parameter(
                    description = "Pagination and sorting information. " +
                            "Use `page` (0-indexed), `size` (items per page), and `sort` (property[,asc|desc]). " +
                            "Example: `?page=0&size=10&sort=name,asc`"
            )
            Pageable pageable,
            @Parameter(
                    description = "Search term to filter services by name or identifier.",
                    example = "TestService")
            @RequestParam(required = false) String searchTerm, BasicUserDetails userDetails) {
        try {
            UtilityBean<Object> utilityBean = getAccountServicesBL.clientValidation(null, accountIdentifier, searchTerm);
            utilityBean.setPageable(pageable);
            utilityBean.getMetadata().put(Constants.USER_ID_KEY, userDetails.getUserIdentifier());
            UtilityBean<AccountServiceValidationBean> validatedBean = getAccountServicesBL.serverValidation(utilityBean);
            Page<ServiceListPage> services = getAccountServicesBL.process(validatedBean);
            ResponsePojo<Page<ServiceListPage>> responsePojo = new ResponsePojo<>("success",
                    services, HttpStatus.OK);
            return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
        } catch (ClientException | ServerException | DataProcessingException e) {
            log.error("Error occurred while getting account services.", e);
            healthMetrics.updateHealApiServiceErrors();
            healthMetrics.addStatusCodes(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE, 1);
            return ResponseEntity.badRequest().body(new ResponsePojo<>(e.getMessage(), null, HttpStatus.BAD_REQUEST));
        } catch (Exception e) {
            log.error("Error occurred while GET account list data.", e);
            healthMetrics.updateHealApiServiceErrors();
            healthMetrics.addStatusCodes(Constants.INTERNAL_SERVER_ERROR_STATUS_CODE, 1);
            return ResponseEntity.internalServerError().body(new ResponsePojo<>(e.getMessage(), null, HttpStatus.INTERNAL_SERVER_ERROR));
        }
    }

    /**
     * Updates a service for the specified account.
     * Validates the request, performs server-side validation, and processes the update.
     *
     * @param accountIdentifier Identifier of the account
     * @param body List of ServicePojo objects containing service details to update
     * @param userDetails Authenticated user details
     * @return ResponseEntity containing the result of the update operation
     */
    @Operation(
            summary = "Update a service for an account",
            description = "Validates and updates a service for the specified account.",
            security = @io.swagger.v3.oas.annotations.security.SecurityRequirement(name = "bearerAuth"),
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Service details to update for the account.",
                    required = true,
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ServicePojo.class)
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Service updated successfully.",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = ResponsePojo.class))),
                    @ApiResponse(responseCode = "400", description = "Invalid request payload or data.",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = ResponsePojo.class))),
                    @ApiResponse(responseCode = "401", description = "Unauthorized - Missing or invalid authentication token.",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = ResponsePojo.class))),
                    @ApiResponse(responseCode = "500", description = "Internal server error during service update.",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = ResponsePojo.class)))
            }
    )
    @AopCustomAnnotation
    @PutMapping(value = "/accounts/{accountIdentifier}/services")
    public ResponseEntity<ResponsePojo<List<IdPojo>>> updateServices(
            @PathVariable String accountIdentifier,
            @RequestBody List<ServicePojo> body,
            BasicUserDetails userDetails) {
        try {
            log.trace("Method Invoked: updateService");
            UtilityBean<List<ServicePojo>> utilityBean = putAccountServicesBL.clientValidation(body, accountIdentifier);
            utilityBean.getMetadata().put(Constants.USER_ID_KEY, userDetails.getUserIdentifier());
            UtilityBean<List<ServiceBean>> updatedUtilityBean = putAccountServicesBL.serverValidation(utilityBean);
            List<IdPojo> result = putAccountServicesBL.process(updatedUtilityBean);
            ResponsePojo<List<IdPojo>> responseBean = new ResponsePojo<>("Service updated successfully", result, HttpStatus.OK);
            return ResponseEntity.ok(responseBean);
        } catch (ClientException | ServerException | DataProcessingException e) {
            log.error("Error occurred while updating services.", e);
            healthMetrics.updateHealApiServiceErrors();
            healthMetrics.addStatusCodes(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE, 1);
            return ResponseEntity.badRequest().body(new ResponsePojo<>(e.getMessage(), null, HttpStatus.BAD_REQUEST));
        } catch (Exception e) {
            log.error("Error occurred while PUT updateServices.", e);
            healthMetrics.updateHealApiServiceErrors();
            healthMetrics.addStatusCodes(Constants.INTERNAL_SERVER_ERROR_STATUS_CODE, 1);
            return ResponseEntity.internalServerError().body(new ResponsePojo<>(e.getMessage(), null, HttpStatus.INTERNAL_SERVER_ERROR));
        }
    }

    /**
     * Deletes one or more services for the specified account.
     * Supports both hard and soft deletion based on the 'isHardDelete' flag in the request body.
     *
     * @param accountIdentifier Identifier of the account for which services are being deleted.
     * @param deleteRequestPojo Object containing a list of service identifiers and the deletion type (hard/soft).
     * @param userDetails       Authenticated user details.
     * @return ResponseEntity indicating the success or failure of the deletion operation.
     */
    @Operation(
            summary = "Delete a service for an account",
            description = "Deletes one or more services for the specified account, supporting hard and soft deletion.",
            security = @io.swagger.v3.oas.annotations.security.SecurityRequirement(name = "bearerAuth"),
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "List of service identifiers and deletion type (hard/soft).",
                    required = true,
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ServiceDeleteRequestPojo.class)
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Service(s) deleted successfully.",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = ResponsePojo.class))),
                    @ApiResponse(responseCode = "400", description = "Invalid request payload or data.",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = ResponsePojo.class))),
                    @ApiResponse(responseCode = "401", description = "Unauthorized - Missing or invalid authentication token.",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = ResponsePojo.class))),
                    @ApiResponse(responseCode = "500", description = "Internal server error during service deletion.",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = ResponsePojo.class)))
            }
    )
    @AopCustomAnnotation
    @DeleteMapping(value = "/accounts/{accountIdentifier}/services")
    public ResponseEntity<ResponsePojo<String>> deleteServices(
            @PathVariable String accountIdentifier,
            @RequestBody ServiceDeleteRequestPojo deleteRequestPojo,
            BasicUserDetails userDetails) {
        try {
            log.trace("Method Invoked: deleteService");
            log.info("Received deleteRequestPojo: {}", deleteRequestPojo);
            UtilityBean<ServiceDeleteRequestPojo> clientValidatedBean = deleteAccountServiceBL.clientValidation(deleteRequestPojo, accountIdentifier);
            clientValidatedBean.getMetadata().put(Constants.USER_ID_KEY, userDetails.getUserIdentifier());
            UtilityBean<List<ServiceBean>> serverValidatedBean = deleteAccountServiceBL.serverValidation(clientValidatedBean);
            String result = deleteAccountServiceBL.process(serverValidatedBean);
            ResponsePojo<String> responseBean = new ResponsePojo<>(result, null, HttpStatus.OK);
            return ResponseEntity.ok(responseBean);
        } catch (ClientException | ServerException | DataProcessingException e) {
            log.error("Error occurred while deleting services.", e);
            healthMetrics.updateHealApiServiceErrors();
            healthMetrics.addStatusCodes(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE, 1);
            return ResponseEntity.badRequest().body(new ResponsePojo<>(e.getMessage(), null, HttpStatus.BAD_REQUEST));
        } catch (Exception e) {
            log.error("Error occurred while DELETE deleteServices.", e);
            healthMetrics.updateHealApiServiceErrors();
            healthMetrics.addStatusCodes(Constants.INTERNAL_SERVER_ERROR_STATUS_CODE, 1);
            return ResponseEntity.internalServerError().body(new ResponsePojo<>(e.getMessage(), null, HttpStatus.INTERNAL_SERVER_ERROR));
        }
    }
}