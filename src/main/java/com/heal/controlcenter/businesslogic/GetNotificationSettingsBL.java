package com.heal.controlcenter.businesslogic;

import com.heal.controlcenter.beans.AccountBean;
import com.heal.controlcenter.beans.NotificationSettingsBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.dao.mysql.MasterDataDao;
import com.heal.controlcenter.dao.mysql.NotificationsDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.NotificationSettings;
import com.heal.controlcenter.pojo.UserAccountPojo;
import com.heal.controlcenter.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class GetNotificationSettingsBL implements BusinessLogic<Object, UserAccountPojo, List<NotificationSettings>> {

    @Autowired
    NotificationsDao notificationsDao;
    @Autowired
    AccountsDao accountDao;
    @Autowired
    CommonUtils commonUtils;
    @Autowired
    MasterDataDao masterDataDao;
    @Autowired
    DateTimeUtil dateTimeUtil;
    @Autowired
    ClientValidationUtils clientValidationUtils;
    @Autowired
    ServerValidationUtils serverValidationUtils;

    @Value("${openForLong.minDuration.time.min:15}")
    private int MIN_OPEN_FOR_LONG;
    @Value("${openForTooLong.minDuration.time.min:30}")
    private int MIN_OPEN_FOR_TOO_LONG;
    @Value("${openForLong.maxDuration.time.min:1440}")
    private int MAX_OPEN_FOR_LONG;
    @Value("${openForTooLong.maxDuration.time.min:2880}")
    private int MAX_OPEN_FOR_TOO_LONG;

    /**
     * Performs client-side validation for authKey and accountIdentifier.
     * Uses utility methods to validate request parameters.
     * Builds and returns a UtilityBean with the validated parameters.
     * Throws ClientException if validation fails.
     */
    @Override
    public UtilityBean<Object> clientValidation(Object requestBody, String... requestParams) throws ClientException {
        String authKey = requestParams[0];
        clientValidationUtils.authKeyValidation(authKey);

        String accountIdentifier = requestParams[1];
        clientValidationUtils.accountIdentifierValidation(accountIdentifier);

        HashMap<String, String> requestParamsMap = CommonUtils.buildRequestParams(authKey, accountIdentifier);

        return UtilityBean.builder()
                .requestParams(requestParamsMap)
                .build();
    }

    /**
     * Performs server-side validation for the provided authKey and accountIdentifier.
     * Retrieves and verifies account details from the database.
     * Returns a UserAccountPojo containing user and account information.
     * Throws ServerException if validation fails or account is not found.
     */
    @Override
    public UserAccountPojo serverValidation(UtilityBean<Object> utilityBean) throws ServerException {
        String authKey = utilityBean.getRequestParams().get(Constants.AUTH_KEY);
        String userId = serverValidationUtils.authKeyValidation(authKey);

        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
        AccountBean account = accountDao.getAccountByIdentifier(accountIdentifier);
        if (account == null) {
            String message = String.format(UIMessages.ACCOUNT_IDENTIFIER_UNAVAILABLE, accountIdentifier);
            log.error(message);
            throw new ServerException(message);
        }

        UserAccountPojo user = new UserAccountPojo();
        user.setUserId(userId);
        user.setAccount(account);

        return user;
    }

    @Override
    @Transactional(rollbackFor = {Exception.class, RuntimeException.class})
    public List<NotificationSettings> process(UserAccountPojo user) throws DataProcessingException {
        List<NotificationSettings> settingsList = new ArrayList<>();
        List<NotificationSettings> notificationSettings = null;
        try {
            notificationSettings = notificationsDao.getNotificationSetting(user.getAccount().getId())
                    .parallelStream().map(d -> NotificationSettings.builder()
                            .durationInMin(d.getDurationInMin())
                            .accountId(d.getAccountId())
                            .typeId(d.getTypeId())
                            .createdTime(d.getCreatedTime())
                            .updatedTime(d.getUpdatedTime())
                            .lastModifiedBy(d.getLastModifiedBy())
                            .properties(d.getProperties())
                            .build()).collect(Collectors.toList());
        } catch (HealControlCenterException e) {
            throw new DataProcessingException(e.getMessage());
        }

        if (notificationSettings.isEmpty()) {
            log.info("Notification settings unavailable for account [{}]. Setting Default Values.", user.getAccount().getIdentifier());
            int[] ids;
            try {
                ids = addDefaultNotificationSettings(user.getAccount().getId(), user.getUserId());
            } catch (HealControlCenterException e) {
                throw new DataProcessingException(e.getMessage());
            }
            if (ids.length > 0) {
                log.info("Successfully added default notification settings for account [{}]", user.getAccount().getIdentifier());
                try {
                    notificationSettings = notificationsDao.getNotificationSetting(user.getAccount().getId())
                            .parallelStream().map(d -> NotificationSettings.builder()
                                    .durationInMin(d.getDurationInMin())
                                    .accountId(d.getAccountId())
                                    .typeId(d.getTypeId())
                                    .createdTime(d.getCreatedTime())
                                    .updatedTime(d.getUpdatedTime())
                                    .lastModifiedBy(d.getLastModifiedBy())
                                    .properties(d.getProperties())
                                    .build()).collect(Collectors.toList());
                } catch (HealControlCenterException e) {
                    throw new DataProcessingException(e.getMessage());
                }
            } else {
                log.error("Error adding default Notification Settings for account [{}]", user.getAccount().getIdentifier());
                throw new DataProcessingException("Error adding default notification settings");
            }
        }

        for (NotificationSettings setting : notificationSettings) {
            String typeName = masterDataDao.getMstSubTypeBySubTypeId(setting.getTypeId()).getSubTypeName();
            setting.setTypeName(typeName);
            if (Constants.LONG.equalsIgnoreCase(typeName)) {
                setting.getProperties().put("min", MIN_OPEN_FOR_LONG);
                setting.getProperties().put("max", MAX_OPEN_FOR_LONG);
            } else if (Constants.TOO_LONG.equalsIgnoreCase(typeName)) {
                setting.getProperties().put("min", MIN_OPEN_FOR_TOO_LONG);
                setting.getProperties().put("max", MAX_OPEN_FOR_TOO_LONG);
            }
            settingsList.add(setting);
        }
        return settingsList;
    }

    public int[] addDefaultNotificationSettings(int accountId, String userId) throws HealControlCenterException {
        int longId = masterDataDao.getViewTypesFromMstTypeAndSubTypeName(Constants.NOTIFICATION_TYPE_LITERAL, Constants.LONG).getSubTypeId();
        int tooLongId = masterDataDao.getViewTypesFromMstTypeAndSubTypeName(Constants.NOTIFICATION_TYPE_LITERAL, Constants.TOO_LONG).getSubTypeId();
        int[] ids = {};

        try {
            List<NotificationSettings> defaultSettingsList = new ArrayList<>();
            NotificationSettings settingsLong = new NotificationSettings();
            NotificationSettings settingsTooLong = new NotificationSettings();
            Timestamp timestamp = new Timestamp(dateTimeUtil.getDateInGMT(System.currentTimeMillis()).getTime());
            List<NotificationSettingsBean> settingsDB = notificationsDao.getNotificationSetting(1);

            for (NotificationSettingsBean settings : settingsDB) {
                if (settings.getTypeId() == longId) {
                    settingsLong.setDurationInMin(settings.getDurationInMin());
                }
                if (settings.getTypeId() == tooLongId) {
                    settingsTooLong.setDurationInMin(settings.getDurationInMin());
                }
            }
            settingsLong.setUpdatedTime(timestamp.toString());
            settingsLong.setCreatedTime(timestamp.toString());
            settingsLong.setLastModifiedBy(userId);
            settingsLong.setAccountId(accountId);
            settingsLong.setTypeId(longId);

            settingsTooLong.setUpdatedTime(timestamp.toString());
            settingsTooLong.setCreatedTime(timestamp.toString());
            settingsTooLong.setLastModifiedBy(userId);
            settingsTooLong.setAccountId(accountId);
            settingsTooLong.setTypeId(tooLongId);

            defaultSettingsList.add(settingsLong);
            defaultSettingsList.add(settingsTooLong);

            List<NotificationSettingsBean> beans = defaultSettingsList.parallelStream()
                    .map(d -> NotificationSettingsBean.builder()
                            .durationInMin(d.getDurationInMin())
                            .accountId(d.getAccountId())
                            .typeId(d.getTypeId())
                            .createdTime(d.getCreatedTime())
                            .updatedTime(d.getUpdatedTime())
                            .lastModifiedBy(d.getLastModifiedBy())
                            .build()).collect(Collectors.toList());

            ids = notificationsDao.addNotificationSettings(beans);
            return ids;
        } catch (Exception e) {
            log.error("Error occurred while adding default notification Settings. Details: ", e);
        }
        return ids;
    }
}
