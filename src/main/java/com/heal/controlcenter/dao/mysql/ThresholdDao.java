package com.heal.controlcenter.dao.mysql;

import com.heal.controlcenter.dao.mysql.entity.InstKpiAttrPersistenceSuppressionBean;
import com.heal.controlcenter.dao.mysql.entity.InstanceKpiAttributeThresholdBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Repository
@Slf4j
public class ThresholdDao {

    @Autowired
    JdbcTemplate jdbcTemplate;

    public List<InstanceKpiAttributeThresholdBean> fetchCompInstanceKpiAttrThresholds(List<Integer> instanceIds) {
        if (instanceIds == null || instanceIds.isEmpty()) {
            return Collections.emptyList();
        }
        String inSql = String.join(",", Collections.nCopies(instanceIds.size(), "?"));
        String query = "SELECT comp_instance_id compInstanceId, kpi_id kpiId, operation_id operationId, min_threshold minThreshold, " +
                "max_threshold maxThreshold, attribute_value attributeValue, account_id accountId, kpi_group_id kpiGroupId, " +
                "status status, threshold_severity_id thresholdSeverityId, persistence persistence, suppression suppression, " +
                "exclude_maintenance excludeMaintainance " +
                "FROM comp_instance_kpi_threshold_details WHERE comp_instance_id IN (" + inSql + ")";
        try {
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(InstanceKpiAttributeThresholdBean.class), instanceIds.toArray());
        } catch (Exception e) {
            log.error("Error occurred while fetching component instance KPI attribute thresholds for instanceIds [{}]. Details: {}", instanceIds, e.getMessage());
            return Collections.emptyList();
        }
    }

    public List<InstKpiAttrPersistenceSuppressionBean> fetchCompInstanceKpiPerSupValuesByAccountId(int accountId, int kpiId) {
        String query = "SELECT persistence persistence, suppression suppression, exclude_maintenance isMaintenanceExcluded, " +
                "kpi_id kpiId, comp_instance_id compInstanceId, account_id accountId, attribute_value attributeValue, " +
                "kpi_group_id kpiGroupId, threshold_severity_id thresholdSeverityId " +
                "FROM comp_instance_kpi_threshold_details " +
                "WHERE account_id = ? AND kpi_id = ?";
        try {
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(InstKpiAttrPersistenceSuppressionBean.class), accountId, kpiId);
        } catch (Exception e) {
            log.error("Error occurred while fetching persistence, suppression, and maintenance values for accountId [{}]. Details: {}", accountId, e.getMessage());
            return Collections.emptyList();
        }
    }

    public List<InstKpiAttrPersistenceSuppressionBean> fetchCompInstanceKpiPerSupValuesByAccountIdAndKpiIds(int accountId, List<Integer> kpiIds) {
        if (kpiIds == null || kpiIds.isEmpty()) {
            return Collections.emptyList();
        }
        String inSql = String.join(",", Collections.nCopies(kpiIds.size(), "?"));
        String query = "SELECT persistence persistence, suppression suppression, exclude_maintenance isMaintenanceExcluded, " +
                "kpi_id kpiId, comp_instance_id compInstanceId, account_id accountId, attribute_value attributeValue, " +
                "kpi_group_id kpiGroupId, threshold_severity_id thresholdSeverityId " +
                "FROM comp_instance_kpi_threshold_details " +
                "WHERE account_id = ? AND kpi_id IN (" + inSql + ")";
        List<Object> params = new ArrayList<>();
        params.add(accountId);
        params.addAll(kpiIds);
        try {
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(InstKpiAttrPersistenceSuppressionBean.class), params.toArray());
        } catch (Exception e) {
            log.error("Error occurred while fetching persistence, suppression, and maintenance values for accountId [{}] and kpiIds [{}]. Details: {}", accountId, kpiIds, e.getMessage());
            return Collections.emptyList();
        }
    }

    public int[] updateInstanceKpiAttributeLevelThresholdSeverityAndGenAnomaly(List<InstanceKpiAttributeThresholdBean> thresholdBeans) {
        String query = "UPDATE comp_instance_kpi_threshold_details SET status = ?, updated_time = ? " +
                "WHERE comp_instance_id = ? AND kpi_group_id = ? AND kpi_id = ? AND attribute_value = ? AND threshold_severity_id = ? ";
        return jdbcTemplate.batchUpdate(query, new BatchPreparedStatementSetter() {
            @Override
            public void setValues(PreparedStatement ps, int i) throws SQLException {
                InstanceKpiAttributeThresholdBean s = thresholdBeans.get(i);
                ps.setInt(1, s.getStatus());
                ps.setTimestamp(2, Timestamp.valueOf(s.getUpdatedTime()));
                ps.setInt(3, s.getCompInstanceId());
                ps.setInt(4, s.getKpiGroupId());
                ps.setInt(5, s.getKpiId());
                ps.setString(6, s.getAttributeValue());
                ps.setInt(7, s.getThresholdSeverityId());
            }

            @Override
            public int getBatchSize() {
                return thresholdBeans.size();
            }
        });
    }

    public int[] updateInstanceKpiAttributeLevelThresholds(List<InstanceKpiAttributeThresholdBean> thresholdBeans) {
        String query = "UPDATE comp_instance_kpi_threshold_details " +
                "SET operation_id = IFNULL(?, operation_id), " +
                "min_threshold = IFNULL(?, min_threshold), " +
                "max_threshold = IFNULL(?, max_threshold), " +
                "status = ?, updated_time = ?, start_time = ? " +
                "WHERE comp_instance_id = ? AND kpi_group_id = ? AND kpi_id = ? AND attribute_value = ? AND threshold_severity_id = ? ";
        return jdbcTemplate.batchUpdate(query, new BatchPreparedStatementSetter() {
            @Override
            public void setValues(PreparedStatement ps, int i) throws SQLException {
                InstanceKpiAttributeThresholdBean bean = thresholdBeans.get(i);
                ps.setObject(1, bean.getOperationId());
                ps.setObject(2, bean.getMinThreshold());
                ps.setObject(3, bean.getMaxThreshold());
                ps.setInt(4, bean.getStatus());
                ps.setTimestamp(5, Timestamp.valueOf(bean.getUpdatedTime()));
                ps.setString(6, bean.getStartTime());
                ps.setInt(7, bean.getCompInstanceId());
                ps.setInt(8, bean.getKpiGroupId());
                ps.setInt(9, bean.getKpiId());
                ps.setString(10, bean.getAttributeValue());
                ps.setInt(11, bean.getThresholdSeverityId());
            }

            @Override
            public int getBatchSize() {
                return thresholdBeans.size();
            }
        });
    }

    public int[] deleteInstanceKpiAttributeLevelThresholds(List<InstanceKpiAttributeThresholdBean> thresholdBeans) {
        String query = "DELETE FROM comp_instance_kpi_threshold_details " +
                "WHERE comp_instance_id = ? AND kpi_id = ? AND kpi_group_id = ? " +
                "AND account_id = ? AND attribute_value = ?";
        try {
            return jdbcTemplate.batchUpdate(query, new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps, int i) throws SQLException {
                    InstanceKpiAttributeThresholdBean bean = thresholdBeans.get(i);
                    ps.setInt(1, bean.getCompInstanceId());
                    ps.setInt(2, bean.getKpiId());
                    ps.setInt(3, bean.getKpiGroupId());
                    ps.setInt(4, bean.getAccountId());
                    ps.setString(5, bean.getAttributeValue());
                }

                @Override
                public int getBatchSize() {
                    return thresholdBeans.size();
                }
            });
        } catch (Exception e) {
            log.error("Error occurred while deleting instance KPI attribute level thresholds. Details: {}", e.getMessage());
            return new int[0];
        }
    }

    public List<InstanceKpiAttributeThresholdBean> getCompInstanceThresholdDetail(int accountId, int kpiId, List<Integer> compInstanceIds) {
        if (compInstanceIds == null || compInstanceIds.isEmpty()) {
            return Collections.emptyList();
        }
        String inSql = String.join(",", Collections.nCopies(compInstanceIds.size(), "?"));
        String query = "SELECT id id, comp_instance_id compInstanceId, kpi_id kpiId, attribute_value attributeValue, operation_id operationId, " +
                "min_threshold minThreshold, max_threshold maxThreshold, created_time createdTime, updated_time updatedTime, " +
                "user_details_id userDetailsId, kpi_group_id kpiGroupId, start_time startTime, end_time endTime, account_id accountId, " +
                "status status, threshold_severity_id thresholdSeverityId, persistence persistence, suppression suppression, " +
                "exclude_maintenance excludeMaintainance, coverage_window coverageWindow " +
                "FROM comp_instance_kpi_threshold_details " +
                "WHERE comp_instance_id IN (" + inSql + ") AND account_id = ? AND kpi_id = ?";
        List<Object> params = new ArrayList<>(compInstanceIds);
        params.add(accountId);
        params.add(kpiId);
        try {
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(InstanceKpiAttributeThresholdBean.class), params.toArray());
        } catch (Exception e) {
            log.error("Error occurred while fetching component instance threshold details for compInstanceIds [{}], accountId [{}], and kpiId [{}]. Details: {}", compInstanceIds, accountId, kpiId, e.getMessage());
            return Collections.emptyList();
        }
    }

    public int[] addInstanceKpiAttributeLevelThresholds(List<InstanceKpiAttributeThresholdBean> thresholdBeans) {
        String sql = "INSERT INTO comp_instance_kpi_threshold_details " +
                "(comp_instance_id, kpi_id, operation_id, min_threshold, max_threshold, kpi_group_id, account_id, status, " +
                "attribute_value, user_details_id, created_time, updated_time, start_time, threshold_severity_id, exclude_maintenance, coverage_window) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        return jdbcTemplate.batchUpdate(sql, new BatchPreparedStatementSetter() {
            @Override
            public void setValues(PreparedStatement ps, int i) throws SQLException {
                InstanceKpiAttributeThresholdBean bean = thresholdBeans.get(i);
                ps.setInt(1, bean.getCompInstanceId());
                ps.setInt(2, bean.getKpiId());
                ps.setInt(3, bean.getOperationId());
                ps.setDouble(4, bean.getMinThreshold());
                ps.setDouble(5, bean.getMaxThreshold());
                ps.setInt(6, bean.getKpiGroupId());
                ps.setInt(7, bean.getAccountId());
                ps.setInt(8, bean.getStatus());
                ps.setString(9, bean.getAttributeValue());
                ps.setString(10, bean.getUserDetailsId());
                ps.setString(11, bean.getCreatedTime());
                ps.setString(12, bean.getUpdatedTime());
                ps.setString(13, bean.getStartTime());
                ps.setInt(14, bean.getThresholdSeverityId());
                ps.setInt(15, bean.getExcludeMaintenance());
                ps.setString(16, bean.getCoverageWindow());
            }

            @Override
            public int getBatchSize() {
                return thresholdBeans.size();
            }
        });
    }
}
