package com.heal.controlcenter.beans;

import com.appnomic.appsone.common.beans.ComponentInstanceBean;
import com.heal.controlcenter.pojo.TimezoneDetail;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ServiceBean {

    private int id;
    private String name;
    private String identifier;
    private int accountId;
    private String accountIdentifier;
    private String userId;
    private String layer;
    private String appIdentifier;
    private int appId;
    private List<Integer> appIds;
    private List<String> appIdentifiers;
    private String type;
    private List<ComponentInstanceBean> instances;
    private int isUpdate;
    private int status;
    private String timezoneId;
    private boolean entryPointService;
    private TimezoneDetail timezoneDetail;
}