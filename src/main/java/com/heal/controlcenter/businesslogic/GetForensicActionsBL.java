package com.heal.controlcenter.businesslogic;

import com.heal.configuration.pojos.Account;
import com.heal.controlcenter.beans.ForensicActionArgumentsBean;
import com.heal.controlcenter.beans.ForensicActionBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.dao.mysql.ActionScriptDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.ForensicActionsCategoryPojo;
import com.heal.controlcenter.pojo.ForensicActionsParametersPojo;
import com.heal.controlcenter.pojo.ForensicActionsPojo;
import com.heal.controlcenter.util.ClientValidationUtils;
import com.heal.controlcenter.util.CommonUtils;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.ServerValidationUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
public class GetForensicActionsBL implements BusinessLogic<Object, Integer, List<ForensicActionsPojo>> {

    @Autowired
    CommonUtils commonUtils;
    @Autowired
    AccountsDao accountDao;
    @Autowired
    ActionScriptDao actionScriptDao;
    @Autowired
    ClientValidationUtils clientValidationUtils;
    @Autowired
    ServerValidationUtils serverValidationUtils;

    @Override
    public UtilityBean<Object> clientValidation(Object requestBody, String... requestParams) throws ClientException {

        String authKey = requestParams[0];
        clientValidationUtils.authKeyValidation(authKey);

        String accountIdentifier = requestParams[1];
        clientValidationUtils.accountIdentifierValidation(accountIdentifier);

        HashMap<String, String> requestParamsMap = CommonUtils.buildRequestParams(authKey, accountIdentifier);

        return UtilityBean.builder()
                .requestParams(requestParamsMap)
                .build();
    }

    @Override
    public Integer serverValidation(UtilityBean<Object> utilityBean) throws ServerException {

        String authKey = utilityBean.getRequestParams().get(Constants.AUTH_KEY);
        serverValidationUtils.authKeyValidation(authKey);

        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
        Account account = serverValidationUtils.accountValidation(accountIdentifier);

        return account.getId();
    }

    @Override
    public List<ForensicActionsPojo> process(Integer accountId) throws DataProcessingException {
        try {
            List<ForensicActionBean> forensicActions = actionScriptDao.getForensicActions(accountId);

            Map<Integer, List<ForensicActionsCategoryPojo>> forensicActionsCategoryMap = forensicActions.parallelStream()
                    .collect(Collectors.groupingBy(ForensicActionBean::getActionId,
                            Collectors.mapping(f -> ForensicActionsCategoryPojo.builder()
                                    .action(f.getActionName())
                                    .actionId(f.getActionId())
                                    .custom(String.valueOf(f.getIsCustomCategory()))
                                    .id(f.getCategoryId())
                                    .name(f.getCategoryName())
                                    .identifier(f.getCategoryIdentifier())
                                    .build(), Collectors.toList())));

            Map<Integer, List<ForensicActionsParametersPojo>> forensicActionArgumentsMap = actionScriptDao.getForensicsParameters()
                    .parallelStream()
                    .collect(Collectors.groupingBy(ForensicActionArgumentsBean::getCommandId,
                            Collectors.mapping(f -> ForensicActionsParametersPojo.builder()
                                    .id(f.getId())
                                    .type(f.getType())
                                    .argument_key(f.getArgument_key())
                                    .value(f.getValue())
                                    .defaultValue(f.getDefaultValue())
                                    .valueType(f.getValueType())
                                    .build(), Collectors.toList())));

            return forensicActions.parallelStream().map(bean -> ForensicActionsPojo.builder()
                    .id(bean.getActionId())
                    .name(bean.getActionName())
                    .type(bean.getActionType())
                    .commandName(bean.getCommandName())
                    .commandIdentifier(bean.getCommandIdentifier())
                    .commandTimeoutInSeconds(String.valueOf(bean.getCommandTimeoutInSeconds()))
                    .supCtrlTimeoutInSeconds(String.valueOf(bean.getSupCtrlTimeoutInSeconds()))
                    .supCtrlRetryCount(String.valueOf(bean.getSupCtrlRetryCount()))
                    .status(bean.getStatus())
                    .lastModifiedBy(bean.getLastModifiedBy())
                    .lastModifiedOn(bean.getLastModifiedOn())
                    .categoryList(forensicActionsCategoryMap.getOrDefault(bean.getActionId(), new ArrayList<>()))
                    .parameters(forensicActionArgumentsMap.getOrDefault(bean.getCommandId(), new ArrayList<>()))
                    .build()
            ).collect(Collectors.toList());

        } catch (Exception e) {
            throw new DataProcessingException(e.getMessage());
        }
    }
}
