package com.heal.controlcenter.util;

import com.appnomic.appsone.common.util.ConfProperties;
import org.apache.hc.core5.http.HttpStatus;

public class Constants {

    public static final String AUTHORIZATION = "Authorization";

    public static final String TOTAL="total";

    public static final String COMMA = ",";

    // General
    public static final String DATE_TIME="yyyy-MM-dd HH:mm:ss";

    // ECDSA constants
    public static final String BC_PROVIDER_NAME = "BC";

    // User role constants used for access control and permission management
    public static final String SUPER_ADMIN = "Super Admin";
    public static final String HEAL_ADMIN = "Heal Admin";

    /**
     * Notification constants
     */
    public static final String NOTIFICATION_TYPE_LITERAL = "NotificationType";
    public static final String LONG = "Open for long";
    public static final String TOO_LONG = "Open for too long";

    public static final String AUTH_TOKEN = "authToken";
    public static final String COMPONENT_TYPE_HOST = "HOST";
    public static final String HEAL_HEALTH_ACCOUNT_IDENTIFIER = "heal_health";
    public static final String OPENSEARCH_CONNECTION_IO_REACTOR_SIZE = "opensearch.connection.io.reactor.size";
    public static final String OPENSEARCH_CONNECTION_IO_REACTOR_SIZE_DEFAULT = "2";
    public static final String INDEX_PREFIX_HEAL_AUTO_DISCOVERY_ERRORS = "heal_autodiscovery_errors";
    public static final String GLOBAl_ACCOUNT_IDENTIFIER = "e573f852-5057-11e9-8fd2-b37b61e52317";
    public static final String ENVIRONMENT_TYPE_NAME = "Environment";

    // SMS and SMTP
    public static final String SMS_ACTION_ADD = "add";
    public static final String SMS_ACTION_EDIT = "edit";
    public static final String SMS_ACTION_DELETE = "delete";
    public static final String SMS_PROTOCOLS = "SMSGatewayProtocols";
    public static final String SMS_HTTP_METHODS= "HTTPSMSRequestMethods";
    public static final String SMS_PARAMETER_TYPE_NAME= "SMSParameterTypes";
    public static final String SMS_PLACEHOLDERS= "SMSPlaceHolders";
    public static final String SMTP_PROTOCOLS = "SMTP Security";

    public static final String MST_TIMEZONE = "mst_timezone";
    public static final String CONTROLLER = "controller";
    public static final String SERVICES_LAYER_TYPE = "ServiceLayers";

    public static final String TIME_ZONE_TAG = "Timezone";
    public static final String SIGNAL_SEVERITY_TYPE_LITERAL = "SignalSeverity";
    public static final String SIGNAL_TYPE_LITERAL = "SignalType";
    public static final String SEVERE = "Severe";
    public static final String DEFAULT = "Default";
    public static final String EARLY_WARNING = "Early Warning";
    public static final String PROBLEM = "Problem";
    public static final String INFO = "Info";
    public static final String BATCH = "Batch Job";
    public static final String IMMEDIATELY = "Immediately";
    public static final String APPLICATION_PERCENTILES_DEFAULT_CONFIGURATION = "application.percentiles.default";
    public static final String APPLICATION_PERCENTILES_DEFAULT_VALUES = "50,75,90,95,99";
    public static final String APPLICATION_CONTROLLER_TYPE = "Application";
    public static final String PERCENTILE_KPIS_IDENTIFIER_SUFFIX = "percentile.kpis.identifier.suffix";
    public static final String PERCENTILE_KPIS_IDENTIFIER_SUFFIX_DEFAULT = "P";

    // Agent commands
    public static final String MST_TYPE_ATTRIBUTE_TYPE = "Attribute_Type";
    public static final String MST_TYPE_COMMAND_OUTPUT_TYPE = "CommandOutputType";
    public static final String MST_TYPE_CONF_CMDS = "ConfigurationCmds";
    public static final String MST_TYPE_SCRIPT_PARAM_TYPE = "SCRIPT_Parameter_Type";

    public static final String MST_SUB_TYPE_COMMAND_LINE = "COMMANDLINE";
    public static final String MST_SUB_TYPE_BLOB = "Blob";
    public static final String MST_SUB_TYPE_TEXT_BOX = "TextBox";
    public static final String MST_SUB_TYPE_EXECUTE = "Execute";

    /**
     * Return codes
     */
    public static final int SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE = HttpStatus.SC_BAD_REQUEST;
    public static final int INTERNAL_SERVER_ERROR_STATUS_CODE = HttpStatus.SC_INTERNAL_SERVER_ERROR;


    // Agent commands
    public static final String AGENT_TYPE = "Agent";
    public static final String JIM_AGENT_SUB_TYPE ="JIMAgent";
    public static final String AGENT_MODE_AUTO = "Auto";
    public static final String AGENT_MODE_VERBOSE = "Verbose";
    public static final int DEFAULT_TIMEOUT_IN_SECS = 300;
    public static final String AGENT_SILENT_WINDOW = "15";

    public static final String SERVICES_CONTROLLER_TYPE = "Services";
    public static final String CONTROLLER_TYPE_NAME_DEFAULT = "ControllerType";

    public static final int AVAILABLE_CORES = Runtime.getRuntime().availableProcessors();

    public static final String WORKER_THREAD_MULTIPLIER_PROPERTY_NAME = "worker.thread.multiplier";
    public static final String WORKER_THREAD_DEFAULT_MULTIPLIER = "1";

    public static final String THRESHOLD_SEVERITY_TYPE = "ThresholdSeverity";
    public static final String THRESHOLD_SEVERITY_TYPE_LOW = "Low";
    public static final String THRESHOLD_SEVERITY_TYPE_MEDIUM = "Medium";
    public static final String THRESHOLD_SEVERITY_TYPE_HIGH = "High";

    public static final String OPERATIONS_TYPE = "Operations";
    public static final String OPERATIONS_TYPE_LESSER_THAN = "lesser than";
    public static final String OPERATIONS_TYPE_GREATER_THAN = "greater than";
    public static final String OPERATIONS_TYPE_NOT_BETWEEN = "not between";
    public static final String AVAILABILITY_OPERATIONS_TYPE = "AvailabilityOperations";
    public static final String STATIC_THRESHOLD = "STATIC";
    public static final String CORE_KPI_TYPE = "Core";
    public static final String AVAIL_KPI_TYPE = "Availability";
    public static final String THRESHOLD_DEFINED_BY_USER = "USER";
    public static final String THRESHOLD_DEFINED_BY_SYSTEM = "SYSTEM";

    // Tag details
    public static final String CONTROLLER_TAG = "Controller";

    // Table names
    public static final String AGENT_TABLE = "agent";
    public static final String COMP_INSTANCE_TABLE = "comp_instance";

    // service suppression persistence
    public static final String OPERATOR_LESS_THAN = "lt";
    public static final String OPERATOR_GREATER_THAN = "gte";

    // Default suppression persistence values
    public static final String SERVICE_START_WITHIN_AN_HOUR_PROPERTY_NAME = "service.startTime.lesserThan.hour";
    public static final String SERVICE_END_WITHIN_AN_HOUR_PROPERTY_NAME = "service.endTime.lesserThan.hour";
    public static final String SERVICE_START_TIME_WITHIN_AN_HOUR = "1";
    public static final String SERVICE_END_TIME_WITHIN_AN_HOUR = "59";

    public static final String SERVICE_START_MORE_THAN_AN_HOUR_PROPERTY_NAME = "service.startTime.greaterThan.hour";
    public static final String SERVICE_END_MORE_THAN_AN_HOUR_PROPERTY_NAME = "service.endTime.greaterThan.hour";
    public static final String SERVICE_START_TIME_MORE_THAN_AN_HOUR = "60";
    public static final String SERVICE_END_TIME_MORE_THAN_AN_HOUR = "1440";

    public static final String SERVICE_LOW_PERSISTENCE_WITHIN_AN_HOUR_PROPERTY_NAME = "service.low.persistence.lesserThan.hour";
    public static final String SERVICE_LOW_SUPPRESSION_WITHIN_AN_HOUR_PROPERTY_NAME = "service.low.suppression.lesserThan.hour";
    public static final String SERVICE_LOW_PERSISTENCE_WITHIN_AN_HOUR = "5";
    public static final String SERVICE_LOW_SUPPRESSION_WITHIN_AN_HOUR = "10";

    public static final String SERVICE_LOW_PERSISTENCE_MORE_THAN_AN_HOUR_PROPERTY_NAME = "service.low.persistence.greaterThan.hour";
    public static final String SERVICE_LOW_SUPPRESSION_MORE_THAN_AN_HOUR_PROPERTY_NAME = "service.low.suppression.greaterThan.hour";
    public static final String SERVICE_LOW_PERSISTENCE_MORE_THAN_AN_HOUR = "2";
    public static final String SERVICE_LOW_SUPPRESSION_MORE_THAN_AN_HOUR = "5";

    public static final String SERVICE_MEDIUM_PERSISTENCE_WITHIN_AN_HOUR_PROPERTY_NAME = "service.medium.persistence.lesserThan.hour";
    public static final String SERVICE_MEDIUM_SUPPRESSION_WITHIN_AN_HOUR_PROPERTY_NAME = "service.medium.suppression.lesserThan.hour";
    public static final String SERVICE_MEDIUM_PERSISTENCE_WITHIN_AN_HOUR = "5";
    public static final String SERVICE_MEDIUM_SUPPRESSION_WITHIN_AN_HOUR = "10";

    public static final String SERVICE_MEDIUM_PERSISTENCE_MORE_THAN_AN_HOUR_PROPERTY_NAME = "service.medium.persistence.greaterThan.hour";
    public static final String SERVICE_MEDIUM_SUPPRESSION_MORE_THAN_AN_HOUR_PROPERTY_NAME = "service.medium.suppression.greaterThan.hour";
    public static final String SERVICE_MEDIUM_PERSISTENCE_MORE_THAN_AN_HOUR = "2";
    public static final String SERVICE_MEDIUM_SUPPRESSION_MORE_THAN_AN_HOUR = "5";

    public static final String SERVICE_HIGH_PERSISTENCE_WITHIN_AN_HOUR_PROPERTY_NAME = "service.high.persistence.lesserThan.hour";
    public static final String SERVICE_HIGH_SUPPRESSION_WITHIN_AN_HOUR_PROPERTY_NAME = "service.high.suppression.lesserThan.hour";
    public static final String SERVICE_HIGH_PERSISTENCE_WITHIN_AN_HOUR = "5";
    public static final String SERVICE_HIGH_SUPPRESSION_WITHIN_AN_HOUR = "10";

    public static final String SERVICE_HIGH_PERSISTENCE_MORE_THAN_AN_HOUR_PROPERTY_NAME = "service.high.persistence.greaterThan.hour";
    public static final String SERVICE_HIGH_SUPPRESSION_MORE_THAN_AN_HOUR_PROPERTY_NAME = "service.high.suppression.greaterThan.hour";
    public static final String SERVICE_HIGH_PERSISTENCE_MORE_THAN_AN_HOUR = "2";
    public static final String SERVICE_HIGH_SUPPRESSION_MORE_THAN_AN_HOUR = "5";

    public static final String SERVICE_HIGH_SUPPRESSION_PERSISTENCE_ENABLE = "service.high.suppression.persistence.enable";
    public static final String SERVICE_MEDIUM_SUPPRESSION_PERSISTENCE_ENABLE = "service.medium.suppression.persistence.enable";
    public static final String SERVICE_LOW_SUPPRESSION_PERSISTENCE_ENABLE = "service.low.suppression.persistence.enable";

    public static final Boolean SERVICE_HIGH_SUPPRESSION_PERSISTENCE_ENABLE_FLAG = true;
    public static final Boolean SERVICE_MEDIUM_SUPPRESSION_PERSISTENCE_ENABLE_FLAG = true;
    public static final Boolean SERVICE_LOW_SUPPRESSION_PERSISTENCE_ENABLE_FLAG = true;

    public static final String SERVICE_CLOSING_WINDOW_PROPERTY_NAME = "service.closing.window";
    public static final String SERVICE_CLOSING_WINDOW = "3";
    public static final String SERVICE_MAX_DATA_BREAKS_PROPERTY_NAME = "service.max.data.breaks";
    public static final String SERVICE_MAX_DATA_BREAKS = "30";
    public static final String NON_KUBERNETES = "Non_Kubernetes";
    public static final String SERVICE_BEAN_LIST = "Service_Bean_List";
    public static final String ALLOWED_NAME_CHARACTERS = ConfProperties.getString("allowed.name.characters", "^[a-zA-Z0-9-_.|\\s]*$");
    public static final String ALLOWED_IDENTIFIER_CHARACTERS = ConfProperties.getString("allowed.identifier.characters", "^[a-zA-Z0-9-_.|]*$");
    public static final String ACCOUNT = "account";

    // request params
    public static final String KPI_TYPE = "KPI";
    public static final String ALL = "ALL";
    public static final String ACCOUNT_IDENTIFIER = "accountIdentifier";
    public static final String SERVICE_IDENTIFIER = "serviceIdentifier";
    public static final String THRESHOLD_TYPE = "thresholdType";
    public static final String IS_SYSTEM = "isSystem";
    public static final String AUTH_KEY = "authKey";
    public static final String ACCOUNT_ID = "accountId";
    public static final String SERVICE_ID = "serviceId";
    public static final String HARD_DELETE = "hardDelete";
    // Redis Cache Wrapper
    public static final String HEAL_INDEX_ZONES = "heal_index_zones";
    public static final String GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT = "e573f852-5057-11e9-8fd2-b37b61e52317";
    public static final String ACCOUNTS_ID_TENANTS = "accounts_id_tenants";
    public static final String VIEW_TYPES = "view_types";
    public static final String OPENSEARCH_CACHE = "opensearchCache";
    public static final String SERVICE_CACHE = "serviceCache";

    //Metadata Constants
    //public static final String ACCOUNT = "Account";
    public static final String SERVICE = "Service";
    public static final String USER_DETAILS = "UserDetails";
    public static final String USER_ID = "userId";
    public static final String LAYER_TAG = "LayerName";
    public static final String LAYER_DEFAULT = "Type";
    public static final String SERVICE_TYPE_TAG = "ServiceType";
    public static final String SERVICE_TYPE_DEFAULT = "Type";
    public static final String ENTRY_POINT = "EntryPoint";

    public static final String AVAILABLE_RULES = "Available";
    public static final String MISSING_RULES = "Missing";
    public static final String DEFAULT_TAG_VALUE = "Type";

    // Property keys for ConfProperties.getInt(...)
    public static final String SERVICE_CLOSING_WINDOW_WITHIN_AN_HOUR_PROPERTY_NAME = "service.closing.window.within.an.hour";
    public static final String SERVICE_MAX_DATA_BREAKS_WITHIN_AN_HOUR_PROPERTY_NAME = "service.max.data.breaks.within.an.hour";
    public static final String SERVICE_CLOSING_WINDOW_MORE_THAN_AN_HOUR_PROPERTY_NAME = "service.closing.window.more.than.an.hour";
    public static final String SERVICE_MAX_DATA_BREAKS_MORE_THAN_AN_HOUR_PROPERTY_NAME = "service.max.data.breaks.more.than.an.hour";

    // Default values (fallbacks if above keys are missing in conf file)
    public static final int DEFAULT_SERVICE_CLOSING_WINDOW_WITHIN_AN_HOUR = 5;
    public static final int DEFAULT_SERVICE_MAX_DATA_BREAKS_WITHIN_AN_HOUR = 3;
    public static final int DEFAULT_SERVICE_CLOSING_WINDOW_MORE_THAN_AN_HOUR = 10;
    public static final int DEFAULT_SERVICE_MAX_DATA_BREAKS_MORE_THAN_AN_HOUR = 5;

    // Search Term
    public static final String SEARCH_TERM_KEY = "searchTerm";
    public static final String USER_ID_KEY = "userIdKey";

    //AddAccountBL tenant constants
    public static final String DEFAULT_TENANT_NAME = "heal_health_tenant";
    public static final String DEFAULT_TENANT_IDENTIFIER = "heal_health_tenant";
    public static final int DEFAULT_TENANT_STATUS = 1;
    public static final String DEFAULT_TENANT_ATTRIBUTE = "DefaultTenantId";
    public static final String CLUSTER_NAME = "101Cluster";
    public static final String PROTOCOL = "https";
    public static final int PER_ROUTE_CONNECTIONS = 10;
    public static final int MAX_CONNECTIONS = 20;
    public static final int CONNECTION_TIMEOUT_MS = 20000;
    public static final int SOCKET_TIMEOUT_MS = 30000;
    public static final int KEEP_ALIVE_SECS = 60;

    // HeaderConfig
    public static final String HEADER_X_FRAME_OPTIONS = "X-Frame-Options";
    public static final String HEADER_X_CONTENT_TYPE_OPTIONS = "X-Content-Type-Options";

    public static final String VALUE_DENY = "DENY";
    public static final String VALUE_NOSNIFF = "nosniff";
    public static final String COMP_INSTANCE_ID_VS_IDENTIFIER = "compInstanceIdVsIdentifier";

    public static final String ALL_THRESHOLDS = "AllThresholds";

    public static class Opensearch {
        //OpenSearch Indices
        public static final String INDEX_PREFIX_HEAL_SERVICE_KPI_THRESHOLDS = "heal_service_kpi_thresholds";
        public static final String INDEX_PREFIX_HEAL_INSTANCE_KPI_THRESHOLDS = "heal_instance_kpi_thresholds";
        public static final String TIMESTAMP_FORMAT_INDEX_PATTERN = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX";

        public static final String INDEX_PREFIX_HEAL_COLLATED_KPI = "heal_collated_kpi";

    }
}
