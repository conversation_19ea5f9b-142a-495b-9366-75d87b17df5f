//package com.heal.controlcenter.businesslogic;
//
//import com.appnomic.appsone.opeasearchquery.results.TabularResults;
//import com.heal.configuration.entities.UserAccessDetails;
//import com.heal.configuration.pojos.*;
//import com.heal.configuration.pojos.opensearch.ServiceKpiThresholds;
//import com.heal.controlcenter.beans.AccountBean;
//import com.heal.controlcenter.beans.CompInstClusterDetailsBean;
//import com.heal.controlcenter.beans.UtilityBean;
//import com.heal.controlcenter.beans.ViewTypesBean;
//import com.heal.controlcenter.dao.mysql.KPIDao;
//import com.heal.controlcenter.dao.mysql.ServiceDao;
//import com.heal.controlcenter.dao.mysql.entity.ComputedKpiBean;
//import com.heal.controlcenter.dao.mysql.entity.KpiDetailsBean;
//import com.heal.controlcenter.dao.mysql.entity.ServiceKpiThreshold;
//import com.heal.controlcenter.dao.opensearch.ServiceOpensearchRepo;
//import com.heal.controlcenter.exception.ClientException;
//import com.heal.controlcenter.exception.ControlCenterException;
//import com.heal.controlcenter.exception.DataProcessingException;
//import com.heal.controlcenter.exception.ServerException;
//import com.heal.controlcenter.pojo.OperationTypeEnum;
//import com.heal.controlcenter.pojo.StaticThresholdRules;
//import com.heal.controlcenter.service.CommonDataService;
//import com.heal.controlcenter.util.*;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.sql.Timestamp;
//import java.util.*;
//import java.util.stream.Collectors;
//
//@Slf4j
//@Component
//public class GetServiceStaticThresholdBL implements BusinessLogic<Object, UtilityBean<Map<String, Object>>, List<StaticThresholdRules>> {
//
//    @Autowired
//    CommonDataService commonDataService;
//    @Autowired
//    KPIDao kpiDao;
//    @Autowired
//    ServiceDao serviceDao;
//    @Autowired
//    CacheWrapper cacheWrapper;
//    @Autowired
//    ServiceOpensearchRepo serviceOpensearchRepo;
//    @Autowired
//    ClientValidationUtils clientValidationUtils;
//    @Autowired
//    ServerValidationUtils serverValidationUtils;
//    @Autowired
//    CommonUtils commonUtils;
//    @Autowired
//    DateTimeUtil dateTimeUtil;
//
//    static Map<Integer, String> computedKpiDetails;
//
//    @Override
//    public UtilityBean<Object> clientValidation(Object requestBody, String... requestParams) throws ClientException {
//        //Entry-Exit logging is handled in LoggingAspect
//
//        String authKey = requestParams[0];
//        clientValidationUtils.nullOrEmptyCheck(authKey, UIMessages.AUTH_KEY_INVALID);
//
//        String accountIdentifier = requestParams[1];
//        clientValidationUtils.nullOrEmptyCheck(accountIdentifier, UIMessages.ACCOUNT_IDENTIFIER_INVALID);
//
//        String serviceIdentifier = requestParams[2];
//        clientValidationUtils.nullOrEmptyCheck(serviceIdentifier, UIMessages.SERVICE_IDENTIFIER_INVALID);
//
//        String kpiType = requestParams[3].trim();
//        clientValidationUtils.nullOrEmptyCheck(kpiType, UIMessages.KPI_TYPE_INVALID);
//
//        String thresholdType = requestParams[4];
//        if (Objects.isNull(thresholdType) || thresholdType.trim().isEmpty() || !thresholdType.equalsIgnoreCase("static")) {
//            log.error("Invalid thresholdType. Reason: thresholdType is undefined in the request.");
//            throw new ClientException(UIMessages.THRESHOLD_TYPE_INVALID);
//        }
//
//        HashMap<String, String> requestParamsMap = new HashMap<>();
//        requestParamsMap.put(Constants.ACCOUNT_IDENTIFIER, requestParams[1]);
//        requestParamsMap.put(Constants.SERVICE_IDENTIFIER, requestParams[2]);
//        requestParamsMap.put(Constants.KPI_TYPE, kpiType);
//        requestParamsMap.put(Constants.THRESHOLD_TYPE, thresholdType);
//        requestParamsMap.put(Constants.AUTH_KEY, authKey);
//
//        return UtilityBean.builder().requestParams(requestParamsMap).build();
//    }
//
//    @Override
//    public UtilityBean<Map<String, Object>> serverValidation(UtilityBean<Object> utilityBean) throws ServerException {
//        //Entry-Exit logging is handled in LoggingAspect
//
//        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
//        String serviceIdentifier = utilityBean.getRequestParams().get(Constants.SERVICE_IDENTIFIER);
//        String kpiType = utilityBean.getRequestParams().get(Constants.KPI_TYPE).trim();
//        String authKey = utilityBean.getRequestParams().get(Constants.AUTH_KEY);
//
//        Map<String, Object> metadata = new HashMap<>();
//
//        String userId = serverValidationUtils.authKeyValidation(authKey);
//
//        Account account = serverValidationUtils.accountValidation(accountIdentifier);
//        metadata.put(Constants.ACCOUNT, account);
//
//        UserAccessDetails userAccessDetails = serverValidationUtils.userAccessDetailsValidation(userId, accountIdentifier);
//
//        Service service = serverValidationUtils.serviceValidation(userId, accountIdentifier, serviceIdentifier, userAccessDetails);
//        metadata.put(Constants.SERVICE, service);
//
//        Map<String, List<ViewTypes>> viewTypes = cacheWrapper.getAllViewTypesIdMap();
//        ViewTypes kpi = commonUtils.getViewTypeByNameAndSubType(viewTypes, Constants.KPI_TYPE, kpiType);
//        if (kpi == null || (!kpi.getSubTypeName().equalsIgnoreCase("Availability") || !kpi.getSubTypeName().equalsIgnoreCase("Core"))) {
//            log.error("Invalid KPI type [{}]. Reason: KPI type should be one of Availability or Core.", kpiType);
//            throw new ServerException(UIMessages.INVALID_KPI_TYPE);
//        }
//        metadata.put(kpiType, kpi);
//
//        ViewTypes lowThresholdSeverityType = commonUtils.getViewTypeByNameAndSubType(viewTypes, Constants.THRESHOLD_SEVERITY_TYPE, Constants.THRESHOLD_SEVERITY_TYPE_LOW);
//        if (lowThresholdSeverityType == null) {
//            log.error("Invalid thresholds view types. Subtype: {} unavailable", Constants.THRESHOLD_SEVERITY_TYPE_LOW);
//            throw new ServerException("Failure in data validation");
//        }
//        metadata.put(Constants.THRESHOLD_SEVERITY_TYPE_LOW, lowThresholdSeverityType);
//
//        ViewTypes mediumThresholdSeverityType = commonUtils.getViewTypeByNameAndSubType(viewTypes, Constants.THRESHOLD_SEVERITY_TYPE, Constants.THRESHOLD_SEVERITY_TYPE_MEDIUM);
//        if (mediumThresholdSeverityType == null) {
//            log.error("Invalid thresholds view types. Subtype: {} unavailable", Constants.THRESHOLD_SEVERITY_TYPE_MEDIUM);
//            throw new ServerException("Failure in data validation");
//        }
//
//        ViewTypes highThresholdSeverityType = commonUtils.getViewTypeByNameAndSubType(viewTypes, Constants.THRESHOLD_SEVERITY_TYPE, Constants.THRESHOLD_SEVERITY_TYPE_HIGH);
//        if (highThresholdSeverityType == null) {
//            log.error("Invalid thresholds view types. Subtype: {} unavailable", Constants.THRESHOLD_SEVERITY_TYPE_HIGH);
//            throw new ServerException("Failure in data validation");
//        }
//        metadata.put(Constants.THRESHOLD_SEVERITY_TYPE_HIGH, highThresholdSeverityType);
//
//        ViewTypes lessThanOperationType = commonUtils.getViewTypeByNameAndSubType(viewTypes, Constants.OPERATIONS_TYPE, Constants.OPERATIONS_TYPE_LESSER_THAN);
//        if (lessThanOperationType == null) {
//            log.error("Invalid operation view types. Subtype: {} unavailable", Constants.OPERATIONS_TYPE_LESSER_THAN);
//            throw new ServerException("Failure in data validation");
//        }
//        metadata.put(Constants.OPERATIONS_TYPE_LESSER_THAN, lessThanOperationType);
//
//        ViewTypes greaterThanOperationType = commonUtils.getViewTypeByNameAndSubType(viewTypes, Constants.OPERATIONS_TYPE, Constants.OPERATIONS_TYPE_GREATER_THAN);
//        if (greaterThanOperationType == null) {
//            log.error("Invalid operation view types. Subtype: {} unavailable", Constants.OPERATIONS_TYPE_GREATER_THAN);
//            throw new ServerException("Failure in data validation");
//        }
//        metadata.put(Constants.OPERATIONS_TYPE_GREATER_THAN, greaterThanOperationType);
//
//        ViewTypes notBetweenOperationType = commonUtils.getViewTypeByNameAndSubType(viewTypes, Constants.OPERATIONS_TYPE, Constants.OPERATIONS_TYPE_NOT_BETWEEN);
//        if (notBetweenOperationType == null) {
//            log.error("Invalid operation view types. Subtype: {} unavailable", Constants.OPERATIONS_TYPE_NOT_BETWEEN);
//            throw new ServerException("Failure in data validation");
//        }
//        metadata.put(Constants.OPERATIONS_TYPE_NOT_BETWEEN, notBetweenOperationType);
//
//        return UtilityBean.<Map<String, Object>>builder()
//                .requestParams(utilityBean.getRequestParams())
//                .pojoObject(metadata)
//                .build();
//    }
//
//    @Override
//    public List<StaticThresholdRules> process(UtilityBean<Map<String, Object>> bean) throws DataProcessingException {
//        //Entry-Exit logging is handled in LoggingAspect
//        Map<String, Object> metadata = bean.getPojoObject();
//        Service service = (Service) metadata.get(Constants.SERVICE);
//        int serviceId = service.getId();
//        String serviceIdentifier = service.getIdentifier();
//        AnomalyConfiguration serviceConfiguration = service.getServiceConfiguration().getAnomalyConfiguration();
//
//        Account account = (Account) metadata.get(Constants.ACCOUNT);
//        int accountId = account.getId();
//        String accountIdentifier = account.getIdentifier();
//
//        String kpiType = bean.getRequestParams().get(Constants.KPI_TYPE).trim();
//        try {
//            //Fetching the clusters mapped to the service to just fetch the KPIs.
//            List<CompInstClusterDetailsBean> serviceCompInstances = commonDataService.getComponentClusterList(serviceId, accountId);
//            if (serviceCompInstances.isEmpty()) {
//                log.info("Component instances unavailable for the serviceId [{}] provided.", serviceId);
//                return Collections.emptyList();
//            }
//
//            ViewTypes lowThreshold = (ViewTypes) metadata.get(Constants.THRESHOLD_SEVERITY_TYPE_LOW);
//            ViewTypes mediumThreshold = (ViewTypes) metadata.get(Constants.THRESHOLD_SEVERITY_TYPE_MEDIUM);
//            ViewTypes highThreshold = (ViewTypes) metadata.get(Constants.THRESHOLD_SEVERITY_TYPE_HIGH);
//
//            List<ServiceKpiThreshold> kpiThresholdList = serviceDao.getAllKpiThresholds(accountId, serviceId).stream()
//                    .filter(k -> k.getOperationTypeId() > 0)
//                    .filter(k -> (serviceConfiguration.isLowEnable() && k.getThresholdSeverityId() == lowThreshold.getSubTypeId())
//                            || (serviceConfiguration.isMediumEnable() && k.getThresholdSeverityId() == mediumThreshold.getSubTypeId())
//                            || (serviceConfiguration.isHighEnable() && k.getThresholdSeverityId() == highThreshold.getSubTypeId()))
//                    .collect(Collectors.toList());
//
//            Set<String> thresholdKpiStrIds = kpiThresholdList.stream()
//                    .map(ServiceKpiThreshold::getKpiId)
//                    .map(String::valueOf)
//                    .collect(Collectors.toSet());
//
//            TabularResults allConfigureThresholdsKpiListsFromOS = serviceOpensearchRepo.getAllConfigureThresholdsKpiListsFromOS(accountIdentifier, service.getIdentifier(), thresholdKpiStrIds);
//            Map<Integer, List<String>> configuredThresholdsFromOS = extractResult(allConfigureThresholdsKpiListsFromOS);
//
//            ViewTypes kpiViewType = (ViewTypes) metadata.get(kpiType);
//
//            List<KpiDetailsBean> kpiDetailsList = serviceCompInstances.stream()
//                    .map(compInstance -> {
//                        List<KpiDetailsBean> kpiDetailBeans = kpiDao.getKpiList(compInstance.getCommonVersionId(), compInstance.getCompId());
//                        if (kpiDetailBeans == null || kpiDetailBeans.isEmpty()) {
//                            log.info("Kpi Bean list is invalid for commonVersionId: {}, compId: {}", compInstance.getCommonVersionId(), compInstance.getCompId());
//                            return Collections.<KpiDetailsBean>emptyList();
//                        }
//
//                        return kpiDetailBeans.stream()
//                                .filter(kpi -> kpi.getTypeId() == kpiViewType.getSubTypeId())
//                                .toList();
//
//                    }).flatMap(Collection::stream).toList();
//
//            Map<String, Set<StaticThresholdRules>> allRules = getStaticThresholdByKpiId(kpiViewType, kpiDetailsList, kpiThresholdList, metadata, configuredThresholdsFromOS);
//
//           List<StaticThresholdRules> rules = allRules
//                    .getOrDefault(Constants.AVAILABLE_RULES, new HashSet<>())
//                    .stream()
//                    .sorted(Comparator.comparing(StaticThresholdRules::getCategoryName).thenComparing(StaticThresholdRules::getKpiName).thenComparing(StaticThresholdRules::getKpiLevel))
//                    .toList();
//
//            log.info("Static threshold rules count: {}, service: {}, account: {}", rules.size(), serviceIdentifier, accountIdentifier);
//
//            Set<StaticThresholdRules> missingThresholdRules = allRules.getOrDefault(Constants.MISSING_RULES, new HashSet<>());
//            if (!missingThresholdRules.isEmpty()) {
//                Timestamp echoMilli = dateTimeUtil.getCurrentTimestampInGMT();
//                long st = System.currentTimeMillis();
//                serviceOpensearchRepo.insertBulkServiceKpiThresholdsIntoOS(accountIdentifier, service.getIdentifier(),List.copyOf(missingThresholdRules), echoMilli, "");
//                log.debug("Total time taken to insert missing thresholds into OS for data: {} ms", (System.currentTimeMillis() - st));
//            }
//            return rules;
//        } catch (ControlCenterException e) {
//            log.error("Error occurred while getting static threshold by kpiId for accountId: {}, serviceId: {}. Details: ", accountIdentifier, service.getIdentifier(), e);
//            throw new RuntimeException(e);
//        }
//    }
//
//    private Map<String, Set<StaticThresholdRules>> getStaticThresholdByKpiId(ViewTypes kpiType, List<KpiDetailsBean> kpiDetailsBeanList,
//                                                                List<ServiceKpiThreshold> kpiThresholdList, Map<String, Object> metadata,
//                                                                Map<Integer, List<String>> configuredThresholdsFromOS) throws ControlCenterException {
//
//        Map<String, Set<StaticThresholdRules>> output = new HashMap<>();
//        Set<StaticThresholdRules> resultList = new HashSet<>();
//        Set<StaticThresholdRules> missingRulesList = new HashSet<>();
//
//        HashMap<Integer, KpiCategoryDetails> kpiCategoryDetailsMap = kpiDao.getAllKpiCategoryDetails().stream()
//                .collect(Collectors.toMap(KpiCategoryDetails::getId, kpiCategoryDetails -> kpiCategoryDetails,
//                        (existing, replacement) -> existing, HashMap::new));
//
//        ViewTypes lowThreshold = (ViewTypes) metadata.get(Constants.THRESHOLD_SEVERITY_TYPE_LOW);
//        ViewTypes mediumThreshold = (ViewTypes) metadata.get(Constants.THRESHOLD_SEVERITY_TYPE_MEDIUM);
//        ViewTypes highThreshold = (ViewTypes) metadata.get(Constants.THRESHOLD_SEVERITY_TYPE_HIGH);
//
//        for (KpiDetailsBean kpiDetailsBean : kpiDetailsBeanList) {
//            KpiCategoryDetails kpiCategoryDetailBean = kpiCategoryDetailsMap.get(kpiDetailsBean.getId());
//            if (kpiCategoryDetailBean == null) {
//                log.error("Category details unavailable for kpi: {}", kpiDetailsBean);
//                continue;
//            }
//
//            List<ServiceKpiThreshold> kpiThresholds = kpiThresholdList.stream()
//                    .filter(f -> f.getKpiId() == kpiDetailsBean.getId()).toList();
//
//            if (!kpiThresholds.isEmpty()) {
//                Map<Integer, List<ServiceKpiThreshold>> severityWiseThresholds = kpiThresholds.stream().collect(Collectors.groupingBy(ServiceKpiThreshold::getThresholdSeverityId));
//
//                //Adding the placeholder static thresholds for instances and/or clusters i.e., for example, if the thresholds are configured
//                // only for a particular cluster, we need to populate a placeholder threshold object for the corresponding instance.
//                Set<StaticThresholdRules> placeholderList = severityWiseThresholds.values().stream().map(matchingThresholds -> {
//                    if (kpiType.getSubTypeName().equals(Constants.CORE_KPI_TYPE) && matchingThresholds.size() == 1) {
//                        boolean clusters = matchingThresholds.get(0).getApplicableTo().equalsIgnoreCase("Clusters");
//                        return populateStaticThresholdRulesByKpiDetail(!clusters, Constants.CORE_KPI_TYPE, kpiDetailsBean, kpiCategoryDetailBean);
//                    }
//                    return null;
//                }).filter(Objects::nonNull).collect(Collectors.toSet());
//
//                resultList.addAll(placeholderList);
//
//                for (ServiceKpiThreshold threshold : kpiThresholds) {
//                    StaticThresholdRules rule = populateStaticThresholdRules(kpiCategoryDetailBean, threshold, kpiDetailsBean, lowThreshold, mediumThreshold, highThreshold);
//                    List<String> applicableTo = configuredThresholdsFromOS.getOrDefault(kpiDetailsBean.getId(), Collections.emptyList());
//                    if (!applicableTo.contains(threshold.getApplicableTo()) && rule.isGenerateAnomaly()) {
//                        log.trace("Threshold missing in OS, inserting for KPI [{}], applicableTo [{}]", kpiDetailsBean.getId(), threshold.getApplicableTo());
//                        missingRulesList.add(rule);
//                    } else {
//                        log.trace("Threshold already exists in OS, skipping for KPI [{}], applicableTo [{}]", kpiDetailsBean.getId(), threshold.getApplicableTo());
//                    }
//                    resultList.add(rule);
//                }
//
//                //resultList.addAll(checkIfBothLevelKpiIsInTheList(configuredThresholdsFromOS, kpiThresholds, kpiDetailsBean, kpiType, kpiCategoryDetailBean));
//            } else {
//                log.debug("This KpiId {} is not present in the service kpi threshold. " +
//                        "Hence an entry without threshold values will be inserted for this KpiId.", kpiDetailsBean.getId());
//                if (kpiType.getSubTypeName().equals(Constants.CORE_KPI_TYPE)) {
//                    resultList.add(populateStaticThresholdRulesByKpiDetail(true, Constants.CORE_KPI_TYPE, kpiDetailsBean, kpiCategoryDetailBean));
//                    resultList.add(populateStaticThresholdRulesByKpiDetail(false, Constants.CORE_KPI_TYPE, kpiDetailsBean, kpiCategoryDetailBean));
//                } else {
//                    resultList.add(populateStaticThresholdRulesByKpiDetail(false, Constants.AVAIL_KPI_TYPE, kpiDetailsBean, kpiCategoryDetailBean));
//                }
//            }
//        }
//
//        output.put(Constants.AVAILABLE_RULES, resultList);
//        output.put(Constants.MISSING_RULES, missingRulesList);
//
//        return output;
//    }
//
//    private StaticThresholdRules populateStaticThresholdRules(KpiCategoryDetails kpiCategoryDetailBean, ServiceKpiThreshold kpiThreshold, KpiDetailsBean kpiDetailsBean,
//                                                              ViewTypes lowThreshold, ViewTypes mediumThreshold, ViewTypes highThreshold) throws ControlCenterException {
//        StaticThresholdRules staticThresholdRules = new StaticThresholdRules();
//        staticThresholdRules.setKpiName(kpiDetailsBean.getName());
//        staticThresholdRules.setKpiId(String.valueOf(kpiThreshold.getKpiId()));
//        staticThresholdRules.setDataId(kpiThreshold.getId());
//        staticThresholdRules.setKpiLevel(kpiThreshold.getApplicableTo());
//        staticThresholdRules.setCategoryId(kpiCategoryDetailBean.getId());
//        staticThresholdRules.setCategoryName(kpiCategoryDetailBean.getName());
//        staticThresholdRules.setKpiAttribute(kpiThreshold.getKpiAttribute());
//        staticThresholdRules.setKpiDataType(kpiDetailsBean.getDataType());
//        staticThresholdRules.setCoverageWindow(kpiThreshold.getCoverageWindow());
//        staticThresholdRules.setComputedDetails(StaticThresholdRules.ComputedDetails.builder()
//                .formula(kpiDetailsBean.getComputedFormula())
//                .build());
//
//        staticThresholdRules.setKpiUnit(kpiDetailsBean.getMeasureUnits());
//        staticThresholdRules.setStartTime(kpiThreshold.getStartTime());
//        if (kpiThreshold.getStatus() == 1) {
//            staticThresholdRules.setGenerateAnomaly(true);
//        }
//
//        ViewTypes customOperationType = cacheWrapper.getAllViewTypesIdMap().getOrDefault(Constants.OPERATIONS_TYPE, new ArrayList<>())
//                .stream().filter(s -> s.getSubTypeId() == kpiThreshold.getOperationTypeId()).findFirst().orElse(null);
//        if (customOperationType == null) {
//            log.error("Invalid Operation Type for {} id.", kpiThreshold.getOperationTypeId());
//            throw new ControlCenterException("Invalid operation type");
//        }
//
//        if (kpiThreshold.getThresholdSeverityId() == lowThreshold.getSubTypeId()) {
//            staticThresholdRules.setLowThreshold(ThresholdConfig.builder()
//                    .operationType(customOperationType.getSubTypeName())
//                    .max(kpiThreshold.getMaxThreshold())
//                    .min(kpiThreshold.getMinThreshold())
//                    .build());
//        } else if (kpiThreshold.getThresholdSeverityId() == mediumThreshold.getSubTypeId()) {
//            staticThresholdRules.setWarningThreshold(ThresholdConfig.builder()
//                    .operationType(customOperationType.getSubTypeName())
//                    .max(kpiThreshold.getMaxThreshold())
//                    .min(kpiThreshold.getMinThreshold())
//                    .build());
//        } else if (kpiThreshold.getThresholdSeverityId() == highThreshold.getSubTypeId()) {
//            staticThresholdRules.setErrorThreshold(ThresholdConfig.builder()
//                    .operationType(customOperationType.getSubTypeName())
//                    .max(kpiThreshold.getMaxThreshold())
//                    .min(kpiThreshold.getMinThreshold())
//                    .build());
//        }
//
//        return staticThresholdRules;
//    }
//
//    protected StaticThresholdRules populateStaticThresholdRulesByKpiDetail(boolean isCluster, String kpiType, KpiDetailsBean kpiDetailsBean,
//                                                                           KpiCategoryDetails kpiCategoryDetailBean) {
//        int temp = kpiDetailsBean.getId();
//        StaticThresholdRules staticThresholdRules = new StaticThresholdRules();
//        staticThresholdRules.setKpiName(kpiDetailsBean.getName());
//        staticThresholdRules.setKpiId(Integer.toString(temp));
//        staticThresholdRules.setDataId(0);
//        staticThresholdRules.setKpiLevel(isCluster ? "clusters" : "instances");
//        staticThresholdRules.setCategoryId(kpiCategoryDetailBean.getId());
//        staticThresholdRules.setCategoryName(kpiCategoryDetailBean.getName());
//        staticThresholdRules.setKpiAttribute(Constants.ALL);
//        staticThresholdRules.setKpiDataType(kpiDetailsBean.getDataType());
//        staticThresholdRules.setKpiUnit(kpiDetailsBean.getMeasureUnits());
//        staticThresholdRules.setComputedDetails(StaticThresholdRules.ComputedDetails.builder()
//                .formula(computedKpiDetails.get(temp))
//                .build());
//
//        if (Constants.AVAIL_KPI_TYPE.equalsIgnoreCase(kpiType)) {
//            staticThresholdRules.setGenerateAnomaly(false);
//            staticThresholdRules.setUserDefinedSOR(true);
//        } else {
//            staticThresholdRules.setGenerateAnomaly(true);
//            staticThresholdRules.setUserDefinedSOR(false);
//        }
//
//        return staticThresholdRules;
//    }
//
//    private static Map<Integer, List<String>> extractResult(TabularResults results) {
//        Map<Integer, List<String>> kpiIdVSApplicableToMap = new HashMap<>();
//
//        if (results != null && results.getRowResults() != null && !results.getRowResults().isEmpty()) {
//            for (TabularResults.ResultRow resultRow : results.getRowResults()) {
//                int kpiId = 0;
//                String applicableTo = "";
//                for (TabularResults.ResultRow.ResultRowColumn resultRowColumn : resultRow.getListOfRows()) {
//                    if (resultRowColumn.getColumnName().equalsIgnoreCase("kpiId")) {
//                        kpiId = Integer.parseInt(resultRowColumn.getColumnValue());
//
//                    }
//                    if (resultRowColumn.getColumnName().equalsIgnoreCase("applicableTo")) {
//                        applicableTo = resultRowColumn.getColumnValue();
//                    }
//                }
//
//                List<String> exists = kpiIdVSApplicableToMap.getOrDefault(kpiId, new ArrayList<>());
//                exists.add(applicableTo);
//                kpiIdVSApplicableToMap.put(kpiId, exists);
//
//            }
//        }
//        return kpiIdVSApplicableToMap;
//    }
//
//}
