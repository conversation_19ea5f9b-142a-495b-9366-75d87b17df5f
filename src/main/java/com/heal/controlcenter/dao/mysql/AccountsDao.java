package com.heal.controlcenter.dao.mysql;

import com.heal.controlcenter.beans.AccountAnomalyConfigurationBean;
import com.heal.controlcenter.beans.AccountBean;
import com.heal.controlcenter.beans.TimezoneBean;
import com.heal.controlcenter.exception.ControlCenterException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.util.List;

@Slf4j
@Repository
public class AccountsDao {

    @Autowired
    JdbcTemplate jdbcTemplate;

    public List<AccountBean> getAccounts(String searchTerm, int page, int size) throws ControlCenterException {
        long startTime = System.currentTimeMillis();
        String baseQuery = "SELECT a.identifier AS identifier, a.id AS id, a.name AS name, a.public_key AS publicKey, " +
                "a.private_key AS privateKey, a.user_details_id AS userIdDetails, a.status AS status, " +
                "a.updated_time AS updatedTime, a.created_time AS createdTime, a.user_details_id AS lastModifiedBy " +
                "FROM account a WHERE a.status = 1";
        List<Object> params = new java.util.ArrayList<>();
        if (searchTerm != null && !searchTerm.isEmpty()) {
            baseQuery += " AND (LOWER(a.name) LIKE ? OR LOWER(a.identifier) LIKE ?)";
            params.add("%" + searchTerm.toLowerCase() + "%");
            params.add("%" + searchTerm.toLowerCase() + "%");
        }
        baseQuery += " ORDER BY a.updated_time DESC LIMIT ? OFFSET ?";
        params.add(size);
        params.add(page * size);
        try {
            log.debug("getting paginated and filtered accounts list");
            return jdbcTemplate.query(baseQuery, new BeanPropertyRowMapper<>(AccountBean.class), params.toArray());
        } catch (Exception ex) {
            log.error("Error in fetching accounts (searchTerm: {}, page: {}, size: {})", searchTerm, page, size, ex);
            throw new ControlCenterException("Error in fetching accounts");
        } finally {
            long endTime = System.currentTimeMillis();
            log.info("getAccounts(searchTerm: {}, page: {}, size: {}) completed in {} ms", searchTerm, page, size, (endTime - startTime));
        }
    }

    public int countAccounts(String searchTerm) throws ControlCenterException {
        long startTime = System.currentTimeMillis();
        String countQuery = "SELECT COUNT(*) FROM account a WHERE a.status = 1";
        List<Object> params = new java.util.ArrayList<>();
        if (searchTerm != null && !searchTerm.isEmpty()) {
            countQuery += " AND (LOWER(a.name) LIKE ? OR LOWER(a.identifier) LIKE ?)";
            params.add("%" + searchTerm.toLowerCase() + "%");
            params.add("%" + searchTerm.toLowerCase() + "%");
        }
        try {
            return jdbcTemplate.queryForObject(countQuery, Integer.class, params.toArray());
        } catch (Exception ex) {
            log.error("Error in counting accounts (searchTerm: {})", searchTerm, ex);
            throw new ControlCenterException("Error in counting accounts");
        } finally {
            long endTime = System.currentTimeMillis();
            log.info("countAccounts(searchTerm: {}) completed in {} ms", searchTerm, (endTime - startTime));
        }
    }

    public AccountBean getAccountDetailsForIdentifier(String accountIdentifier) {
        String query = "select a.id as id, a.status, a.name, a.identifier, a.user_details_id as userDetailsId " +
                "FROM account a where a.identifier=?";

        try {
            return jdbcTemplate.queryForObject(query, BeanPropertyRowMapper.newInstance(AccountBean.class), accountIdentifier);
        } catch (Exception e) {
            log.error("Exception encountered while fetching accounts information for identifier [{}]. Details: ", accountIdentifier, e);
        }

        return null;
    }
    /**
     * Fetches timezone details for the specified account ID.
     * Joins `tag_mapping`, `mst_timezone`, and `tag_details` tables to retrieve timezone metadata.
     *
     * @param accountId ID of the account
     * @return TimezoneBean containing timezone details, or null if no data is found
     * @throws ControlCenterException If any error occurs during database operation
     */
    public TimezoneBean getAccountTimezoneDetails(int accountId) throws ControlCenterException {
        try {
            String query = "select mt.id id, mt.timeoffset offset, mt.time_zone_id timeZoneId, mt.account_id accountId " +
                    "from tag_mapping tm, mst_timezone mt, tag_details td " +
                    "where tm.object_id = ? and tm.object_ref_table = 'account' and tm.tag_id = td.id " +
                    "and td.name ='Timezone' and mt.id = tm.tag_key";
            return jdbcTemplate.queryForObject(query,
                    new BeanPropertyRowMapper<>(TimezoneBean.class), accountId);
        } catch (EmptyResultDataAccessException e) {
            log.warn("No timezone found for accountId {}. Skipping timezone.", accountId);
            return null;
        } catch (Exception e) {
            log.error("Error while getting time zone for accountId {}. Details: {}", accountId, e.getMessage(), e);
            throw new ControlCenterException("Error in fetching timezones for account ID: " + accountId);
        }
    }

    public AccountBean getAccountByIdentifier(String identifier) {
        String query = "select identifier, id, name, public_key publicKey, private_key privateKey, " +
                "user_details_id lastModifiedBy, status, updated_time updatedTime, created_time createdTime " +
                "from account where status = 1 and identifier=?";
        try {
            log.debug("getting account details.");
            return jdbcTemplate.queryForObject(query, new BeanPropertyRowMapper<>(AccountBean.class), identifier);
        } catch (Exception e) {
            log.error("Error occurred while fetching account details from 'account' table for identifier [{}]. Details: {}", identifier, e.getMessage(), e);
        }
        return null;
    }

    /**
     * Checks whether an account exists with the given identifier.
     *
     * @param identifier Unique identifier of the account
     * @return true if an account with the identifier exists, false otherwise
     */
    public boolean existsByIdentifier(String identifier) {
        String sql = "SELECT COUNT(1) FROM account WHERE identifier = ?";
        Integer count = jdbcTemplate.queryForObject(sql, Integer.class, identifier);
        return count != null && count > 0;
    }

    /**
     * Retrieves the account ID based on the given account name.
     *
     * @param accountName Name of the account to search
     * @return Account ID if found, 0 if not found
     * @throws ControlCenterException If multiple accounts are found or a database error occurs
     */
    public int getAccountByName(String accountName) throws ControlCenterException {
        String query = "SELECT id FROM account WHERE name = ?";
        try {
            log.debug("Fetching account ID by name: {}", accountName);

            List<Integer> ids = jdbcTemplate.query(query, (rs, rowNum) -> rs.getInt("id"), accountName);

            if (ids.size() > 1) {
                log.error("Multiple accounts found for name: {}", accountName);
                throw new ControlCenterException("Multiple accounts found for name: " + accountName);
            } else if (ids.size() == 1) {
                return ids.get(0);
            } else {
                return 0;
            }

        } catch (DataAccessException e) {
            log.error("Database error fetching account ID for name: {}", accountName, e);
            throw new ControlCenterException(e, "Error fetching account ID by name");
        }
    }

    /**
     * Inserts a new account into the database and returns the generated account ID.
     *
     * @param accountBean Bean containing account details to be inserted
     * @return The generated ID of the newly created account
     * @throws ControlCenterException If the insert fails or the ID is not generated
     */
    public int addAccount(AccountBean accountBean) throws ControlCenterException {
        String sql = "INSERT INTO account (name, created_time, updated_time, status, private_key, public_key, user_details_id, identifier) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
        try {
            // Using jdbcTemplate to insert and get generated key (assuming id is auto-increment)
            KeyHolder keyHolder = new GeneratedKeyHolder();

            jdbcTemplate.update(connection -> {
                PreparedStatement ps = connection.prepareStatement(sql, new String[]{"id"});
                ps.setString(1, accountBean.getName());
                ps.setString(2, accountBean.getCreatedTime());
                ps.setString(3, accountBean.getUpdatedTime());
                ps.setInt(4, accountBean.getStatus());
                ps.setString(5, accountBean.getPrivateKey());
                ps.setString(6, accountBean.getPublicKey());
                ps.setString(7, accountBean.getLastModifiedBy());
                ps.setString(8, accountBean.getIdentifier());
                return ps;
            }, keyHolder);

            //Return generated id
            Number key = keyHolder.getKey();
            if (key == null || key.intValue() == 0) {
                log.error("Failed to retrieve generated account ID for account: {}", accountBean);
                throw new ControlCenterException("Failed to retrieve generated account ID");
            }
            return key.intValue();

        } catch (DataAccessException e) {
            log.error("Error inserting account: {}", accountBean, e);
            throw new ControlCenterException("Error inserting account");
        }
    }

    /**
     * Inserts anomaly configuration details for a specific account into the database.
     * Includes thresholds for low, medium, and high severities along with closing window and max data breaks.
     *
     * @param accountId                       ID of the account
     * @param accountAnomalyConfigurationBean Bean containing anomaly configuration details
     * @param userDetailsId                   User who is inserting the configuration
     * @throws ControlCenterException If there is a database error or unexpected input
     */
    public void insertAccountAnomalyConfigurations(int accountId, AccountAnomalyConfigurationBean accountAnomalyConfigurationBean, String userDetailsId) throws ControlCenterException {
        String query = "INSERT INTO account_anomaly_configurations " +
                "(account_id, user_details_id, created_time, updated_time, " +
                "low_enable, medium_enable, high_enable, closing_window, max_data_breaks) " +
                "VALUES (?, ?, NOW(), NOW(), ?, ?, ?, ?, ?)";

        try {
            int lowEnable = accountAnomalyConfigurationBean.isLowEnable() ? 1 : 0;
            int mediumEnable = accountAnomalyConfigurationBean.isMediumEnable() ? 1 : 0;
            int highEnable = accountAnomalyConfigurationBean.isHighEnable() ? 1 : 0;

            int closingWindow = accountAnomalyConfigurationBean.getClosingWindow();
            int maxDataBreaks = accountAnomalyConfigurationBean.getMaxDataBreaks();

            jdbcTemplate.update(query,
                    accountId,
                    userDetailsId,
                    lowEnable,
                    mediumEnable,
                    highEnable,
                    closingWindow,
                    maxDataBreaks);

            log.debug("Successfully inserted anomaly configuration for accountId [{}]", accountId);
        } catch (DataAccessException e) {
            log.error("Database error while inserting anomaly configuration for accountId [{}],account_anomaly_configurations: {}", accountId, accountAnomalyConfigurationBean, e);
            throw new ControlCenterException("Database error during anomaly configuration insert");
        } catch (NumberFormatException e) {
            log.error("Invalid number format in anomaly config input for accountId [{}], account_anomaly_configurations: {}", accountId, accountAnomalyConfigurationBean, e);
            throw new ControlCenterException("Invalid numeric value in request");
        } catch (Exception e) {
            log.error("Unexpected error while inserting anomaly configuration for accountId [{}], account_anomaly_configurations: {}", accountId, accountAnomalyConfigurationBean, e);
            throw new ControlCenterException("Unexpected error occurred during anomaly configuration insert");
        }
    }

    /**
     * Retrieves the anomaly configuration details for the specified account ID.
     *
     * @param accountId ID of the account
     * @return AccountAnomalyConfigurationBean containing configuration details, or null if not found
     * @throws ControlCenterException If any database error occurs during fetch
     */
    public AccountAnomalyConfigurationBean getAccountAnomalyConfiguration(int accountId) throws ControlCenterException {
        String query = "SELECT account_id AS accountId, closing_window AS closingWindow, max_data_breaks AS maxDataBreaks, " +
                "low_enable AS lowEnable, medium_enable AS mediumEnable, high_enable AS highEnable " +
                "FROM account_anomaly_configurations WHERE account_id = ?";

        try {
            return jdbcTemplate.queryForObject(
                    query,
                    new BeanPropertyRowMapper<>(AccountAnomalyConfigurationBean.class),
                    accountId
            );
        } catch (EmptyResultDataAccessException e) {
            log.warn("No anomaly configuration found for accountId {}.", accountId);
            return null;
        } catch (Exception e) {
            log.error("Error while getting anomaly configuration for accountId {}. Details: ", accountId, e);
            throw new ControlCenterException("Error in fetching anomaly configuration for account ID: " + accountId);
        }
    }
}
