package com.heal.controlcenter.businesslogic;

import com.heal.controlcenter.beans.AccountBean;
import com.heal.controlcenter.beans.SMTPDetailsBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.beans.ViewTypesBean;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.dao.mysql.MasterDataDao;
import com.heal.controlcenter.dao.mysql.NotificationsDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.SMTPDetailsPojo;
import com.heal.controlcenter.util.AECSBouncyCastleUtil;
import com.heal.controlcenter.util.CommonUtils;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.DateTimeUtil;
import org.bouncycastle.crypto.InvalidCipherTextException;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.sql.Timestamp;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@PrepareForTest({PutEmailConfigurationsBL.class})
class PutEmailConfigurationsBLTest {

    @InjectMocks
    PutEmailConfigurationsBL putEmailConfigurationsBL;

    @Mock
    CommonUtils commonUtils;
    @Mock
    AccountsDao accountsDao;
    @Mock
    NotificationsDao notificationsDao;
    @Mock
    AECSBouncyCastleUtil aecsBouncyCastleUtil;
    @Mock
    MasterDataDao masterDataDao;
    @Mock
    DateTimeUtil dateTimeUtil;

    String[] requestParams = new String[2];
    UtilityBean<SMTPDetailsPojo> mockUtilityBean = null;
    SMTPDetailsPojo smtpDetailsPojo = new SMTPDetailsPojo();
    SMTPDetailsBean smtpDetailsBean = new SMTPDetailsBean();
    AccountBean account = new AccountBean();
    ViewTypesBean securityType = new ViewTypesBean();

    @BeforeEach
    void setUp() {
        requestParams[0] = "mockAuthToken";
        requestParams[1] = "mockAccountIdentifier";

        account.setId(2);

        smtpDetailsPojo.setSecurity("SSL");
        smtpDetailsPojo.setAddress("trial");
        smtpDetailsPojo.setPort(8996);
        smtpDetailsPojo.setFromRecipient("<EMAIL>");
        smtpDetailsPojo.setUsername("heal");
        smtpDetailsPojo.setPassword("password");

        smtpDetailsBean.setSecurityId(152);
        smtpDetailsBean.setAddress("trial");
        smtpDetailsBean.setPort(8996);
        smtpDetailsBean.setFromRecipient("<EMAIL>");
        smtpDetailsBean.setUsername("heal");
        smtpDetailsBean.setPassword("password");
        smtpDetailsBean.setAccountId(2);
        smtpDetailsBean.setLastModifiedBy("mockUserId");
        smtpDetailsBean.setCreatedTime("2021-12-02 02:02:02");
        smtpDetailsBean.setUpdatedTime("2021-12-02 02:02:02");
        smtpDetailsBean.setStatus(1);

        mockUtilityBean = UtilityBean.<SMTPDetailsPojo>builder()
                .requestParams(Map.of(
                        "Authorization", "mockAuthToken",
                        "accountIdentifier", "mockAccountIdentifier"
                ))
                .metadata(Map.of(
                        "userId", "mockUserId"
                ))
                .pojoObject(smtpDetailsPojo)
                .account(account)
                .build();

        securityType.setTypeName("SMTP Security");
        securityType.setSubTypeId(152);
        securityType.setTypeId(49);
        securityType.setSubTypeName("SSL");
    }

    @AfterEach
    void tearDown() {
        requestParams = null;
        mockUtilityBean = null;
    }

    @Test
    void clientValidations_EmptyAuthorizationToken() {
        requestParams[0] = "";
        requestParams[1] = "mockAccountIdentifier";

        String expectedMessage = "ClientException : Invalid authorization token";
        ClientException requestException = assertThrows(ClientException.class, () ->
                putEmailConfigurationsBL.clientValidation(smtpDetailsPojo, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidations_EmptyAccountIdentifier() {
        requestParams[0] = "mockAuthToken";
        requestParams[1] = "";

        String expectedMessage = "ClientException : Invalid account identifier";
        ClientException requestException = assertThrows(ClientException.class, () ->
                putEmailConfigurationsBL.clientValidation(smtpDetailsPojo, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidations_ErrorWhileFetchingUserId() throws Exception {
        String expectedMessage = "ClientException : Error occurred while fetching userId from authorization token";
        when(commonUtils.getUserId("mockAuthToken")).thenThrow(ControlCenterException.class);
        ClientException requestException = assertThrows(ClientException.class, () ->
                putEmailConfigurationsBL.clientValidation(smtpDetailsPojo, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidations_InvalidRequestBody() throws Exception {
        String expectedMessage = "ClientException : {SMTP address=Address is either NULL, empty or its length is greater than 256 characters.}";
        smtpDetailsPojo.setAddress("");

        when(commonUtils.getUserId("mockAuthToken")).thenReturn(any());
        ClientException requestException = assertThrows(ClientException.class, () ->
                putEmailConfigurationsBL.clientValidation(smtpDetailsPojo, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidations_Success() throws Exception {
        String userId = "mockUserId";
        when(commonUtils.getUserId("mockAuthToken")).thenReturn(userId);
        UtilityBean<SMTPDetailsPojo> utilityBean = putEmailConfigurationsBL.clientValidation(smtpDetailsPojo, requestParams);
        assertEquals(utilityBean.getMetadata().get("userId"), userId);
    }

    @Test
    void serverValidation_InvalidAccountIdentifier() {
        String expectedMessage = "ServerException : Account with identifier [mockAccountIdentifier] is unavailable";
        when(accountsDao.getAccountByIdentifier("mockAccountIdentifier")).thenReturn(any());
        ServerException requestException = assertThrows(ServerException.class, () ->
                putEmailConfigurationsBL.serverValidation(mockUtilityBean));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void serverValidation_Success() throws Exception {
        String userId = "mockUserId";

        when(accountsDao.getAccountByIdentifier("mockAccountIdentifier")).thenReturn(account);
        UtilityBean<SMTPDetailsPojo> utilityBean = putEmailConfigurationsBL.serverValidation(mockUtilityBean);
        assertEquals(utilityBean.getMetadata().get("userId"), userId);
    }

    @Test
    void process_Success() throws Exception {
        when(dateTimeUtil.getCurrentTimestampInGMT()).thenReturn(Timestamp.valueOf("2021-12-02 02:02:02"));
        when(notificationsDao.getSMTPDetails(2)).thenReturn(smtpDetailsBean);
        when(masterDataDao.getViewTypesFromMstTypeAndSubTypeName(Constants.SMTP_PROTOCOLS, mockUtilityBean.getPojoObject().getSecurity())).thenReturn(securityType);
        when(aecsBouncyCastleUtil.decrypt(mockUtilityBean.getPojoObject().getPassword())).thenReturn("password");
        putEmailConfigurationsBL.process(mockUtilityBean);
    }

    @Test
    void process_Failure_NoConfigurations() {
        String expectedMessage = "DataProcessingException : Error occurred, Email details not found.";
        when(dateTimeUtil.getCurrentTimestampInGMT()).thenReturn(Timestamp.valueOf("2021-12-02 02:02:02"));
        DataProcessingException requestException = assertThrows(DataProcessingException.class, () ->
                putEmailConfigurationsBL.process(mockUtilityBean));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void process_Failure_InvalidCipherText() throws Exception {
        String expectedMessage = "DataProcessingException : Error occurred while decrypting the password.";

        when(dateTimeUtil.getCurrentTimestampInGMT()).thenReturn(Timestamp.valueOf("2021-12-02 02:02:02"));
        when(notificationsDao.getSMTPDetails(2)).thenReturn(smtpDetailsBean);
        when(masterDataDao.getViewTypesFromMstTypeAndSubTypeName(Constants.SMTP_PROTOCOLS, mockUtilityBean.getPojoObject().getSecurity())).thenReturn(securityType);
        InvalidCipherTextException invalidCipherTextException = new InvalidCipherTextException();
        when(aecsBouncyCastleUtil.decrypt(mockUtilityBean.getPojoObject().getPassword())).thenThrow(invalidCipherTextException);

        DataProcessingException requestException = assertThrows(DataProcessingException.class, () ->
                putEmailConfigurationsBL.process(mockUtilityBean));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void process_Failure_ImproperEncryption() throws Exception {
        String expectedMessage = "DataProcessingException : Password is not encrypted properly";

        when(dateTimeUtil.getCurrentTimestampInGMT()).thenReturn(Timestamp.valueOf("2021-12-02 02:02:02"));
        when(notificationsDao.getSMTPDetails(2)).thenReturn(smtpDetailsBean);
        when(masterDataDao.getViewTypesFromMstTypeAndSubTypeName(Constants.SMTP_PROTOCOLS, mockUtilityBean.getPojoObject().getSecurity())).thenReturn(securityType);
        when(aecsBouncyCastleUtil.decrypt(mockUtilityBean.getPojoObject().getPassword())).thenReturn("");

        DataProcessingException requestException = assertThrows(DataProcessingException.class, () ->
                putEmailConfigurationsBL.process(mockUtilityBean));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }
}
