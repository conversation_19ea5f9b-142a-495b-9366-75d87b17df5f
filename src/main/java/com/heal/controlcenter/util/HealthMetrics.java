package com.heal.controlcenter.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class HealthMetrics {

    public void updateHealApiServiceErrors() {
        log.info("Updating Heal API Service Errors");
    }

    public void updateUnauthorizedRequests() {
        log.info("Updating Unauthorized Requests");
    }

    public void updateResponseTime(String apiName, long time) {
        log.info("Updating response time for API: {} with time: {}ms", apiName, time);
    }
}