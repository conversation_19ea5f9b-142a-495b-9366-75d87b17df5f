package com.heal.controlcenter.businesslogic;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.dao.mysql.TagsDao;
import com.heal.controlcenter.dao.mysql.UserDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.Account;
import com.heal.controlcenter.pojo.Tags;
import com.heal.controlcenter.pojo.ThresholdSeverity;
import com.heal.controlcenter.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Business logic class for handling account retrieval operations.
 * Implements validation, authorization, and data transformation logic.
 */
@Slf4j
@Component
public class GetAccountsBL implements BusinessLogic<Account, UtilityBean<AccessDetailsBean>, List<Account>> {

    @Autowired
    TagsDao tagsDao;
    @Autowired
    UserDao userDao;
    @Autowired
    AccountsDao accountDao;
    @Autowired
    ObjectMapper objectMapper;
    @Autowired
    KeyCloakAuthService keyCloakAuthService;
    @Autowired
    ClientValidationUtils clientValidationUtils;
    @Autowired
    ServerValidationUtils serverValidationUtils;

    /**
     * Performs client-side validation for the Get Accounts request.
     *
     * <p>This method validates the presence and correctness of the authorization key.
     * It then builds request parameters needed for downstream processing.
     *
     * @param requestBody The {@link Account} object, not used directly for validation in this case.
     * @param requestParams Varargs containing the authorization token as the first parameter.
     * @return A {@link UtilityBean} containing request parameters (like the auth key) for further processing.
     * @throws ClientException If the authorization key is missing or invalid.
     */
    @Override
    public UtilityBean<Account> clientValidation(Account requestBody, String... requestParams) throws ClientException {
        String authKey = requestParams[0];
        clientValidationUtils.authKeyValidation(authKey);

        HashMap<String, String> requestParamsMap = CommonUtils.buildRequestParams(authKey, null);

        return UtilityBean.<Account>builder()
                .requestParams(requestParamsMap)
                .build();
    }

    /**
     * Performs server-side validation by extracting and validating user access context.
     *
     * <p>This method:
     * <ul>
     *     <li>Validates the authorization key and extracts the user identifier.</li>
     *     <li>Fetches user access details from the database using the identifier.</li>
     *     <li>Converts the JSON access details into an {@link AccessDetailsBean} object.</li>
     * </ul>
     *
     * @param utilityBean The {@link UtilityBean} generated during client validation, containing request parameters.
     * @return A {@link UtilityBean} enriched with the user's access context and user ID metadata.
     * @throws ServerException If the user ID is invalid, or if the access details cannot be fetched or parsed.
     */
    @Override
    public UtilityBean<AccessDetailsBean> serverValidation(UtilityBean<Account> utilityBean) throws ServerException {
        String authKey = utilityBean.getRequestParams().get(Constants.AUTH_KEY);
        String userId = serverValidationUtils.authKeyValidation(authKey);

        UserAccessBean accessDetails = userDao.fetchUserAccessDetailsUsingIdentifier(userId);

        AccessDetailsBean accessDetailsBean;
        try {
            accessDetailsBean = objectMapper.readValue(accessDetails.getAccessDetails(), AccessDetailsBean.class);
        } catch (Exception e) {
            log.error("Error occurred while mapping userAccessDetails Json to java object of AccessDetailsBean class");
            throw new ServerException(UIMessages.USER_ACCESS_DETAILS_NOT_FOUND);
        }

        if (accessDetailsBean == null) {
            log.error(UIMessages.INVALID_USER_ACCESS_DETAILS);
            throw new ServerException(UIMessages.INVALID_USER_ACCESS_DETAILS);
        }

        return UtilityBean.<AccessDetailsBean>builder()
                .pojoObject(accessDetailsBean)
                .metadata(Map.of(Constants.USER_ID_KEY, userId))
                .build();
    }

    /**
     * Retrieves and builds a list of accessible accounts for the authenticated user.
     *
     * <p>This method performs the following operations:
     * <ul>
     *     <li>Fetches all accounts from the database.</li>
     *     <li>Filters accounts based on access scope (wildcard or restricted).</li>
     *     <li>Fetches account-specific configuration details (timezone, anomaly settings, tags).</li>
     *     <li>Constructs and enriches {@link Account} objects with all relevant details.</li>
     * </ul>
     *
     * <p>Accounts without timezone information are excluded from the result set.
     *
     * @param utilityBean The {@link UtilityBean} containing access details and user ID metadata.
     * @return A list of {@link Account} objects accessible to the user.
     * @throws DataProcessingException If any data retrieval or transformation step fails.
     */
    @Override
    public List<Account> process(UtilityBean<AccessDetailsBean> utilityBean) throws DataProcessingException {
        AccessDetailsBean bean = utilityBean.getPojoObject();
        String userId = (String) utilityBean.getMetadata().get(Constants.USER_ID_KEY);
        try {
            List<AccountBean> accessibleAccounts = accountDao.getAccounts();

            if (Objects.nonNull(accessibleAccounts) && !bean.getAccounts().contains("*")) {
                accessibleAccounts = accessibleAccounts.parallelStream()
                        .filter(acc -> bean.getAccounts().contains(acc.getIdentifier()))
                        .toList();
            }

            List<Account> accountsList = new ArrayList<>();

            for (AccountBean accBean : accessibleAccounts) {
                TimezoneBean timezoneBean;
                try {
                    timezoneBean = accountDao.getAccountTimezoneDetails(accBean.getId());
                } catch (ControlCenterException e) {
                    throw new DataProcessingException(e.getMessage());
                }
                if (timezoneBean == null) {
                    continue;
                }

                AccountAnomalyConfigurationBean config;
                try {
                    config = accountDao.getAccountAnomalyConfiguration(accBean.getId());
                } catch (ControlCenterException e) {
                    throw new DataProcessingException(e.getMessage());
                }

                List<Tags> tagsList = tagsDao.getTagsByObjectId(accBean.getId(), "account")
                        .stream()
                        .map(tag -> new Tags(tag.getType(), tag.getKey()))
                        .collect(Collectors.toList());

                ThresholdSeverity thresholdSeverity = new ThresholdSeverity(
                        config != null && config.isLowEnable(),
                        config != null && config.isMediumEnable(),
                        config != null && config.isHighEnable()
                );

                Account account = Account.builder()
                        .accountId(accBean.getId())
                        .accountName(accBean.getName())
                        .identifier(accBean.getIdentifier())
                        .privateKey(accBean.getPrivateKey())
                        .publicKey(accBean.getPublicKey())
                        .updatedTime(accBean.getUpdatedTime() != null
                                ? Timestamp.valueOf(accBean.getUpdatedTime()).getTime()
                                : null)
                        .timezoneMilli(timezoneBean.getOffset())
                        .timeZoneString(timezoneBean.getTimeZoneId())
                        .status(accBean.getStatus())
                        .dateFormat("YYYY-MM-DD")
                        .timeFormat("HH:mm")
                        .tags(tagsList)
                        .closingWindow(config != null ? config.getClosingWindow() : 0)
                        .maxDataBreaks(config != null ? config.getMaxDataBreaks() : 0)
                        .thresholdSeverity(thresholdSeverity)
                        .build();

                account.getUserNameFromIdentifier(accBean.getLastModifiedBy(), keyCloakAuthService);
                accountsList.add(account);
            }

            return accountsList;
        } catch (ControlCenterException e) {
            log.error("Error in fetching accounts list for user: {}", userId);
            throw new DataProcessingException("Error in fetching accounts details");
        }
    }
}
