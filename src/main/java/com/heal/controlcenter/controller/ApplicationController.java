package com.heal.controlcenter.controller;

import com.heal.configuration.pojos.Account;
import com.heal.controlcenter.aop.AopCustomAnnotation;
import com.heal.controlcenter.beans.ApplicationBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.AddApplicationsBL;
import com.heal.controlcenter.businesslogic.GetApplicationsBL;
import com.heal.controlcenter.businesslogic.PutApplicationsBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.*;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.JsonFileParser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * REST Controller for retrieving applications associated with an account.
 * This controller provides endpoints for fetching applications with optional cluster data and search filters.
 */
@Slf4j
@RestController
public class ApplicationController {
    @Autowired
    GetApplicationsBL getApplicationsBL;
    @Autowired
    JsonFileParser headersParser;
    @Autowired
    AddApplicationsBL addApplicationsBL;
    @Autowired
    PutApplicationsBL putApplicationsBL;

    @Operation(
            summary = "Retrieves applications for the specified account.",
            description = "Returns a paginated list of applications linked to the given account identifier. " +
                    "Optionally filters results using a search term and includes cluster data if requested.",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Applications fetched successfully.",
                            content = @Content(schema = @Schema(implementation = ApplicationPojo.class))
                    ),
                    @ApiResponse(
                            responseCode = "400",
                            description = "Client-side error due to invalid input parameters or token.",
                            content = @Content(schema = @Schema(implementation = ResponsePojo.class))
                    ),
                    @ApiResponse(
                            responseCode = "401",
                            description = "Unauthorized - Invalid or missing authentication token.",
                            content = @Content(schema = @Schema(implementation = ResponsePojo.class))
                    ),
                    @ApiResponse(
                            responseCode = "500",
                            description = "Server-side error while processing application retrieval.",
                            content = @Content(schema = @Schema(implementation = ResponsePojo.class))
                    )
            }
    )
    /**
     * Retrieves paginated list of applications for the specified account.
     * <p>
     * This endpoint performs the following operations:
     * 1. Validates client input (authorization token, account identifier)
     * 2. Performs server-side validation of the account
     * 3. Retrieves applications with optional search filtering and cluster data inclusion
     * 4. Returns paginated results with standard response headers
     *
     * @param authorization         Bearer token for authentication (required)
     * @param accountIdentifier     Unique identifier of the account (required)
     * @param clusterDataRequired   Flag to include cluster data (default: true)
     * @param searchTerm            Optional term to filter applications by name/identifier
     * @param pageable             Pagination and sorting parameters
     * @return ResponseEntity containing paginated ApplicationPojo results
     * @throws ClientException         When invalid client input is detected
     * @throws ServerException        When server-side validation fails
     * @throws DataProcessingException When application processing fails
     * @throws ControlCenterException For unexpected system errors
     */
    @AopCustomAnnotation
    @GetMapping(value = "/accounts/{identifier}/applications")
    public ResponseEntity<ResponsePojo<Page<ApplicationPojo>>> getApplications(
            @Parameter(
                    description = "Account identifier to fetch associated applications",
                    required = true
            )
            @PathVariable(value = "identifier") String accountIdentifier,
            @Parameter(
                    description = "Boolean flag indicating whether cluster data is required",
                    required = false,
                    example = "true"
            )
            @RequestParam(value = "clusterDataRequired", required = false, defaultValue = "true") String clusterDataRequired,
            @Parameter(
                    description = "Search term to filter applications by name or identifier",
                    required = false,
                    example = "InventoryApp"
            )
            @RequestParam(value = "searchTerm", required = false, defaultValue = "") String searchTerm,
            @Parameter(
                    hidden = true
            )
            Pageable pageable, BasicUserDetails userDetails)
            throws ClientException, DataProcessingException, ServerException {

        log.info("Request received to fetch applications for account [{}] with parameters - clusterDataRequired: {}, searchTerm: {}, pageable: {}",
                accountIdentifier, clusterDataRequired, searchTerm, pageable);
        log.debug("Performing client validation for account [{}]", accountIdentifier);
        UtilityBean<String> applicationBean = getApplicationsBL.clientValidation(null, accountIdentifier, clusterDataRequired, searchTerm);
        applicationBean.getMetadata().put(Constants.USER_ID_KEY, userDetails.getUserIdentifier());
        applicationBean.setPageable(pageable);

        UtilityBean<Account> account = getApplicationsBL.serverValidation(applicationBean);
        Page<ApplicationPojo> listOfApplications = getApplicationsBL.process(account);

        ResponsePojo<Page<ApplicationPojo>> responsePojo =
                new ResponsePojo<>("Applications fetched successfully", listOfApplications, HttpStatus.OK);

        log.info("Successfully fetched {} applications for account [{}]",
                listOfApplications.getNumberOfElements(), accountIdentifier);

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
    }

    @Operation(
            summary = "Adds a new application for the specified account.",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Application added successfully.",
                            content = @Content(schema = @Schema(implementation = Application.class))
                    ),
                    @ApiResponse(
                            responseCode = "500",
                            description = "Exception encountered while adding application.",
                            content = @Content(schema = @Schema(implementation = ResponsePojo.class))
                    ),
                    @ApiResponse(
                            responseCode = "400",
                            description = "Error occurred while adding application.",
                            content = @Content(schema = @Schema(implementation = ResponsePojo.class))
                    ),
                    @ApiResponse(
                            responseCode = "401",
                            description = "Unauthorized - Invalid or missing authentication token.",
                            content = @Content(schema = @Schema(implementation = ResponsePojo.class))
                    )
            }
    )
    /**
     * Adds new applications for the specified account.
     * <p>
     * This endpoint performs the following operations:
     * 1. Validates the input application list and account identifier (client-side)
     * 2. Performs server-side validation for account, user, and application uniqueness
     * 3. Processes and persists the applications, including tags, anomaly configs, notification preferences, and percentiles
     * 4. Returns a list of created application IDs
     *
     * @param accountIdentifier Unique identifier of the account (from path variable)
     * @param body              List of Application objects to be added (from request body)
     * @param userDetails       Authenticated user details (injected)
     * @return ResponseEntity containing a list of created application IDs
     * @throws ClientException         If client-side validation fails
     * @throws ServerException         If server-side validation fails
     * @throws DataProcessingException If application processing or persistence fails
     */
    @AopCustomAnnotation
    @PostMapping(value = "/accounts/{identifier}/applications")
    public ResponseEntity<ResponsePojo<List<IdPojo>>> addApplications(
            @Parameter(
                    name = "identifier",
                    description = "Unique identifier of the account",
                    required = true,
                    example = "account-001"
            )
            @PathVariable(value = "identifier") String accountIdentifier,
            @Parameter(
                    description = "Application details to add (list)",
                    required = true
            )
            @RequestBody List<Application> body, BasicUserDetails userDetails)
            throws ClientException, ServerException, DataProcessingException {

        log.info("Request received to add {} applications for account [{}] by user [{}]", body != null ? body.size() : 0, accountIdentifier, userDetails.getUserIdentifier());
        UtilityBean<List<Application>> applicationBean = addApplicationsBL.clientValidation(body, accountIdentifier);
        applicationBean.getMetadata().put(Constants.USER_ID_KEY, userDetails.getUserIdentifier());

        UtilityBean<List<ApplicationBean>> bean = addApplicationsBL.serverValidation(applicationBean);
        List<IdPojo> idPojoList = addApplicationsBL.process(bean);

        log.info("Successfully added {} applications for account [{}]", idPojoList.size(), accountIdentifier);
        ResponsePojo<List<IdPojo>> responsePojo = new ResponsePojo<>("Applications added successfully", idPojoList, HttpStatus.OK);

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
    }

    @Operation(
            summary = "Updates an existing application",
            description = "Updates allowed fields (name, environment, timezone) for an existing application. Identifier and linkedEnvironment cannot be updated.",
            security = @SecurityRequirement(name = "bearerAuth"),
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Application details for update",
                    required = true,
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = Application.class)
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Application updated successfully",
                            content = @Content(mediaType = "application/json",
                                    schema = @Schema(implementation = ResponsePojo.class))),
                    @ApiResponse(responseCode = "400", description = "Invalid request payload or data.",
                            content = @Content(schema = @Schema(implementation = ResponsePojo.class))),
                    @ApiResponse(responseCode = "401", description = "Unauthorized - Missing or invalid authentication token.",
                            content = @Content(schema = @Schema(implementation = ResponsePojo.class))),
                    @ApiResponse(responseCode = "500", description = "Internal server error during application update.",
                            content = @Content(schema = @Schema(implementation = ResponsePojo.class)))
            }
    )
    @AopCustomAnnotation
    @PutMapping(value = "/accounts/{identifier}/applications")
    public ResponseEntity<ResponsePojo<List<IdPojo>>> updateApplication(
            @PathVariable(value = "identifier") String accountIdentifier,
            @RequestBody List<Application> body, BasicUserDetails userDetails) throws ClientException, ServerException, DataProcessingException {
        log.trace("Method Invoked: updateApplication");

        UtilityBean<List<Application>> utilityBean = putApplicationsBL.clientValidation(body,accountIdentifier);
        utilityBean.getMetadata().put(Constants.USER_ID_KEY, userDetails.getUserIdentifier());
        UtilityBean<List<ApplicationBean>> validatedBean = putApplicationsBL.serverValidation(utilityBean);
        putApplicationsBL.process(validatedBean);
        ResponsePojo<List<IdPojo>> responseBean = new ResponsePojo<>("Application updated successfully.", null, HttpStatus.OK);
        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responseBean);
    }
}