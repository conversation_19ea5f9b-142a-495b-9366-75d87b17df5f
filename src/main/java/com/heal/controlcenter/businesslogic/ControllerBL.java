package com.heal.controlcenter.businesslogic;

import com.heal.controlcenter.beans.ControllerBean;
import com.heal.controlcenter.beans.ServiceBean;
import com.heal.controlcenter.beans.TagDetailsBean;
import com.heal.controlcenter.dao.mysql.AccountServiceDao;
import com.heal.controlcenter.pojo.Tags;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.DateTimeUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

@Service
public class ControllerBL {

    private static final Logger logger = LoggerFactory.getLogger(ControllerBL.class);

    @Autowired
    private TagMappingBL tagMappingBL;

    @Autowired
    private AccountServiceDao accountServiceDao;


    public ControllerBean addOrUpdateController(ServiceBean bean, int controllerTypeId, int isUpdate) {
        String name = bean.getName();
        String identifier = bean.getIdentifier();
        int accountId = bean.getAccountId();
        String userId = bean.getUserId();
        String layer = bean.getLayer();
        List<Integer> appIds = bean.getAppIds();
        List<Tags> tags = new ArrayList<>();

        ControllerBean controllerBean = new ControllerBean();
        controllerBean.setName(name);
        controllerBean.setIdentifier(identifier);
        controllerBean.setAccountId(accountId);
        controllerBean.setId(Integer.parseInt(userId));
        controllerBean.setCreatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
        controllerBean.setUpdatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
        controllerBean.setControllerTypeId(controllerTypeId);
        controllerBean.setStatus(bean.getStatus());


        try {
            int id;
            if (isUpdate == 0) {
                id = accountServiceDao.addController(controllerBean); // ← direct call to JdbcTemplate method
            } else {
                id = accountServiceDao.editControllerName(controllerBean.getName(), controllerBean.getUpdatedTime(), controllerBean.getId()); // ← direct call
            }


            controllerBean.setId(id);
            if (id < 0) {
                logger.error("Failed to {} controller: {} for account {}", (isUpdate == 1 ? "update" : "create"), identifier, accountId);                throw new RuntimeException("Controller creation/update failed");
            }

            logger.info("Controller {} with ID {} {} successfully.", identifier, id, (isUpdate == 1 ? "update" : "created"));
            // Layer Mapping
            if (StringUtils.hasText(layer)) {
                TagDetailsBean tagDetailsBean = accountServiceDao.getTagDetails(Constants.LAYER_TAG, accountId);                if (tagDetailsBean != null) {
                    int tagId = tagMappingBL.addTagMapping(tagDetailsBean.getId(), id, Constants.CONTROLLER,
                            Constants.LAYER_DEFAULT, layer, userId, accountId);
                    if (tagId == -1) throw new RuntimeException("Failed to map DB Layer tag");
                    tags.add(Tags.builder()
                            .name(Constants.LAYER_TAG)
                            .identifier(layer)
                            .build());
                }
            }

            // AppIds Mapping
            if (appIds != null && !appIds.isEmpty()) {
                TagDetailsBean tagDetailsBean = accountServiceDao.getTagDetails(Constants.CONTROLLER_TAG, accountId);                if (tagDetailsBean != null) {
                    for (int appId : appIds) {
                        int tagId = tagMappingBL.addTagMapping(tagDetailsBean.getId(), appId, Constants.CONTROLLER,
                                String.valueOf(id), identifier, userId, accountId);
                        if (tagId == -1) throw new RuntimeException("Failed to map AppId " + appId);
                        tags.add(Tags.builder()
                                .name(Constants.CONTROLLER_TAG)
                                .identifier(String.valueOf(id))
                                .build());
                    }
                }
            }

            // Service Type Mapping
            if (StringUtils.hasText(bean.getType())) {
                TagDetailsBean tagDetailsBean = accountServiceDao.getTagDetails(Constants.SERVICE_TYPE_TAG, accountId);                if (tagDetailsBean != null) {
                    int tagId = tagMappingBL.addTagMapping(tagDetailsBean.getId(), id, Constants.CONTROLLER,
                            Constants.SERVICE_TYPE_DEFAULT, bean.getType(), userId, accountId);
                    if (tagId == -1) throw new RuntimeException("Failed to map Service Type tag");
                    tags.add(Tags.builder()
                            .name(Constants.SERVICE_TYPE_TAG)
                            .identifier(bean.getType())
                            .build());
                }
            }

        } catch (Exception ex) {
            logger.error("Exception while processing controller {} for account {}", identifier, accountId, ex);
            throw new RuntimeException("Controller processing failed", ex);
        }

        return controllerBean;
    }
}
