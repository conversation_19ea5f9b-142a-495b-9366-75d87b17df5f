package com.heal.controlcenter.businesslogic;

import com.heal.configuration.pojos.AnomalyConfiguration;
import com.heal.configuration.pojos.EscalationSettings;
import com.heal.controlcenter.beans.AccountAnomalyConfigurationBean;
import com.heal.controlcenter.beans.AccountBean;
import com.heal.controlcenter.beans.TimezoneBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.dao.mysql.MasterDataDao;
import com.heal.controlcenter.dao.mysql.NotificationsDao;
import com.heal.controlcenter.dao.mysql.TagsDao;
import com.heal.controlcenter.dao.redis.AccountRepo;
import com.heal.controlcenter.dao.redis.NotificationRepo;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.Account;
import com.heal.controlcenter.pojo.TagMappingDetails;
import com.heal.controlcenter.pojo.Tags;
import com.heal.controlcenter.pojo.ThresholdSeverity;
import com.heal.controlcenter.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.security.KeyPair;
import java.util.*;

@Slf4j
@Service
public class AddAccountBL implements BusinessLogic<Account, UtilityBean<Account>, Account> {

    @Autowired
    AccountsDao accountsDao;
    @Autowired
    MasterDataDao masterDataDao;
    @Autowired
    DateTimeUtil dateTimeUtil;
    @Autowired
    TagsDao tagsDao;
    @Autowired
    AccountRepo accountRepo;
    @Autowired
    NotificationRepo notificationRepo;
    @Autowired
    GetNotificationSettingsBL getNotificationSettingsBL;
    @Autowired
    ClientValidationUtils clientValidationUtils;
    @Autowired
    ServerValidationUtils serverValidationUtils;
    @Autowired
    NotificationsDao notificationsDao;

    /**
     * Validates the client-side input for account creation.
     * Checks account identifier format and required fields in the request body.
     *
     * @param requestBody   Account object from the request payload.
     * @param requestParams Authorization token and account identifier.
     * @return UtilityBean containing request data and metadata.
     * @throws ClientException if validation fails.
     */
    @Override
    public UtilityBean<Account> clientValidation(Account requestBody, String... requestParams) throws ClientException {

        String accountIdentifier = requestParams[1];
        clientValidationUtils.accountIdentifierValidation(accountIdentifier);

        Map<String, String> error = requestBody.validate();
        if (!error.isEmpty()) {
            String err = error.toString();
            log.error(err);
            throw new ClientException(err);
        }

        HashMap<String, String> requestParamsMap = CommonUtils.buildRequestParams(null, accountIdentifier);

        return UtilityBean.<Account>builder()
                .requestParams(requestParamsMap)
                .pojoObject(requestBody)
                .metadata(new HashMap<>())
                .build();
    }

    /**
     * Validates server-side constraints for account creation.
     * Ensures the account identifier is unique and not already in use.
     *
     * @param utilityBean UtilityBean containing request parameters and account object.
     * @return UtilityBean enriched with metadata.
     * @throws ServerException if the account identifier already exists.
     */
    @Override
    public UtilityBean<Account> serverValidation(UtilityBean<Account> utilityBean) throws ServerException {

        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (accountsDao.existsByIdentifier(accountIdentifier.trim())) {
            String msg = "Account identifier already in use, kindly use another identifier.";
            log.error("{} -> {}", msg, accountIdentifier);
            throw new ServerException(msg);
        }

        return UtilityBean.<Account>builder()
                .pojoObject(utilityBean.getPojoObject())
                .requestParams(utilityBean.getRequestParams())
                .metadata(utilityBean.getMetadata())
                .build();
    }

    /**
     * Processes the creation of a new account.
     * Handles DB persistence, key generation, tag mapping, and Redis updates.
     *
     * @param utilityBean Validated UtilityBean with account data and metadata.
     * @return An empty Account object (actual account is persisted).
     * @throws DataProcessingException if any error occurs during processing.
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public Account process(UtilityBean<Account> utilityBean) throws DataProcessingException {
        String userId = (String) utilityBean.getMetadata().get(Constants.USER_ID_KEY);
        Account accountDetails = utilityBean.getPojoObject();
        try {
            int id = accountsDao.getAccountByName(accountDetails.getAccountName());
            if (id > 0) {
                String msg = String.format("Account name [%s] already exists.", utilityBean.getPojoObject().getAccountName());
                log.error(msg);
                throw new ControlCenterException(msg);
            }

            KeyPair keyPair = KeyGenerator.generateKeys();
            String privateKey = KeyGenerator.getPrivateKey(null, keyPair);
            String publicKey = KeyGenerator.getPublicKey(null, keyPair);

            String createdTime = dateTimeUtil.getCurrentTimestampInGMT().toString();
            if (createdTime == null) {
                log.error("Error while fetching currentTimestamp in GMT");
                throw new ControlCenterException("Error while fetching currentTime in GMT");
            }

            //Insert account
            AccountBean accountBean = new AccountBean();
            accountBean.setName(accountDetails.getAccountName());
            accountBean.setLastModifiedBy(userId);
            accountBean.setIdentifier(accountDetails.getIdentifier());
            accountBean.setPublicKey(publicKey);
            accountBean.setPrivateKey(privateKey);
            accountBean.setCreatedTime(createdTime);
            accountBean.setUpdatedTime(createdTime);
            accountBean.setStatus(utilityBean.getPojoObject().getStatus());

            id = accountsDao.addAccount(accountBean);

            AccountAnomalyConfigurationBean accountAnomalyConfigurationBean = new AccountAnomalyConfigurationBean();
            accountAnomalyConfigurationBean.setAccountId(id);
            accountAnomalyConfigurationBean.setUserDetailsId(userId);
            accountAnomalyConfigurationBean.setCreatedTime(createdTime);
            accountAnomalyConfigurationBean.setUpdatedTime(createdTime);
            accountAnomalyConfigurationBean.setLowEnable(utilityBean.getPojoObject().getThresholdSeverity().isLow());
            accountAnomalyConfigurationBean.setMediumEnable(utilityBean.getPojoObject().getThresholdSeverity().isWarning());
            accountAnomalyConfigurationBean.setHighEnable(utilityBean.getPojoObject().getThresholdSeverity().isCritical());
            accountAnomalyConfigurationBean.setClosingWindow(utilityBean.getPojoObject().getClosingWindow());
            accountAnomalyConfigurationBean.setMaxDataBreaks(utilityBean.getPojoObject().getMaxDataBreaks());

            accountsDao.insertAccountAnomalyConfigurations(id, accountAnomalyConfigurationBean, userId);

            TimezoneBean timezone;
            List<Tags> tagList = accountDetails.getTags();

            String timeZoneId = tagList.get(0).getIdentifier();
            try {
                timezone = masterDataDao.getTimeZoneWithId(timeZoneId);
            } catch (ControlCenterException e) {
                log.error("Invalid timezone [{}]", timeZoneId);
                throw new ControlCenterException(e.getMessage());
            }

            //Insert tag_mapping
            try {
                TagMappingDetails timeZoneTag = TagMappingDetails.builder()
                        .tagId(tagsDao.getTagDetailsByName(Constants.TIME_ZONE_TAG).getId())
                        .tagKey(String.valueOf(timezone.getId()))
                        .tagValue(String.valueOf(timezone.getOffset()))
                        .objectId(id)
                        .objectRefTable(Constants.ACCOUNT)
                        .accountId(id)
                        .userDetailsId(userId)
                        .createdTime(createdTime)
                        .updatedTime(createdTime)
                        .build();
                int mappingId = tagsDao.addTagMappingDetails(timeZoneTag);
                if (mappingId <= 0) {
                    log.error("Timezone mapping to application [{}] failed", id);
                    throw new DataProcessingException("Timezone mapping to application failed");
                }
            } catch (ControlCenterException e) {
                throw new DataProcessingException(e.getMessage());
            }

            Optional<Tags> timeZoneTag = utilityBean.getPojoObject().getTags().stream().filter(tag -> tag.getName().equalsIgnoreCase(Constants.TIME_ZONE_TAG))
                    .findAny();

            com.heal.configuration.pojos.Tags tags = new com.heal.configuration.pojos.Tags();
            String tagName = tagList.get(0).getName();//Ex:Timezone
            tags.setKey(timezone.getTimeZoneId());
            tags.setValue(String.valueOf(timezone.getOffset()));
            tags.setType(tagName);

            //Account Repo saving to Redis
            ThresholdSeverity sourceSeverity = utilityBean.getPojoObject().getThresholdSeverity();
            AnomalyConfiguration redisSeverity = new AnomalyConfiguration();
            redisSeverity.setLowEnable(sourceSeverity.isLow());
            redisSeverity.setMediumEnable(sourceSeverity.isWarning());
            redisSeverity.setHighEnable(sourceSeverity.isCritical());
            redisSeverity.setClosingWindow(utilityBean.getPojoObject().getClosingWindow());
            redisSeverity.setMaxDataBreaks(utilityBean.getPojoObject().getMaxDataBreaks());
            redisSeverity.setLastModifiedBy(userId);
            redisSeverity.setCreatedTime(createdTime);
            redisSeverity.setUpdatedTime(createdTime);
            redisSeverity.setAccountId(id);

            com.heal.configuration.pojos.Account newAccount = com.heal.configuration.pojos.Account.builder()
                    .accountId(id)
                    .id(id)
                    .name(accountBean.getName())
                    .createdTime(String.valueOf(accountBean.getCreatedTime()))
                    .updatedTime(String.valueOf(accountBean.getUpdatedTime()))
                    .status(accountBean.getStatus())
                    .privateKey(accountBean.getPrivateKey())
                    .publicKey(accountBean.getPublicKey())
                    .identifier(accountBean.getIdentifier())
                    .tags(List.of(tags))
                    .timezone(timeZoneTag.map(Tags::getIdentifier).orElse(null))
                    .lastModifiedBy(userId)
                    .anomalyConfiguration(redisSeverity)
                    .build();

            accountRepo.updateAccount(utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER), newAccount);

            List<com.heal.configuration.pojos.Account> existingAccountList = accountRepo.getAccounts();
            if (existingAccountList.isEmpty()) {
                existingAccountList = new ArrayList<>();
            }
            existingAccountList.add(newAccount);
            accountRepo.updateAccounts(existingAccountList);

            //Insert notification_settings
            getNotificationSettingsBL.addDefaultNotificationSettings(id, userId);

            //Notification Repo saving to Redis
            List<EscalationSettings> escalationSetting = notificationsDao.getEscalationSettingsForAccount(id);
            notificationRepo.updateNotificationSettingsInRedis(utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER), escalationSetting);

            return new Account();

        } catch (Exception e) {
            log.error("Error while creating account: {}", accountDetails.getAccountName(), e);
            throw new DataProcessingException(e.getMessage());
        }
    }
}
