package com.heal.controlcenter.util;

import com.appnomic.appsone.common.util.Commons;
import com.appnomic.appsone.model.JWTData;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.heal.configuration.entities.UserAccessDetails;
import com.heal.configuration.pojos.ViewTypes;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.ServerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringEscapeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.security.Security;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Slf4j
public class CommonUtils {

    @Autowired
    KeyCloakAuthService keyCloakAuthService;

    public boolean isEmpty(String s) {
        return (s == null || s.trim().length() == 0);
    }

    public static ObjectMapper getObjectMapperWithHtmlEncoder() {
        ObjectMapper objectMapper = new ObjectMapper();

        SimpleModule simpleModule = new SimpleModule("HTML-Encoder", objectMapper.version()).addDeserializer(String.class, new EscapeHTML());

        objectMapper.registerModule(simpleModule);

        return objectMapper;
    }

    public String getUserId(String authKey) throws ControlCenterException {
        JWTData jwtData = keyCloakAuthService.extractUserDetails(authKey);
        return jwtData.getSub();
    }

    public static String getDecryptedData(String encryptedData) {
        //TODO BouncyCastle encryption algorithm has to be used in further releases.
        return new String(Base64.getDecoder().decode(encryptedData));
    }

    public String decryptInBCEC(String input) throws ServerException {
        if (input != null && !input.isEmpty()) {
            try {
                Security.removeProvider(Constants.BC_PROVIDER_NAME);
                return Commons.decrypt(input);
            } catch (Exception e) {
                log.error("Exception encountered while decrypting the password. Details: {}", e.getMessage());
                throw new ServerException("Error occurred while decrypting the password.");
            }
        }
        return "";
    }

    public String encryptInBCEC(String input) throws ServerException {
        if (input != null && !input.isEmpty()) {
            try {
                Security.removeProvider(Constants.BC_PROVIDER_NAME);
                return Commons.encrypt(input);
            } catch (Exception e) {
                log.error("Exception encountered while decrypting the password. Details: ", e);
                throw new ServerException("Error occurred while decrypting the password.");
            }
        }
        return "";
    }

    public Map<String, UserAccessDetails> getUserAccessDetailsByAccount(List<UserAccessDetails> userAccessDetails, String accountIdentifier) {
        Map<String, UserAccessDetails> map = new HashMap<>();

        //SuperAdmin and UserManager will have accountWiseAccessible set to true
        if (userAccessDetails.get(0).isAccountWiseAccessible()) {
            map.put(accountIdentifier, userAccessDetails.get(0));
        } else {
            map = userAccessDetails.stream().collect(Collectors.toMap(u -> u.getAccount().getIdentifier(), Function.identity(), (existing, replacement) -> {
                log.warn("Duplicate tags of accountIdentifier found. Choosing the second entry [{}] available instead of [{}].", replacement, existing);
                return replacement;
            }));
        }
        return map;
    }

    public static HashMap<String, String> buildRequestParams(String authKey, String accountIdentifier) {
        HashMap<String, String> map = new HashMap<>();
        map.put(Constants.AUTH_KEY, authKey);
        map.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier);
        return map;
    }

    public ViewTypes getViewTypeByNameAndSubType(Map<String, List<ViewTypes>> viewTypes, String typeName, String subType) {
        if (viewTypes == null || viewTypes.isEmpty()) {
            return null;
        }
        return viewTypes.getOrDefault(typeName, new ArrayList<>())
                .stream().filter(v -> v.getSubTypeName().equalsIgnoreCase(subType))
                .findFirst().orElse(null);
    }
}

class EscapeHTML extends JsonDeserializer<String> {

    @Override
    public String deserialize(JsonParser jp, DeserializationContext ctxt)
            throws IOException {
        String s = jp.getValueAsString();
        return StringEscapeUtils.escapeHtml4(s);
    }

}

