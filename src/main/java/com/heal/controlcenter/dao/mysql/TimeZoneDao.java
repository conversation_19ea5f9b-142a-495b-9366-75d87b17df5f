package com.heal.controlcenter.dao.mysql;

import com.heal.controlcenter.beans.TimezoneBean;
import com.heal.controlcenter.exception.HealControlCenterException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;

@Slf4j
@Repository
public class TimeZoneDao {
    @Autowired
    private JdbcTemplate jdbcTemplate;

    public List<TimezoneBean> getTimeZones() throws HealControlCenterException {
        try {
            String ALL_TIME_ZONES_QUERY = "select id, time_zone_id timeZoneId, timeoffset offset, user_details_id userDetailsId, " +
                    "account_id accountId, status status from mst_timezone where status=1";
            return jdbcTemplate.query(ALL_TIME_ZONES_QUERY, new BeanPropertyRowMapper<>(TimezoneBean.class));
        } catch (Exception e) {
            log.error("Error while getting time zone. Details: ", e);
            throw new HealControlCenterException("Error in fetching timezones");
        }
    }

    public TimezoneBean getApplicationTimezoneDetails(int applicationId) throws HealControlCenterException {
        try {
            String query = "SELECT mt.id, mt.timeoffset AS offset, mt.time_zone_id AS timeZoneId, " +
                    "mt.user_details_id AS userDetailsId, mt.account_id AS accountId, mt.status " +
                    "FROM tag_mapping tm " +
                    "JOIN tag_details td ON tm.tag_id = td.id " +
                    "JOIN mst_timezone mt ON tm.tag_key = mt.id " +
                    "WHERE tm.object_id = ? AND tm.object_ref_table = 'controller' AND td.name = 'Timezone'";

            return jdbcTemplate.queryForObject(query, new BeanPropertyRowMapper<>(TimezoneBean.class), applicationId);
        } catch (Exception e) {
            log.error("Error fetching timezone for applicationId {}: {}", applicationId, e.getMessage());
            throw new HealControlCenterException("Error in fetching timezone for application ID: " + applicationId);
        }
    }

}
