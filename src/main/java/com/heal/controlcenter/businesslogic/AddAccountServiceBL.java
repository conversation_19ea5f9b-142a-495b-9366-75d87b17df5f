package com.heal.controlcenter.businesslogic;


import com.appnomic.appsone.common.util.ConfProperties;
import com.appnomic.appsone.common.util.StringUtils;
import com.google.common.base.Throwables;
import com.heal.configuration.pojos.*;
import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.dao.mysql.*;
import com.heal.controlcenter.dao.redis.*;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.RulesHelperPojo;
import com.heal.controlcenter.pojo.ServicePojo;
import com.heal.controlcenter.pojo.TimezoneDetail;
import com.heal.controlcenter.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.heal.configuration.pojos.ViewTypes;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AddAccountServiceBL implements BusinessLogic<List<ServicePojo>,  UtilityBean<List<ServiceBean>>, List<IdPojo>> {
    @Autowired
    ServiceRepo serviceRepo;
    @Autowired
    AccountRepo accountRepo;

    @Autowired
    AccountsDao accountsDao;
    @Autowired
    MasterDataDao masterDataDao;
    @Autowired
    ControllerDao controllerDao;
    @Autowired
    DateTimeUtil dateTimeUtil;
    @Autowired
    TagsDao tagsDao;
    @Autowired
    NotificationRepo notificationRepo;
    @Autowired
    GetNotificationSettingsBL getNotificationSettingsBL;
    @Autowired
    ClientValidationUtils clientValidationUtils;
    @Autowired
    ServerValidationUtils serverValidationUtils;
    @Autowired
    NotificationsDao notificationsDao;
    @Autowired
    ServiceDao serviceDao;
    @Autowired
    AccountServiceDao accountServiceDao;
    @Autowired
    ControllerBL controllerBL;
    @Autowired
    TagMappingBL tagMappingBL;
    @Autowired
    MasterDataRepo masterDataRepo;
    @Autowired
    ApplicationRepo applicationRepo;

    public UtilityBean<List<ServicePojo>> clientValidation(List<ServicePojo> requestBody, String... requestParams) throws ClientException {
        String authKey = requestParams[0];
        clientValidationUtils.authKeyValidation(authKey);
        String accountIdentifier = requestParams[1];
        clientValidationUtils.accountIdentifierValidation(accountIdentifier);
        validateServiceRequestPayload(requestBody);
        Map<String, String> error = new HashMap<>();
        for (ServicePojo servicePojo : requestBody) {
            Map<String, String> validationError = servicePojo.validate();
            if (validationError != null && !validationError.isEmpty()) {
                error.putAll(validationError);
            }
        }
        if (!error.isEmpty()) {
            String err = error.toString();
            log.error(err);
            throw new ClientException(err);
        }
        HashMap<String, String> requestParamsMap = CommonUtils.buildRequestParams(authKey, accountIdentifier);
        return UtilityBean.<List<ServicePojo>>builder()
                .requestParams(requestParamsMap)
                .pojoObject(requestBody)
                .build();
    }

    @Override
    public UtilityBean<List<ServiceBean>> serverValidation(UtilityBean<List<ServicePojo>> utilityBean) throws ServerException {
        String authKey = utilityBean.getRequestParams().get(Constants.AUTH_KEY);
        String userId = serverValidationUtils.authKeyValidation(authKey);
        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
        Account account = serverValidationUtils.accountValidation(accountIdentifier);
        int accountId = account.getId();

        ViewTypesBean serviceControllerType;
        try {
            serviceControllerType = masterDataDao.getViewTypesFromMstTypeAndSubTypeName(
                    Constants.CONTROLLER_TYPE_NAME_DEFAULT,
                    Constants.SERVICES_CONTROLLER_TYPE);
        } catch (ControlCenterException e) {
            throw new ServerException(e.getMessage());
        }
        if (serviceControllerType == null) {
            throw new ServerException("Critical configuration error: 'Service' controller type not found.");
        }
        int svcTypeId = serviceControllerType.getSubTypeId();

        TagDetailsBean controllerTagDetails = masterDataDao.findTagDetailsByName(Constants.CONTROLLER_TAG, String.valueOf(accountId));
        if (controllerTagDetails == null) {
            throw new ServerException("Critical configuration error: 'Controller' tag details not found for this account.");
        }
        int controllerTagId = controllerTagDetails.getId();

        // Validate each service and construct ServiceBean list
        List<ServiceBean> serviceBeanList = new ArrayList<>();
        List<ServicePojo> services = utilityBean.getPojoObject();

        for (ServicePojo svc : services) {
            int isUpdate = 0;
            if (StringUtils.isEmpty(svc.getIdentifier())) {
                svc.setIdentifier(UUID.randomUUID().toString());
            }
            if (svc.getType() == null || svc.getType().isEmpty()) {
                svc.setType(Constants.NON_KUBERNETES);
            }
            String layer = svc.getLayer() == null ? null : svc.getLayer().trim();
            List<String> appIdentifiers = svc.getAppIdentifiers();
            List<Integer> appIds = new ArrayList<>();
            // Validate that all provided application identifiers exist for this account
            if (appIdentifiers != null && !appIdentifiers.isEmpty()) {
                for (String id : appIdentifiers) {
                    // REPLACEMENT 1: Using controllerDao
                    ControllerBean controllerBean = controllerDao.getControllerByIdentifierOrName(id.trim(), null);
                    if (controllerBean == null) {
                        String err = "App Identifier '" + id + "' does not exist.";
                        log.error(err);
                        throw new ServerException(err);
                    }
                    appIds.add(controllerBean.getId());
                }
            }
            String appIdentifier = svc.getAppIdentifiers().get(0).trim();
            ControllerBean controllerBean = controllerDao.getControllerByIdentifierOrName(appIdentifier, null);
            if (controllerBean != null) {
                Integer applicationId = controllerDao.getControllerApplicationId(controllerBean.getId(), controllerTagId, accountId);

                if (applicationId != null && controllerBean.getStatus() == 0 && controllerBean.getAccountId() == accountId && appIds.contains(applicationId)) {
                    log.info("Service identifier '{}' already exists with status 0 for acc:{}. So Reactivating..", svc.getIdentifier(), accountId);
                    isUpdate = 1;
                } else {
                    String err = "Service identifier '" + svc.getIdentifier() + "' already exists.";
                    log.error(err);
                    throw new ServerException(err);
                }
            } else {
                controllerBean = controllerDao.getControllerByIdentifierOrName(null, svc.getName());
                if (controllerBean != null) {
                    Integer applicationId = controllerDao.getControllerApplicationId(controllerBean.getId(), controllerTagId, accountId);

                    if (applicationId != null && controllerBean.getControllerTypeId() == svcTypeId && controllerBean.getStatus() == 1 &&
                            controllerBean.getAccountId() == accountId && appIds.contains(applicationId)) {
                        String err = "Service name '" + svc.getName() + "' already exists.";
                        log.error(err);
                        throw new ServerException(err);
                    }
                }
            }

            serviceBeanList.add(ServiceBean.builder()
                    .name(svc.getName().trim())
                    .identifier(svc.getIdentifier().trim())
                    .layer(layer)
                    .appIdentifier(svc.getAppIdentifiers().toString())
                    .appIds(appIds)
                    .appIdentifiers(svc.getAppIdentifiers())
                    .accountId(accountId)
                    .accountIdentifier(accountIdentifier)
                    .userId(userId)
                    .type(svc.getType())
                    .isUpdate(isUpdate)
                    .status(1)
                    .entryPointService(svc.getIsEntryPointService() == 1)
                    .build());
        }

        UtilityBean<List<ServiceBean>> updatedUtilityBean = UtilityBean.<List<ServiceBean>>builder()
                .pojoObject(serviceBeanList)
                .metadata(Map.of(Constants.ACCOUNT, account)) //TODO check this
                .requestParams(utilityBean.getRequestParams())
                .metadata(utilityBean.getMetadata())
                .build();

        updatedUtilityBean.getMetadata().put(Constants.SERVICE_BEAN_LIST, serviceBeanList);
        updatedUtilityBean.getMetadata().put(Constants.USER_ID_KEY, userId);

        return updatedUtilityBean;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<IdPojo> process(UtilityBean<List<ServiceBean>> utilityBean) throws DataProcessingException {
        try {
            List<ServiceBean> serviceBeanList = utilityBean.getPojoObject();
            List<IdPojo> idPojoList = new ArrayList<>();

            for (ServiceBean serviceBean : serviceBeanList) {
                IdPojo idPojo = addService(serviceBean); // Pass connection if needed
                idPojoList.add(idPojo);
            }

            addServiceToRedis(serviceBeanList, idPojoList); // Optional Redis logic
            return idPojoList;

        } catch (Exception e) {
            log.error("Error while processing services",e);
            throw new DataProcessingException(Throwables.getRootCause(e).getMessage());
        }
    }


    private void validateServiceRequestPayload(List<ServicePojo> services) throws ClientException {
        if (CollectionUtils.isEmpty(services)) {
            log.error("The list of services in the request body cannot be empty.");
            throw new ClientException("The list of services in the request body cannot be empty.");
        }
        Set<String> uniqueNames = services.stream()
                .map(ServicePojo::getName)
                .collect(Collectors.toSet());
        if (uniqueNames.size() < services.size()) {
            log.error("Duplicate service name found in the request payload.");
            throw new ClientException("Duplicate service name found in the request payload.");
        }
        log.info("Service payload passed all client-side business validations.");
    }

    public IdPojo addService(ServiceBean bean) throws ControlCenterException {
        List<ViewTypesBean> viewTypes = accountServiceDao.getMstSubTypeByTypeId(Integer.parseInt(Constants.CONTROLLER_TYPE_NAME_DEFAULT));
        ViewTypesBean matched = viewTypes.stream()
                .filter(vt -> Constants.SERVICES_CONTROLLER_TYPE.equalsIgnoreCase(vt.getSubTypeName()))
                .findFirst()
                .orElseThrow(() -> {
                    String err = Constants.SERVICES_CONTROLLER_TYPE + " not found in 'view_types' table.";
                    log.error(err);
                    return new ControlCenterException(err);
                });

        ControllerBean controllerBean = controllerBL.addOrUpdateController(bean, matched.getSubTypeId(), bean.getIsUpdate());
        if (controllerBean.getId() == -1) {
            String err = "Unable to add service in controller table";
            log.error(err);
            throw new ControlCenterException(err);
        }
        bean.setId(controllerBean.getId());
//
        if (bean.isEntryPointService()) {
            TagDetailsBean entryPointTagDetails = accountServiceDao.getTagDetails(Constants.ENTRY_POINT, bean.getAccountId());
            if (entryPointTagDetails == null) {
                String err = "Unable to fetch tag details for Entry-Point";
                log.error(err);
                throw new ControlCenterException(err);
            }

            int id = tagMappingBL.addTagMapping(entryPointTagDetails.getId(), controllerBean.getId(),
                    Constants.CONTROLLER, "Type", "1", bean.getUserId(), bean.getAccountId());

            if (id == -1) {
                String err = "Unable to save entry point details.";
                log.error(err);
                throw new ControlCenterException(err);
            }
        }
        return IdPojo.builder()
                .id(controllerBean.getId())
                .name(bean.getName())
                .build();
    }

    public List<Rule> buildRulesForService(ServiceBean serviceBean) {
        log.info("Populating rules for the service {}", serviceBean.getName());

        List<RulesHelperPojo> rulesHelperList = accountServiceDao.getRulesHelperPojo(serviceBean.getAccountId(), serviceBean.getId());

        if (rulesHelperList.isEmpty()) return Collections.emptyList();

        List<ViewTypes> viewTypesList = masterDataRepo.getViewTypes();

        return rulesHelperList.stream().map(rule -> {
            // RegexTypeDetail
            RegexTypeDetail regexDetail = RegexTypeDetail.builder()
                    .id(rule.getTcpId())
                    .initialPattern(rule.getTcpInitialPattern())
                    .endPattern(rule.getTcpLastPattern())
                    .length(rule.getTcpLength())
                    .build();

            // PairData
            List<PairData> pairDataList = accountServiceDao.getDataBeans(rule.getId(), rule.getHttpId());

            // RequestTypeDetail
            RequestTypeDetail requestTypeDetail = RequestTypeDetail.builder()
                    .id(rule.getHttpId())
                    .firstUriSegments(rule.getHttpFirstUriSegments())
                    .lastUriSegments(rule.getHttpLastUriSegments())
                    .completeURI(rule.getHttpCompleteURI() != 0)
                    .payloadTypeId(rule.getHttpPayloadTypeId())
                    .payloadTypeName(
                            viewTypesList.stream()
                                    .filter(vt -> vt.getSubTypeId() == rule.getHttpPayloadTypeId())
                                    .map(ViewTypes::getSubTypeName)
                                    .findFirst()
                                    .orElse(""))
                    .pairData(pairDataList)
                    .build();

            // Rule object
            return Rule.builder()
                    .id(rule.getId())
                    .name(rule.getName())
                    .monitoringEnabled(rule.getEnabled() != 0)
                    .discoveryEnabled(rule.getDiscoveryEnabled() == 1)
                    .order(rule.getOrder())
                    .ruleTypeId(rule.getRuleTypeId())
                    .ruleType(
                            viewTypesList.stream()
                                    .filter(vt -> vt.getSubTypeId() == rule.getRuleTypeId())
                                    .map(ViewTypes::getSubTypeName)
                                    .findFirst()
                                    .orElse(""))
                    .isDefault(rule.getIsDefault())
                    .maxTags(rule.getMaxTags())
                    .regexTypeDetails(regexDetail)
                    .requestTypeDetails(requestTypeDetail)
                    .transactionGroups(new ArrayList<>()) // empty for now
                    .build();
        }).collect(Collectors.toList());
    }

    public void addApplicationsToService(ServiceBean serviceBean, Map<String, IdPojo> serviceDetailMap) {
        // Fetch all apps for the account
        List<BasicEntity> allApplications = accountRepo.getAllApplications(serviceBean.getAccountIdentifier());
        List<BasicEntity> filteredApps = new ArrayList<>();

        // Filter by appIds present in serviceBean
        for (Integer appId : serviceBean.getAppIds()) {
            allApplications.stream()
                    .filter(app -> app.getId() == appId)
                    .findAny()
                    .ifPresent(filteredApps::add);
        }

        // Update service with filtered applications
        serviceRepo.updateApplicationsByServiceIdentifier(
                serviceBean.getAccountIdentifier(),
                serviceBean.getIdentifier(),
                filteredApps
        );
    }

    public void addServiceMappedToApplications(ServiceBean serviceBean, BasicEntity serviceBasicEntity) {
        for (String appIdentifier : serviceBean.getAppIdentifiers()) {
            List<BasicEntity> mappedServices = applicationRepo.getServicesMappedToApplication(
                    serviceBean.getAccountIdentifier(), appIdentifier
            );

            mappedServices.add(serviceBasicEntity); // Add the current service
            applicationRepo.updateServiceApplication(
                    serviceBean.getAccountIdentifier(), appIdentifier, mappedServices
            );
        }
    }

    public BasicEntity addNewServiceToList(ServiceRepo serviceRepo,
                                           ServiceBean serviceBean,
                                           Map<String, IdPojo> serviceDetailMap) {
        // Fetch existing services from Redis
        List<BasicEntity> allServicesDetails = serviceRepo.getAllServicesDetails(serviceBean.getAccountIdentifier());

        // Build BasicEntity object
        IdPojo idPojo = serviceDetailMap.get(serviceBean.getName());
        if (idPojo == null) {
            throw new IllegalArgumentException("Service name [" + serviceBean.getName() + "] not found in serviceDetailMap.");
        }

        BasicEntity newService = BasicEntity.builder()
                .id(idPojo.getId())
                .name(serviceBean.getName())
                .identifier(idPojo.getName()) // Assuming `getName()` returns the identifier
                .status(serviceBean.getStatus())
                .createdTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                .updatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                .lastModifiedBy(serviceBean.getUserId())
                .build();

        // Add to list and update Redis
        allServicesDetails.add(newService);
        serviceRepo.updateServiceConfiguration(serviceBean.getAccountIdentifier(), allServicesDetails);

        return newService;
    }

    private List<ServiceConfiguration> buildServiceConfigurations(ServiceBean serviceBean) {
        String currentTime = DateTimeUtil.getTimeInGMT(System.currentTimeMillis());
        String userId = serviceBean.getUserId();

        int closingWindowWithinHour = ConfProperties.getInt(
                Constants.SERVICE_CLOSING_WINDOW_WITHIN_AN_HOUR_PROPERTY_NAME,
                Constants.DEFAULT_SERVICE_CLOSING_WINDOW_WITHIN_AN_HOUR);

        int maxDataBreaksWithinHour = ConfProperties.getInt(
                Constants.SERVICE_MAX_DATA_BREAKS_WITHIN_AN_HOUR_PROPERTY_NAME,
                Constants.DEFAULT_SERVICE_MAX_DATA_BREAKS_WITHIN_AN_HOUR);

        int closingWindowMoreThanHour = ConfProperties.getInt(
                Constants.SERVICE_CLOSING_WINDOW_MORE_THAN_AN_HOUR_PROPERTY_NAME,
                Constants.DEFAULT_SERVICE_CLOSING_WINDOW_MORE_THAN_AN_HOUR);

        int maxDataBreaksMoreThanHour = ConfProperties.getInt(
                Constants.SERVICE_MAX_DATA_BREAKS_MORE_THAN_AN_HOUR_PROPERTY_NAME,
                Constants.DEFAULT_SERVICE_MAX_DATA_BREAKS_MORE_THAN_AN_HOUR);

        ServiceConfiguration configWithinHour = ServiceConfiguration.builder()
                .lastModifiedBy(userId)
                .createdTime(currentTime)
                .updatedTime(currentTime)
                .lowEnable(true)
                .mediumEnable(true)
                .highEnable(true)
                .closingWindow(closingWindowWithinHour)
                .maxDataBreaks(maxDataBreaksWithinHour)
                .build();

        ServiceConfiguration configMoreThanHour = ServiceConfiguration.builder()
                .lastModifiedBy(userId)
                .createdTime(currentTime)
                .updatedTime(currentTime)
                .lowEnable(true)
                .mediumEnable(true)
                .highEnable(true)
                .closingWindow(closingWindowMoreThanHour)
                .maxDataBreaks(maxDataBreaksMoreThanHour)
                .build();

        return List.of(configWithinHour, configMoreThanHour);
    }



    private List<Tags> buildTags(ServiceBean serviceBean) {
        List<Tags> tags = new ArrayList<>();

        tags.add(Tags.builder()
                .type(Constants.LAYER_TAG)
                .value(serviceBean.getLayer().trim())
                .key(Constants.LAYER_DEFAULT)
                .build());

        tags.add(Tags.builder()
                .type(Constants.TIME_ZONE_TAG)
                .value(String.valueOf(serviceBean.getTimezoneDetail().getOffset()))
                .key(String.valueOf(serviceBean.getTimezoneDetail().getId()))
                .build());

        tags.add(Tags.builder()
                .type(Constants.SERVICE_TYPE_TAG)
                .value(serviceBean.getType().trim())
                .key(Constants.DEFAULT_TAG_VALUE)
                .build());

        if (serviceBean.isEntryPointService()) {
            tags.add(Tags.builder()
                    .type(Constants.ENTRY_POINT)
                    .value("1")
                    .key(Constants.LAYER_DEFAULT)
                    .build());
        }

        return tags;
    }

    private void addServiceToRedis(List<ServiceBean> serviceBeanList, List<IdPojo> idPojoList) {
        Map<String, IdPojo> serviceDetailMap = idPojoList.stream()
                .collect(Collectors.toMap(IdPojo::getName, Function.identity()));

        for (ServiceBean serviceBean : serviceBeanList) {
            List<ServiceConfiguration> serviceConfigurations = buildServiceConfigurations(serviceBean);
            List<Tags> tags = buildTags(serviceBean);

            com.heal.configuration.pojos.Service serviceObject = com.heal.configuration.pojos.Service.builder()
                    .id(serviceDetailMap.get(serviceBean.getName()).getId())
                    .status(serviceBean.getStatus())
                    .createdTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                    .updatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                    .name(serviceBean.getName())
                    .type(serviceBean.getType())
                    .id(serviceDetailMap.get(serviceBean.getName()).getId())
                    .lastModifiedBy(serviceBean.getUserId())
                    .serviceConfiguration((ServiceConfiguration) serviceConfigurations)
                    .tags(tags)
                    .build();

            List<Rule> rules = buildRulesForService(serviceBean);

            serviceRepo.updateServiceRules(serviceBean.getAccountIdentifier(), serviceBean.getIdentifier(), rules);

            serviceRepo.updateServiceConfigurationByServiceIdentifier(
                    serviceBean.getAccountIdentifier(),
                    String.valueOf(serviceDetailMap.get(serviceBean.getName()).getId()),
                    serviceObject
            );

            BasicEntity basicEntityObject = addNewServiceToList(serviceRepo, serviceBean, serviceDetailMap);
            addServiceMappedToApplications(serviceBean, basicEntityObject);
            addApplicationsToService(serviceBean, serviceDetailMap);
        }
    }



}
