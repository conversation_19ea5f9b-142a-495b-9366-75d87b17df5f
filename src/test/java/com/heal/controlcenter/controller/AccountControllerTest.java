package com.heal.controlcenter.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.controlcenter.beans.AccessDetailsBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.AddAccountBL;
import com.heal.controlcenter.businesslogic.GetAccountsBL;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.Account;
import com.heal.controlcenter.pojo.Tags;
import com.heal.controlcenter.pojo.ThresholdSeverity;
import com.heal.controlcenter.util.JsonFileParser;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.powermock.api.mockito.PowerMockito.doReturn;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Unit tests for {@link AccountController}, using standalone MockMvc and mock dependencies.
 */
@ExtendWith(MockitoExtension.class)
class AccountControllerTest {

    @InjectMocks
    private AccountController accountController;

    @Mock
    private GetAccountsBL getAccountsBL;
    @Mock
    private AddAccountBL addAccountBL;
    @Mock
    private JsonFileParser jsonFileParser;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;
    private Account account;
    private static final String MOCK_AUTH_TOKEN = "mockAuthorization";

    @BeforeEach
    void setup() {
        mockMvc = MockMvcBuilders.standaloneSetup(accountController).build();
        objectMapper = new ObjectMapper();

        account = Account.builder()
                .identifier("acc-identifier")
                .accountName("Test Account")
                .status(1)
                .closingWindow(10)
                .maxDataBreaks(5)
                .thresholdSeverity(ThresholdSeverity.builder()
                        .low(true)
                        .warning(false)
                        .critical(true)
                        .build())
                .tags(List.of(new Tags("Timezone", "Asia/Kolkata")))
                .build();

        account = new Account();
        account.setIdentifier("acc-identifier");
        account.setAccountName("Test Account");
        account.setStatus(1);
        account.setClosingWindow(10);
        account.setMaxDataBreaks(5);

        ThresholdSeverity severityResponse = new ThresholdSeverity();
        severityResponse.setLow(true);
        severityResponse.setWarning(false);
        severityResponse.setCritical(true);
        account.setThresholdSeverity(severityResponse);

        Tags tags = new Tags();
        tags.setName("Timezone");
        tags.setIdentifier("Asia/Kolkata");
        account.setTags(List.of(tags));
    }

    /**
     * Test: GET /accounts with valid authorization header.
     * Expectation: Returns 200 OK with list of account responses.
     */
    @Test
    void getAccounts_WhenSuccess() throws Exception {
        HttpHeaders mockHeaders = new HttpHeaders();
        mockHeaders.set("Authorization", MOCK_AUTH_TOKEN);

        UtilityBean<Account> mockUtilityBean = UtilityBean.<Account>builder()
                .requestParams(Map.of("Authorization", MOCK_AUTH_TOKEN)).build();

        AccessDetailsBean accessDetails = new AccessDetailsBean();

        when(jsonFileParser.loadHeaderConfiguration()).thenReturn(mockHeaders);
        when(getAccountsBL.clientValidation(null, MOCK_AUTH_TOKEN)).thenReturn(mockUtilityBean);
        when(getAccountsBL.serverValidation(mockUtilityBean))
                .thenReturn(UtilityBean.<AccessDetailsBean>builder().pojoObject(accessDetails).build());
        doReturn(createMockAccounts()).when(getAccountsBL)
                .process(UtilityBean.<AccessDetailsBean>builder().pojoObject(accessDetails).build());

        mockMvc.perform(get("/accounts")
                        .header("Authorization", MOCK_AUTH_TOKEN))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(header().string("Authorization", MOCK_AUTH_TOKEN))
                .andExpect(jsonPath("$.message").value("Accounts fetched successfully."))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(2))
                .andExpect(jsonPath("$.data[0].accountId").value(1))
                .andExpect(jsonPath("$.responseStatus").value("OK"));
    }

    /**
     * Test: GET /accounts without authorization header.
     * Expectation: Returns 400 Bad Request due to missing required header.
     */
    @Test
    void getAccounts_WhenMissingAuthorization() throws Exception {
        mockMvc.perform(get("/accounts"))
                .andDo(print())
                .andExpect(status().isBadRequest());
    }

    /**
     * Test: POST /accounts with valid input and authorization header.
     * Expectation: Returns 200 OK with created account details.
     */
    @Test
    void postAccount_WhenValidInput() throws Exception {
        HttpHeaders mockHeaders = new HttpHeaders();
        mockHeaders.add("X-Test-Header", "value");

        String accountIdentifier = "acc-identifier";

        UtilityBean<Account> utilityBean = UtilityBean.<Account>builder()
                .pojoObject(account)
                .requestParams(Map.of(
                        "Authorization", MOCK_AUTH_TOKEN,
                        "accountIdentifier", accountIdentifier
                ))
                .metadata(Map.of("userId", "user-id"))
                .build();

        when(jsonFileParser.loadHeaderConfiguration()).thenReturn(mockHeaders);
        when(addAccountBL.clientValidation(any(Account.class), anyString(), anyString()))
                .thenReturn(utilityBean);
        when(addAccountBL.serverValidation(any())).thenReturn(utilityBean);
        when(addAccountBL.process(any())).thenReturn(account);

        mockMvc.perform(post("/accounts")
                        .param("accountIdentifier", accountIdentifier)
                        .header("Authorization", MOCK_AUTH_TOKEN)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(account)))
                .andExpect(status().isOk());
    }

    /**
     * Test: POST /accounts without authorization header.
     * Expectation: Returns 400 Bad Request due to missing required header.
     */
    @Test
    void postAccount_WhenMissingAuthorization() throws Exception {
        mockMvc.perform(post("/accounts")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(account)))
                .andExpect(status().isBadRequest());
    }

    /**
     * Test: POST /accounts when serverValidation throws ServerException.
     * Expectation: Returns 500 Internal Server Error.
     */
    @Test
    void postAccount_WhenServerExceptionThrown() throws Exception {
        when(addAccountBL.clientValidation(any(Account.class), anyString(), anyString()))
                .thenReturn(UtilityBean.<Account>builder()
                        .pojoObject(account)
                        .requestParams(Map.of("Authorization", MOCK_AUTH_TOKEN))
                        .build());

        when(addAccountBL.serverValidation(any()))
                .thenThrow(new ServerException("Server error"));

        mockMvc.perform(post("/accounts")
                        .param("accountIdentifier", "acc-identifier")
                        .header("Authorization", MOCK_AUTH_TOKEN)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(account)))
                .andExpect(status().isInternalServerError());
    }

    /**
     * Helper method to create mock list of accounts.
     */
    private List<Account> createMockAccounts() {
        List<Account> accounts = new ArrayList<>();

        accounts.add(Account.builder()
                .accountId(1)
                .accountName("Test Account 1")
                .identifier("test-account-001")
                .status(1)
                .publicKey("mock-public-key-1")
                .privateKey("mock-private-key-1")
                .build());

        accounts.add(Account.builder()
                .accountId(2)
                .accountName("Test Account 2")
                .identifier("test-account-002")
                .status(1)
                .publicKey("mock-public-key-2")
                .privateKey("mock-private-key-2")
                .build());

        return accounts;
    }
}