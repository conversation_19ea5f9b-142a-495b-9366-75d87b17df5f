package com.heal.controlcenter.businesslogic;


import com.heal.controlcenter.beans.TagMappingBean;
import com.heal.controlcenter.dao.mysql.AccountServiceDao;
import groovy.util.logging.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.heal.controlcenter.util.DateTimeUtil;
import com.heal.controlcenter.util.LogMessages;

@Slf4j
@Service
public class TagMappingBL {

    @Autowired
    private AccountServiceDao accountServiceDao;

    private static final Logger log = LoggerFactory.getLogger(TagMappingBL.class);

    /**
     * Adds a new tag mapping if it does not already exist in the tag_mapping table.
     *
     * @param tagId           The ID of the tag to map.
     * @param objId           The object ID to which the tag is mapped.
     * @param objectRefTable  The reference table name for the object.
     * @param tagKey          The key for the tag mapping.
     * @param tagValue        The value for the tag mapping.
     * @param userId          The user ID performing the operation.
     * @param accountId       The account ID for which the mapping is being created.
     * @return The inserted tag mapping ID, or -1 if the mapping already exists.
     */
    public int addTagMapping(int tagId, int objId, String objectRefTable, String tagKey,
                             String tagValue, String userId, int accountId) {

        Integer tagMappingId = accountServiceDao.getTagMappingId(
                tagId, objId, objectRefTable, tagKey, tagValue, accountId);

        if (tagMappingId != null && tagMappingId != 0) {
            log.error(LogMessages.TAG_MAPPING_ALREADY_EXISTS, tagId, objId, objectRefTable, tagKey, tagValue, accountId);
            return -1;
        }

        TagMappingBean bean = new TagMappingBean();
        bean.setTagId(tagId);
        bean.setObjectId(objId);
        bean.setObjectRefTable(objectRefTable);
        bean.setTagKey(tagKey);
        bean.setTagValue(tagValue);
        bean.setUserDetailsId(userId);
        bean.setAccountId(accountId);

        String date = DateTimeUtil.getTimeInGMT(System.currentTimeMillis());
        bean.setCreatedTime(date);
        bean.setUpdatedTime(date);

        int insertedId = accountServiceDao.addTagMappingDetails(bean);
        if (insertedId != -1) {
            log.info(LogMessages.TAG_MAPPING_ADD_SUCCESS, tagId, accountId);
        } else {
            log.error(LogMessages.TAG_MAPPING_ADD_FAILURE, tagId, accountId);
        }

        return insertedId;
    }

    public boolean checkTagMappingExists(int tagId, int entityId, String entityType, String tagKey, String tagValue, int accountId) {
        return accountServiceDao.isTagMappingPresent(tagId, entityId, entityType, tagKey, tagValue, accountId);
    }

}
