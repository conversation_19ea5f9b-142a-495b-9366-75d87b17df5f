package com.heal.controlcenter.util;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * Utility class for common pagination, sorting, and filtering operations on lists.
 */
public class PaginationSortingFilteringUtils {

    /**
     * Applies filtering, sorting, and pagination to a list of objects.
     *
     * @param <T>             The type of objects in the list.
     * @param list            The original list of objects.
     * @param pageable        The Pageable object containing pagination and sorting information.
     * @param searchTerm      The search term for filtering.
     * @param filterPredicate A predicate to define the filtering logic based on the search term.
     * @param sortComparators A map of property names to comparators for sorting.
     * @return A Page object containing the processed list.
     */
    public static <T> Page<T> applyPaginationSortingFiltering(
            List<T> list,
            Pageable pageable,
            String searchTerm,
            Predicate<T> filterPredicate,
            java.util.Map<String, Comparator<T>> sortComparators) {

        // 1. Apply Filtering
        List<T> filteredList;
        if (searchTerm != null && !searchTerm.trim().isEmpty()) {
            filteredList = list.stream()
                    .filter(filterPredicate)
                    .collect(Collectors.toList());
        } else {
            filteredList = list;
        }

        // 2. Apply Sorting
        Sort sort = pageable.getSort();
        if (sort.isSorted() && !filteredList.isEmpty()) {
            sort.stream().forEach(order -> {
                Comparator<T> comparator = sortComparators.get(order.getProperty());
                if (comparator != null) {
                    if (order.isDescending()) {
                        filteredList.sort(comparator.reversed());
                    } else {
                        filteredList.sort(comparator);
                    }
                }
            });
        }

        // 3. Apply Pagination
        int totalSize = filteredList.size();
        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), totalSize);

        List<T> paginatedList;
        if (start > end || start >= totalSize) {
            paginatedList = Collections.emptyList();
        } else {
            paginatedList = filteredList.subList(start, end);
        }

        return new PageImpl<>(paginatedList, pageable, totalSize);
    }
}