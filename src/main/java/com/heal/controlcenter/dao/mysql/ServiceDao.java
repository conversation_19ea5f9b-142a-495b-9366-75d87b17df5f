package com.heal.controlcenter.dao.mysql;

import com.heal.controlcenter.dao.mysql.entity.ServiceKpiThreshold;
import com.heal.controlcenter.exception.ControlCenterException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;

@Repository
@Slf4j
public class ServiceDao {

    @Autowired
    JdbcTemplate jdbcTemplate;

    public List<ServiceKpiThreshold> getAllKpiThresholds(int accountId, int serviceId) throws ControlCenterException {
        String query = "SELECT id, account_id accountId , service_id serviceId, kpi_id kpiId, " +
                "applicable_to applicableTo, operation_type_id operationTypeId, " +
                "min_threshold minThreshold, max_threshold maxThreshold, created_time createdTime, " +
                "updated_time updatedTime, user_details_id userDetailsId, start_time startTime, " +
                "kpi_attribute kpiAttribute, status status , defined_by definedBy, threshold_severity_id thresholdSeverityId, coverage_window coverageWindow " +
                "FROM service_kpi_thresholds  where service_id = ?  " +
                "and account_id = ?";
        try {
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(ServiceKpiThreshold.class), serviceId, accountId);
        } catch (Exception ex) {
            log.error("Error in fetching KpiThreshold", ex);
            throw new ControlCenterException("Error in fetching KpiThreshold");
        }
    }

    public List<ServiceKpiThreshold> getKpiThresholdsByApplicableToAndAttribute(int accountId, int serviceId, String applicableTo, String kpiAttribute) throws ControlCenterException {
        String query = "SELECT id, account_id accountId , service_id serviceId, kpi_id kpiId, " +
                "applicable_to applicableTo, operation_type_id operationTypeId, " +
                "min_threshold minThreshold, max_threshold maxThreshold, created_time createdTime, " +
                "updated_time updatedTime, user_details_id userDetailsId, start_time startTime, " +
                "kpi_attribute kpiAttribute, status status , defined_by definedBy, threshold_severity_id thresholdSeverityId, coverage_window coverageWindow " +
                "FROM service_kpi_thresholds  where service_id = ?  " +
                "and account_id = ? and applicable_to = ? and kpi_attribute = ?";
        try {
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(ServiceKpiThreshold.class), serviceId, accountId, applicableTo, kpiAttribute);
        } catch (Exception ex) {
            log.error("Error in fetching KpiThreshold", ex);
            throw new ControlCenterException("Error in fetching existing KpiThreshold");
        }
    }

    public int[] addThreshold(List<ServiceKpiThreshold> thresholdBeans) {
        String sql = "INSERT INTO service_kpi_thresholds " +
                "(account_id, service_id, kpi_id, applicable_to, operation_type_id, min_threshold, max_threshold, " +
                "created_time, updated_time, user_details_id, kpi_attribute, status, start_time, defined_by, " +
                "threshold_severity_id, coverage_window) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        return jdbcTemplate.batchUpdate(sql, new BatchPreparedStatementSetter() {
            @Override
            public void setValues(PreparedStatement ps, int i) throws SQLException {
                ServiceKpiThreshold s = thresholdBeans.get(i);
                ps.setInt(1, s.getAccountId());
                ps.setInt(2, s.getServiceId());
                ps.setInt(3, s.getKpiId());
                ps.setString(4, s.getApplicableTo());
                ps.setInt(5, s.getOperationTypeId());
                ps.setObject(6, s.getMinThreshold());
                ps.setObject(7, s.getMaxThreshold());
                ps.setTimestamp(8, s.getCreatedTime());
                ps.setTimestamp(9, s.getUpdatedTime());
                ps.setString(10, s.getUserDetailsId());
                ps.setString(11, s.getKpiAttribute());
                ps.setInt(12, s.getStatus());
                ps.setTimestamp(13, s.getStartTime());
                ps.setString(14, s.getDefinedBy());
                ps.setInt(15, s.getThresholdSeverityId());
                ps.setString(16, s.getCoverageWindow());
            }

            @Override
            public int getBatchSize() {
                return thresholdBeans.size();
            }
        });
    }

    public int[] updateThreshold(List<ServiceKpiThreshold> updateThresholds) {
        String sql = "UPDATE service_kpi_thresholds SET " +
                "min_threshold = ?, max_threshold = ?, defined_by = ?, updated_time = ?, " +
                "operation_type_id = ?, status = ?, start_time = ?, user_details_id = ?, " +
                "coverage_window = ? WHERE id = ?";

        return jdbcTemplate.batchUpdate(sql, new BatchPreparedStatementSetter() {
            @Override
            public void setValues(PreparedStatement ps, int i) throws SQLException {
                ServiceKpiThreshold s = updateThresholds.get(i);
                ps.setObject(1, s.getMinThreshold());
                ps.setObject(2, s.getMaxThreshold());
                ps.setString(3, s.getDefinedBy());
                ps.setTimestamp(4, s.getUpdatedTime());
                ps.setInt(5, s.getOperationTypeId());
                ps.setInt(6, s.getStatus());
                ps.setTimestamp(7, s.getStartTime());
                ps.setString(8, s.getUserDetailsId());
                ps.setString(9, s.getCoverageWindow());
                ps.setInt(10, s.getId());
            }

            @Override
            public int getBatchSize() {
                return updateThresholds.size();
            }
        });
    }

}
