package com.heal.controlcenter.dao.opensearch;

import com.appnomic.appsone.opeasearchquery.helper.OpenSearchQueryHelper;
import com.appnomic.appsone.opeasearchquery.queryopts.Documents;
import com.appnomic.appsone.opeasearchquery.queryopts.QueryOptions;
import com.appnomic.appsone.opeasearchquery.results.NameValuePair;
import com.appnomic.appsone.opeasearchquery.results.RawDocumentResults;
import com.appnomic.appsone.opeasearchquery.results.TabularResults;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.configuration.pojos.ThresholdConfig;
import com.heal.configuration.pojos.ViewTypes;
import com.heal.configuration.pojos.opensearch.ServiceKpiThresholds;
import com.heal.configuration.util.DateHelper;
import com.heal.controlcenter.config.OpenSearchConfig;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.pojo.StaticThresholdRules;
import com.heal.controlcenter.util.CacheWrapper;
import com.heal.controlcenter.util.CommonUtils;
import com.heal.controlcenter.util.Constants;
import lombok.extern.slf4j.Slf4j;
import org.opensearch.client.opensearch.OpenSearchClient;
import org.opensearch.client.opensearch._types.Result;
import org.opensearch.client.opensearch.core.*;
import org.opensearch.client.opensearch.core.bulk.BulkOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Repository;

import java.io.IOException;
import java.sql.Timestamp;
import java.util.*;

@Repository
@Slf4j
public class ServiceOpensearchRepo {
    @Autowired
    OpenSearchConfig openSearchConfig;
    @Autowired
    private CacheWrapper cacheWrapper;
    @Autowired
    ObjectMapper objectMapper;
    @Autowired
    CommonUtils commonUtils;

    @Value("${opensearch.cache.mode:0}")
    public int mode;

    String invalidThresholdTypeError = "Invalid thresholds view types. Subtype: {} unavailable";


    public void updateThreshold(String accountIdentifier, String serviceName, StaticThresholdRules updateSorRule, ServiceKpiThresholds configKpiDetails, Timestamp time) throws HealControlCenterException {
        try {
            long startTime = configKpiDetails.getStartTime();
            ThresholdConfig lowThreshold = updateSorRule.getLowThreshold();
            ThresholdConfig warningThreshold = updateSorRule.getWarningThreshold();
            ThresholdConfig errorThreshold = updateSorRule.getErrorThreshold();

            String date = DateHelper.getWeeksAsString(startTime, startTime).get(0);
            String indexName = Constants.Opensearch.INDEX_PREFIX_HEAL_SERVICE_KPI_THRESHOLDS + "_" + accountIdentifier.toLowerCase() + "_" + date;
            Map<String, List<ViewTypes>> viewTypes = cacheWrapper.getAllViewTypesIdMap();
            ViewTypes lowThresholdSeverityType = commonUtils.getViewTypeByNameAndSubType(viewTypes, Constants.THRESHOLD_SEVERITY_TYPE, Constants.THRESHOLD_SEVERITY_TYPE_LOW);
            if (lowThresholdSeverityType == null) {
                log.error(invalidThresholdTypeError, Constants.THRESHOLD_SEVERITY_TYPE_LOW);
                throw new HealControlCenterException("Invalid thresholds view types. Subtype: " + Constants.THRESHOLD_SEVERITY_TYPE_LOW + " unavailable");
            }

            ViewTypes mediumThresholdSeverityType = commonUtils.getViewTypeByNameAndSubType(viewTypes, Constants.THRESHOLD_SEVERITY_TYPE, Constants.THRESHOLD_SEVERITY_TYPE_MEDIUM);
            if (mediumThresholdSeverityType == null) {
                log.error(invalidThresholdTypeError, Constants.THRESHOLD_SEVERITY_TYPE_MEDIUM);
                throw new HealControlCenterException("Invalid thresholds view types. Subtype: " + Constants.THRESHOLD_SEVERITY_TYPE_MEDIUM + " unavailable");
            }

            ViewTypes highThresholdSeverityType = commonUtils.getViewTypeByNameAndSubType(viewTypes, Constants.THRESHOLD_SEVERITY_TYPE, Constants.THRESHOLD_SEVERITY_TYPE_HIGH);
            if (highThresholdSeverityType == null) {
                log.error(invalidThresholdTypeError, Constants.THRESHOLD_SEVERITY_TYPE_HIGH);
                throw new HealControlCenterException("Invalid thresholds view types. Subtype: " + Constants.THRESHOLD_SEVERITY_TYPE_HIGH + " unavailable");
            }

            int lowThresholdSeverityTypeId = lowThresholdSeverityType.getSubTypeId();
            int mediumThresholdSeverityTypeId = mediumThresholdSeverityType.getSubTypeId();
            int highThresholdSeverityTypeId = highThresholdSeverityType.getSubTypeId();
            updateIntoOpensearch(accountIdentifier, serviceName, updateSorRule, configKpiDetails, time, startTime, lowThreshold, indexName, lowThresholdSeverityTypeId);
            updateIntoOpensearch(accountIdentifier, serviceName, updateSorRule, configKpiDetails, time, startTime, warningThreshold, indexName, mediumThresholdSeverityTypeId);
            updateIntoOpensearch(accountIdentifier, serviceName, updateSorRule, configKpiDetails, time, startTime, errorThreshold, indexName, highThresholdSeverityTypeId);

        } catch (Exception e) {
            log.error("Unable to update service kpi thresholds details into [{}]. Details: accountIdentifier [{}], sericename [{}], kpiidetifier [{}], kpiattribute [{}]",
                    Constants.Opensearch.INDEX_PREFIX_HEAL_SERVICE_KPI_THRESHOLDS, accountIdentifier, serviceName, updateSorRule.getKpiId(), updateSorRule.getKpiAttribute(), e);
            throw new HealControlCenterException("Unable to update service kpi thresholds details.");
        }
    }

    private void updateIntoOpensearch(String accountIdentifier, String serviceName, StaticThresholdRules updateSorRule, ServiceKpiThresholds configKpiDetails, Timestamp time, long startTime, ThresholdConfig threshold, String indexName, int thresholdSeverityId) throws Exception {
        if (threshold == null) {
            log.debug("Threshold is null for serviceIdentifier [{}], kpiId [{}], kpiAttribute [{}], kpiLevel [{}], thresholdSeverityId [{}]", serviceName, updateSorRule.getKpiId(), updateSorRule.getKpiAttribute(), updateSorRule.getKpiLevel(), thresholdSeverityId);
            return;
        }
        OpenSearchClient openSearchClient = openSearchConfig.getOpenSearchClient(accountIdentifier, Constants.Opensearch.INDEX_PREFIX_HEAL_SERVICE_KPI_THRESHOLDS);
        if (openSearchClient == null) {
            return;
        }
        List<String> indexNames = new ArrayList<>();
        indexNames.add(indexName);

        String docId = serviceName + "#" + updateSorRule.getKpiId() + "#" + updateSorRule.getKpiAttribute() + "#" + updateSorRule.getKpiLevel() + "#" + thresholdSeverityId + "#" + startTime;
        QueryOptions queryOptions = QueryOptions.builder()
                .indexNames(indexNames)
                .fetchAllRecords(true)
                .build();

        log.debug("Searching service kpi thresholds for update with docId value [{}] in index [{}]", docId, indexName);
        RawDocumentResults results = OpenSearchQueryHelper.getRawDocuments(queryOptions, openSearchClient);
        if (results == null && results.getDocuments() != null && results.getDocuments().isEmpty()) {
            log.debug("[{}] is updated for offline mode for id {}", indexName, docId);
            return;
        }
        if (startTime == time.getTime()) {
            Map<String, Object> serviceKpiThresholds = new HashMap<>();
            serviceKpiThresholds.put("endTime", time.getTime());

            log.debug("Updating service kpi thresholds with docId value [{}] in index [{}] with timestamp value [{}] - details [{}]", docId, indexName, time, serviceKpiThresholds);
            UpdateRequest updateRequest = new UpdateRequest.Builder()
                    .index(indexName)
                    .id(docId)
                    .doc(serviceKpiThresholds)
                    .docAsUpsert(true)
                    .build();

            UpdateResponse<Object> response = openSearchClient.update(updateRequest, Object.class);
            if (response == null) {
                return;
            }
            if (response.result() == Result.Updated) {
                log.debug("[{}] is updated for offline mode for id {}", indexName, docId);
            }
        } else if (configKpiDetails.getEndTime() == 0L) {

            Map<String, Object> serviceKpiThresholds = new HashMap<>();
            serviceKpiThresholds.put("endTime", time.getTime());

            log.debug("Updating service kpi thresholds with docId value [{}] in index [{}] with timestamp value [{}] - details [{}]", docId, indexName, time, serviceKpiThresholds);
            UpdateRequest updateRequest = new UpdateRequest.Builder()
                    .index(indexName)
                    .id(docId)
                    .doc(serviceKpiThresholds)
                    .docAsUpsert(true)
                    .build();

            UpdateResponse<Object> response = openSearchClient.update(updateRequest, Object.class);

            if (response == null) {
                return;
            }

            if (response.result() == Result.Updated) {
                log.debug("[{}] is updated for online mode for id {}", Constants.Opensearch.INDEX_PREFIX_HEAL_SERVICE_KPI_THRESHOLDS, docId);
            }
        }
    }

    @Cacheable(value = Constants.OPENSEARCH_CACHE, key = "#root.methodName + #accountIdentifier + #serviceIdentifier " +
            "+ #kpiId + #thresholdType + #applicableTo + #fromTime + #toTime", condition = "#root.target.mode == 0", unless = "#result == null || #result.isEmpty()")
    public List<ServiceKpiThresholds> getServiceLevelThresholdDetails(String accountIdentifier, String serviceIdentifier, String kpiId, String applicableTo) throws HealControlCenterException {
        String indexPrefix = Constants.Opensearch.INDEX_PREFIX_HEAL_SERVICE_KPI_THRESHOLDS + "_" + accountIdentifier.toLowerCase() + "_*";
        try {
            OpenSearchClient openSearchClient = openSearchConfig.getOpenSearchClient(accountIdentifier, Constants.Opensearch.INDEX_PREFIX_HEAL_SERVICE_KPI_THRESHOLDS);
            if (openSearchClient == null) {
                return Collections.emptyList();
            }

            List<String> indexNames = new ArrayList<>();
            indexNames.add(indexPrefix);

            List<NameValuePair> matchAllFields = new ArrayList<>();
            matchAllFields.add(new NameValuePair("kpiId", kpiId));
            matchAllFields.add(new NameValuePair("serviceIdentifier", serviceIdentifier));
            matchAllFields.add(new NameValuePair("applicableTo", applicableTo));

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .isTimeSeriesData(false)
                    .matchAllFields(Optional.of(matchAllFields))
                    .timeStampFieldName("startTime")
                    .fetchAllRecords(true)
                    .build();

            log.debug("OS query for fetching service kpi threshold data: {}", queryOptions);
            RawDocumentResults results = OpenSearchQueryHelper.getRawDocuments(queryOptions, openSearchClient);
            if (results != null && results.getDocuments() != null && !results.getDocuments().isEmpty()) {
                return objectMapper.readValue(results.getDocuments().stream().map(Documents::getSource).toList().toString(), new TypeReference<>() {
                });
            }
            return Collections.emptyList();
        } catch (Exception e) {
            log.error("Error in fetching data from index {}. Details: accountId [{}]", indexPrefix, accountIdentifier, e);
            throw new HealControlCenterException("Error in fetching service KPI thresholds");
        }
    }

    public Map<String, List<ServiceKpiThresholds>> getServiceLevelThresholdDetailsBulk(String accountIdentifier, String serviceIdentifier,
                                                                                       List<Map<String, String>> queryParamsList) throws HealControlCenterException {

        String indexPrefix = Constants.Opensearch.INDEX_PREFIX_HEAL_SERVICE_KPI_THRESHOLDS + "_" + accountIdentifier.toLowerCase() + "_*";
        Map<String, List<ServiceKpiThresholds>> result = new HashMap<>();

        try {
            OpenSearchClient openSearchClient = openSearchConfig.getOpenSearchClient(accountIdentifier, Constants.Opensearch.INDEX_PREFIX_HEAL_SERVICE_KPI_THRESHOLDS);
            if (openSearchClient == null) {
                return Collections.emptyMap();
            }

            if (serviceIdentifier == null || serviceIdentifier.trim().isEmpty()) {
                log.error("Null/Empty Service identifier provided in input.");
                return Collections.emptyMap();
            }

            List<NameValuePair> matchAllFields = new ArrayList<>();
            matchAllFields.add(new NameValuePair("serviceIdentifier", serviceIdentifier));

            Set<String> kpiIds = new HashSet<>();
            Set<String> applicableTos = new HashSet<>();
            for (Map<String, String> paramMap : queryParamsList) {
                if (paramMap == null) continue;
                String kpiId = paramMap.get("kpiId");
                String applicableTo = paramMap.get("applicableTo");
                if (kpiId != null) kpiIds.add(kpiId);
                if (applicableTo != null) applicableTos.add(applicableTo);
            }

            if (kpiIds.isEmpty() || applicableTos.isEmpty()) {
                return Collections.emptyMap();
            }

            List<NameValuePair> matchAnyFieldsKpiId = kpiIds.stream()
                    .map(kpiId -> new NameValuePair("kpiId", kpiId)).toList();
            List<NameValuePair> matchAnyFieldsApplicableTo = applicableTos.stream()
                    .map(applicableTo -> new NameValuePair("applicableTo", applicableTo)).toList();

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(Collections.singletonList(indexPrefix))
                    .isTimeSeriesData(false)
                    .matchAnyOfFields(Optional.of(new ArrayList<>() {{
                        add(matchAnyFieldsKpiId);
                        add(matchAnyFieldsApplicableTo);
                    }}))
                    .matchAllFields(Optional.of(matchAllFields))
                    .timeStampFieldName("startTime")
                    .fetchAllRecords(true)
                    .build();

            log.debug("OS bulk query for fetching service kpi threshold data: {}", queryOptions);
            RawDocumentResults results = OpenSearchQueryHelper.getRawDocuments(queryOptions, openSearchClient);

            if (results != null && results.getDocuments() != null && !results.getDocuments().isEmpty()) {
                List<ServiceKpiThresholds> allThresholds = objectMapper.readValue(
                        results.getDocuments().stream().map(Documents::getSource).toList().toString(),
                        new TypeReference<>() {
                        });

                // Group results by the composite key of kpiId#applicableTo
                for (ServiceKpiThresholds threshold : allThresholds) {
                    String key = threshold.getKpiId() + "#" + threshold.getApplicableTo();
                    result.computeIfAbsent(key, k -> new ArrayList<>()).add(threshold);
                }
            }

            return result;
        } catch (Exception e) {
            log.error("Error in bulk fetching data from index {}. Details: accountId [{}], queryParams [{}]",
                    indexPrefix, accountIdentifier, queryParamsList, e);
            throw new HealControlCenterException("Error in bulk fetching service KPI thresholds");
        }
    }

    public void createThreshold(String accountIdentifier, String serviceIdentifier, StaticThresholdRules ruleDetails, Timestamp startTime, String description) throws HealControlCenterException {
        log.info("Creating threshold in Opensearch for accountIdentifier [{}], serviceIdentifier [{}], kpiId [{}], kpiAttribute [{}], kpiLevel [{}]",
                accountIdentifier, serviceIdentifier, ruleDetails.getKpiId(), ruleDetails.getKpiAttribute(), ruleDetails.getKpiLevel());
        String indexName = Constants.Opensearch.INDEX_PREFIX_HEAL_SERVICE_KPI_THRESHOLDS + "_" + accountIdentifier.toLowerCase();
        try {
            ThresholdConfig lowThreshold = ruleDetails.getLowThreshold();
            ThresholdConfig warningThreshold = ruleDetails.getWarningThreshold();
            ThresholdConfig errorThreshold = ruleDetails.getErrorThreshold();

            if (lowThreshold == null && warningThreshold == null && errorThreshold == null) {
                return;
            } else {
                if ((lowThreshold != null && !lowThreshold.validate()) || (warningThreshold != null && !warningThreshold.validate()) || (errorThreshold != null && !errorThreshold.validate())) {
                    return;
                }
            }
            Map<String, List<ViewTypes>> viewTypes = cacheWrapper.getAllViewTypesIdMap();
            ViewTypes lowThresholdSeverityType = commonUtils.getViewTypeByNameAndSubType(viewTypes, Constants.THRESHOLD_SEVERITY_TYPE, Constants.THRESHOLD_SEVERITY_TYPE_LOW);
            if (lowThresholdSeverityType == null) {
                log.error(invalidThresholdTypeError, Constants.THRESHOLD_SEVERITY_TYPE_LOW);
                throw new HealControlCenterException("Invalid thresholds view types. Subtype: " + Constants.THRESHOLD_SEVERITY_TYPE_LOW + " unavailable");
            }

            ViewTypes mediumThresholdSeverityType = commonUtils.getViewTypeByNameAndSubType(viewTypes, Constants.THRESHOLD_SEVERITY_TYPE, Constants.THRESHOLD_SEVERITY_TYPE_MEDIUM);
            if (mediumThresholdSeverityType == null) {
                log.error(invalidThresholdTypeError, Constants.THRESHOLD_SEVERITY_TYPE_MEDIUM);
                throw new HealControlCenterException("Invalid thresholds view types. Subtype: " + Constants.THRESHOLD_SEVERITY_TYPE_MEDIUM + " unavailable");
            }

            ViewTypes highThresholdSeverityType = commonUtils.getViewTypeByNameAndSubType(viewTypes, Constants.THRESHOLD_SEVERITY_TYPE, Constants.THRESHOLD_SEVERITY_TYPE_HIGH);
            if (highThresholdSeverityType == null) {
                log.error(invalidThresholdTypeError, Constants.THRESHOLD_SEVERITY_TYPE_HIGH);
                throw new HealControlCenterException("Invalid thresholds view types. Subtype: " + Constants.THRESHOLD_SEVERITY_TYPE_HIGH + " unavailable");
            }

            int lowThresholdSeverityTypeId = lowThresholdSeverityType.getSubTypeId();
            int mediumThresholdSeverityTypeId = mediumThresholdSeverityType.getSubTypeId();
            int highThresholdSeverityTypeId = highThresholdSeverityType.getSubTypeId();

            addIntoOpensearch(accountIdentifier, serviceIdentifier, ruleDetails, startTime, description, indexName, lowThreshold, lowThresholdSeverityTypeId);
            addIntoOpensearch(accountIdentifier, serviceIdentifier, ruleDetails, startTime, description, indexName, warningThreshold, mediumThresholdSeverityTypeId);
            addIntoOpensearch(accountIdentifier, serviceIdentifier, ruleDetails, startTime, description, indexName, errorThreshold, highThresholdSeverityTypeId);

        } catch (Exception e) {
            log.error("Error in inserting the agent health details or in closing connection. Details: ", e);
            throw new HealControlCenterException("Error in inserting the agent health details or in closing connection.");
        }
    }

    private void addIntoOpensearch(String accountIdentifier, String serviceIdentifier, StaticThresholdRules ruleDetails, Timestamp startTime, String description, String indexName, ThresholdConfig threshold, int thresholdSeverityId) throws IOException, HealControlCenterException {
        indexName = indexName + "_" + DateHelper.getWeeksAsString(startTime.getTime(), startTime.getTime()).get(0);
        String docId = serviceIdentifier + "#" + ruleDetails.getKpiId() + "#" + ruleDetails.getKpiAttribute() + "#" + ruleDetails.getKpiLevel() + "#" + thresholdSeverityId + "#" + startTime.getTime();

        if (threshold == null) {
            log.debug("Threshold is null for serviceIdentifier [{}], kpiId [{}], kpiAttribute [{}], kpiLevel [{}], thresholdSeverityId [{}]", serviceIdentifier, ruleDetails.getKpiId(), ruleDetails.getKpiAttribute(), ruleDetails.getKpiLevel(), thresholdSeverityId);
            return;
        }

        OpenSearchClient openSearchClient = openSearchConfig.getOpenSearchClient(accountIdentifier, Constants.Opensearch.INDEX_PREFIX_HEAL_SERVICE_KPI_THRESHOLDS);
        if (openSearchClient == null) {
            log.debug("Error while getting openSearch client to add threshold for serviceIdentifier [{}], kpiId [{}], kpiAttribute [{}], kpiLevel [{}], thresholdSeverityId [{}]", serviceIdentifier, ruleDetails.getKpiId(), ruleDetails.getKpiAttribute(), ruleDetails.getKpiLevel(), thresholdSeverityId);
            return;
        }

        Map<String, Double> thresholds = new HashMap<>();
        if (threshold.getMin() != null) {
            thresholds.put("Lower", threshold.getMin());
        }
        if (threshold.getMax() != null) {
            thresholds.put("Upper", threshold.getMax());
        }

        ServiceKpiThresholds serviceKpiThresholds = ServiceKpiThresholds.builder()
                .serviceIdentifier(serviceIdentifier)
                .kpiId(Integer.parseInt(ruleDetails.getKpiId()))
                .applicableTo(ruleDetails.getKpiLevel())
                .kpiAttribute(ruleDetails.getKpiAttribute())
                .startTime(startTime.getTime())
                .endTime(0)
                .description(description)
                .thresholdType(Constants.STATIC_THRESHOLD)
                .thresholds(thresholds)
                .operationType(threshold.getOperationType())
                .timestamp(DateHelper.getDate(startTime.getTime()))
                .thresholdSeverityId(thresholdSeverityId)
                .build();
        log.debug("Adding following service kpi thresholds details in OS : [{}] with docId [{}] at timestamp value [{}]", serviceKpiThresholds, docId, startTime);

        String finalIndexName = indexName;
        IndexResponse response = openSearchClient.index(i -> i
                .index(finalIndexName)
                .id(docId)
                .document(serviceKpiThresholds)
        );

        if (response == null) {
            log.error("Error while inserting service kpi threshold details for serviceId [{}] & kpiId [{}]", serviceIdentifier, ruleDetails.getKpiId());
            throw new HealControlCenterException("Error while inserting service kpi threshold details");
        }
        if (response.result() == Result.Created) {
            log.debug("Successfully added document to [{}]", indexName);
        }
    }

    // method for get all the kpis which is present in threshold details table
    public TabularResults getAllConfigureThresholdsKpiListsFromOS(String accountIdentifier, String serviceIdentifier, Set<String> kpiIds) throws HealControlCenterException {
        List<String> indexNames = new ArrayList<>();
        long st = System.currentTimeMillis();
        try {
            OpenSearchClient openSearchClient = openSearchConfig.getOpenSearchClient(accountIdentifier, Constants.Opensearch.INDEX_PREFIX_HEAL_SERVICE_KPI_THRESHOLDS);
            if (openSearchClient == null) {
                throw new HealControlCenterException("Unable to establish elasticsearch connection");
            }

            String indexName = Constants.Opensearch.INDEX_PREFIX_HEAL_SERVICE_KPI_THRESHOLDS + "_" + accountIdentifier.toLowerCase() + "_*";

            indexNames.add(indexName);

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("serviceIdentifier", serviceIdentifier));

            List<NameValuePair> matchAnyFields = new ArrayList<>();

            kpiIds.forEach(kpiId -> matchAnyFields.add(new NameValuePair("kpiId", kpiId)));

            List<String> columns = new ArrayList<>();
            columns.add("kpiId");
            columns.add("applicableTo");

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .isTimeSeriesData(false)
                    .matchAllFields(Optional.of(matchFields))
                    .groupByColumns(Optional.of(columns))
                    .matchAnyOfFields(Optional.of(new ArrayList<>() {{
                        add(matchAnyFields);
                    }}))
                    .useTermsMatchAnyQuery(true)
                    .timeStampFieldName("startTime")
                    .build();

            return OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, openSearchClient);

        } catch (Exception e) {
            log.error("Error in fetching data from index {}. Details: kpiIdentifiers {}, serviceIdentifier [{}]",
                    indexNames, kpiIds, serviceIdentifier, e);
            throw new HealControlCenterException("Error in fetching service KPI thresholds");
        } finally {
            log.debug("Total time taken to fetch AllConfigureThresholdsKpiListsFromOS for serviceId: [{}], kpiIds: {}, {} ms", serviceIdentifier, kpiIds, System.currentTimeMillis() - st);
        }
    }

    // bulk insert for service kpi thresholds
    @Async
    public void insertBulkServiceKpiThresholdsIntoOS(String accountIdentifier, String serviceIdentifier, List<StaticThresholdRules> ruleDetailsList,
                                                     Timestamp startTime, String description) throws HealControlCenterException {
        if (ruleDetailsList == null || ruleDetailsList.isEmpty()) {
            log.info("No threshold rules provided for bulk insert");
            return;
        }

        log.info("Creating thresholds in bulk in OpenSearch for accountIdentifier [{}], serviceIdentifier [{}], number of rules [{}]",
                accountIdentifier, serviceIdentifier, ruleDetailsList.size());

        String indexPrefix = Constants.Opensearch.INDEX_PREFIX_HEAL_SERVICE_KPI_THRESHOLDS + "_" + accountIdentifier.toLowerCase();
        String weekIndex = DateHelper.getWeeksAsString(startTime.getTime(), startTime.getTime()).get(0);
        String indexName = indexPrefix + "_" + weekIndex;

        try {
            OpenSearchClient openSearchClient = openSearchConfig.getOpenSearchClient(accountIdentifier,
                    Constants.Opensearch.INDEX_PREFIX_HEAL_SERVICE_KPI_THRESHOLDS);
            if (openSearchClient == null) {
                log.error("Unable to get OpenSearch client for accountIdentifier [{}]", accountIdentifier);
                return;
            }

            Map<String, List<ViewTypes>> viewTypes = cacheWrapper.getAllViewTypesIdMap();
            ViewTypes lowThresholdSeverityType = commonUtils.getViewTypeByNameAndSubType(viewTypes,
                    Constants.THRESHOLD_SEVERITY_TYPE, Constants.THRESHOLD_SEVERITY_TYPE_LOW);
            if (lowThresholdSeverityType == null) {
                log.error(invalidThresholdTypeError, Constants.THRESHOLD_SEVERITY_TYPE_LOW);
                return;
            }

            ViewTypes mediumThresholdSeverityType = commonUtils.getViewTypeByNameAndSubType(viewTypes,
                    Constants.THRESHOLD_SEVERITY_TYPE, Constants.THRESHOLD_SEVERITY_TYPE_MEDIUM);
            if (mediumThresholdSeverityType == null) {
                log.error(invalidThresholdTypeError, Constants.THRESHOLD_SEVERITY_TYPE_MEDIUM);
                return;
            }

            ViewTypes highThresholdSeverityType = commonUtils.getViewTypeByNameAndSubType(viewTypes,
                    Constants.THRESHOLD_SEVERITY_TYPE, Constants.THRESHOLD_SEVERITY_TYPE_HIGH);
            if (highThresholdSeverityType == null) {
                log.error(invalidThresholdTypeError, Constants.THRESHOLD_SEVERITY_TYPE_HIGH);
                return;
            }

            int lowThresholdSeverityTypeId = lowThresholdSeverityType.getSubTypeId();
            int mediumThresholdSeverityTypeId = mediumThresholdSeverityType.getSubTypeId();
            int highThresholdSeverityTypeId = highThresholdSeverityType.getSubTypeId();

            List<BulkOperation> bulkOperations = new ArrayList<>();

            for (StaticThresholdRules ruleDetails : ruleDetailsList) {
                ThresholdConfig lowThreshold = ruleDetails.getLowThreshold();
                ThresholdConfig warningThreshold = ruleDetails.getWarningThreshold();
                ThresholdConfig errorThreshold = ruleDetails.getErrorThreshold();

                if (lowThreshold == null && warningThreshold == null && errorThreshold == null) {
                    log.debug("Skipping rule with no thresholds: kpiId [{}], kpiAttribute [{}]",
                            ruleDetails.getKpiId(), ruleDetails.getKpiAttribute());
                    continue;
                }

                if ((lowThreshold != null && !lowThreshold.validate()) ||
                        (warningThreshold != null && !warningThreshold.validate()) ||
                        (errorThreshold != null && !errorThreshold.validate())) {
                    log.debug("Skipping rule with invalid threshold configuration: kpiId [{}], kpiAttribute [{}]",
                            ruleDetails.getKpiId(), ruleDetails.getKpiAttribute());
                    continue;
                }

                addBulkOperationsForThreshold(bulkOperations, serviceIdentifier, ruleDetails, startTime, description,
                        indexName, lowThreshold, lowThresholdSeverityTypeId);
                addBulkOperationsForThreshold(bulkOperations, serviceIdentifier, ruleDetails, startTime, description,
                        indexName, warningThreshold, mediumThresholdSeverityTypeId);
                addBulkOperationsForThreshold(bulkOperations, serviceIdentifier, ruleDetails, startTime, description,
                        indexName, errorThreshold, highThresholdSeverityTypeId);
            }

            if (bulkOperations.isEmpty()) {
                log.debug("No valid threshold operations to process for bulk insert");
                return;
            }

            log.debug("Executing bulk index request with [{}] operations", bulkOperations.size());
            BulkRequest bulkRequest = new BulkRequest.Builder()
                    .operations(bulkOperations)
                    .build();

            BulkResponse response = openSearchClient.bulk(bulkRequest);

            if (response == null) {
                log.error("Null response from OpenSearch bulk operation");
                return;
            }

            if (response.errors()) {
                response.items().forEach(item -> {
                    if (item.error() != null) {
                        log.error("Bulk operation error: {}, reason: {}",
                                item.error().type(), item.error().reason());
                    }
                });
            } else {
                log.info("Successfully created [{}] threshold entries in OpenSearch", bulkOperations.size());
            }

        } catch (Exception e) {
            log.error("Error in bulk inserting threshold details. Details: accountId [{}], serviceIdentifier [{}]",
                    accountIdentifier, serviceIdentifier, e);
            throw new HealControlCenterException("Error in inserting thresholds in Opensearch");
        }
    }

    private void addBulkOperationsForThreshold(List<BulkOperation> bulkOperations, String serviceIdentifier,
                                               StaticThresholdRules ruleDetails, Timestamp startTime, String description,
                                               String indexName, ThresholdConfig threshold, int thresholdSeverityId) throws IOException {
        if (threshold == null) {
            return;
        }

        String docId = serviceIdentifier + "#" + ruleDetails.getKpiId() + "#" + ruleDetails.getKpiAttribute() +
                "#" + ruleDetails.getKpiLevel() + "#" + thresholdSeverityId + "#" + startTime.getTime();

        Map<String, Double> thresholds = new HashMap<>();
        if (threshold.getMin() != null) {
            thresholds.put("min", threshold.getMin());
        }
        if (threshold.getMax() != null) {
            thresholds.put("max", threshold.getMax());
        }

        ServiceKpiThresholds serviceKpiThresholds = ServiceKpiThresholds.builder()
                .serviceIdentifier(serviceIdentifier)
                .kpiId(Integer.parseInt(ruleDetails.getKpiId()))
                .applicableTo(ruleDetails.getKpiLevel())
                .kpiAttribute(ruleDetails.getKpiAttribute())
                .startTime(startTime.getTime())
                .endTime(0)
                .description(description)
                .thresholdType(Constants.STATIC_THRESHOLD)
                .thresholds(thresholds)
                .operationType(threshold.getOperationType())
                .timestamp(DateHelper.getDate(startTime.getTime()))
                .thresholdSeverityId(thresholdSeverityId)
                .build();

        log.debug("Adding bulk operation for service kpi threshold with docId [{}]", docId);

        BulkOperation operation = BulkOperation.of(op -> op
                .index(idx -> idx
                        .index(indexName)
                        .id(docId)
                        .document(serviceKpiThresholds)
                )
        );

        bulkOperations.add(operation);
    }

    /**
     * Updates multiple service KPI thresholds in OpenSearch in a single bulk operation
     */
    @Async
    public void updateThresholdsBulk(String accountIdentifier, String serviceIdentifier,
                                     List<Map.Entry<StaticThresholdRules, ServiceKpiThresholds>> updateData, Timestamp time) throws HealControlCenterException {
        if (updateData == null || updateData.isEmpty()) {
            log.info("No thresholds to update for account [{}], service [{}]", accountIdentifier, serviceIdentifier);
            return;
        }

        try {
            Map<String, List<ViewTypes>> viewTypes = cacheWrapper.getAllViewTypesIdMap();
            // Get threshold severity types
            ViewTypes lowThresholdSeverityType = commonUtils.getViewTypeByNameAndSubType(viewTypes,
                    Constants.THRESHOLD_SEVERITY_TYPE, Constants.THRESHOLD_SEVERITY_TYPE_LOW);
            if (lowThresholdSeverityType == null) {
                log.error(invalidThresholdTypeError, Constants.THRESHOLD_SEVERITY_TYPE_LOW);
                return;
            }

            ViewTypes mediumThresholdSeverityType = commonUtils.getViewTypeByNameAndSubType(viewTypes,
                    Constants.THRESHOLD_SEVERITY_TYPE, Constants.THRESHOLD_SEVERITY_TYPE_MEDIUM);
            if (mediumThresholdSeverityType == null) {
                log.error(invalidThresholdTypeError, Constants.THRESHOLD_SEVERITY_TYPE_MEDIUM);
                return;
            }

            ViewTypes highThresholdSeverityType = commonUtils.getViewTypeByNameAndSubType(viewTypes,
                    Constants.THRESHOLD_SEVERITY_TYPE, Constants.THRESHOLD_SEVERITY_TYPE_HIGH);
            if (highThresholdSeverityType == null) {
                log.error(invalidThresholdTypeError, Constants.THRESHOLD_SEVERITY_TYPE_HIGH);
                return;
            }

            int lowThresholdSeverityTypeId = lowThresholdSeverityType.getSubTypeId();
            int mediumThresholdSeverityTypeId = mediumThresholdSeverityType.getSubTypeId();
            int highThresholdSeverityTypeId = highThresholdSeverityType.getSubTypeId();

            // Group updates by index name for efficient bulk processing
            Map<String, List<BulkOperation>> operationsByIndex = new HashMap<>();

            for (Map.Entry<StaticThresholdRules, ServiceKpiThresholds> entry : updateData) {
                StaticThresholdRules updateSorRule = entry.getKey();
                ServiceKpiThresholds configKpiDetails = entry.getValue();
                long startTime = configKpiDetails.getStartTime();

                String date = DateHelper.getWeeksAsString(startTime, startTime).get(0);
                String indexName = Constants.Opensearch.INDEX_PREFIX_HEAL_SERVICE_KPI_THRESHOLDS + "_" +
                        accountIdentifier.toLowerCase() + "_" + date;

                ThresholdConfig lowThreshold = updateSorRule.getLowThreshold();
                ThresholdConfig warningThreshold = updateSorRule.getWarningThreshold();
                ThresholdConfig errorThreshold = updateSorRule.getErrorThreshold();

                addBulkUpdateOperation(operationsByIndex, serviceIdentifier, updateSorRule,
                        time, startTime, lowThreshold, indexName, lowThresholdSeverityTypeId);
                addBulkUpdateOperation(operationsByIndex, serviceIdentifier, updateSorRule,
                        time, startTime, warningThreshold, indexName, mediumThresholdSeverityTypeId);
                addBulkUpdateOperation(operationsByIndex, serviceIdentifier, updateSorRule,
                        time, startTime, errorThreshold, indexName, highThresholdSeverityTypeId);
            }

            // Execute bulk operations for each index
            for (Map.Entry<String, List<BulkOperation>> indexOperations : operationsByIndex.entrySet()) {
                String indexName = indexOperations.getKey();
                List<BulkOperation> operations = indexOperations.getValue();

                if (operations.isEmpty()) {
                    continue;
                }

                OpenSearchClient openSearchClient = openSearchConfig.getOpenSearchClient(accountIdentifier,
                        Constants.Opensearch.INDEX_PREFIX_HEAL_SERVICE_KPI_THRESHOLDS);
                if (openSearchClient == null) {
                    log.error("Failed to get OpenSearch client for accountIdentifier [{}]", accountIdentifier);
                    continue;
                }

                log.debug("Executing [{}] bulk update operations for index [{}]", operations.size(), indexName);
                BulkRequest bulkRequest = new BulkRequest.Builder()
                        .operations(operations)
                        .build();

                BulkResponse response = openSearchClient.bulk(bulkRequest);

                if (response == null) {
                    log.error("Null response from OpenSearch bulk operation for index [{}]", indexName);
                    continue;
                }

                if (response.errors()) {
                    response.items().forEach(item -> {
                        if (item.error() != null) {
                            log.error("Bulk update error: {}, reason: {}, id: {}",
                                    item.error().type(), item.error().reason(), item.id());
                        }
                    });
                } else {
                    log.debug("Successfully updated [{}] thresholds in index [{}]", operations.size(), indexName);
                }
            }
        } catch (Exception e) {
            log.error("Error in bulk updating thresholds. Details: accountIdentifier:{}, serviceName:{}",
                    accountIdentifier, serviceIdentifier, e);
            throw new HealControlCenterException("Error in updating thresholds in Opensearch");
        }
    }

    private void addBulkUpdateOperation(Map<String, List<BulkOperation>> operationsByIndex,
                                        String serviceName, StaticThresholdRules updateSorRule,
                                        Timestamp time, long startTime, ThresholdConfig threshold,
                                        String indexName, int thresholdSeverityId) {
        if (threshold == null) {
            log.debug("Threshold is null for serviceIdentifier [{}], kpiId [{}], kpiAttribute [{}], kpiLevel [{}], thresholdSeverityId [{}]",
                    serviceName, updateSorRule.getKpiId(), updateSorRule.getKpiAttribute(), updateSorRule.getKpiLevel(), thresholdSeverityId);
            return;
        }

        String docId = serviceName + "#" + updateSorRule.getKpiId() + "#" + updateSorRule.getKpiAttribute() +
                "#" + updateSorRule.getKpiLevel() + "#" + thresholdSeverityId + "#" + startTime;

        Map<String, Object> updateDoc = new HashMap<>();
        updateDoc.put("endTime", time.getTime());

        BulkOperation operation = BulkOperation.of(op -> op
                .update(upd -> upd
                        .index(indexName)
                        .id(docId)
                        .document(updateDoc)
                        .docAsUpsert(true)
                )
        );

        operationsByIndex.computeIfAbsent(indexName, k -> new ArrayList<>()).add(operation);
        log.debug("Added bulk update operation for threshold with docId [{}] in index [{}]", docId, indexName);
    }
}
