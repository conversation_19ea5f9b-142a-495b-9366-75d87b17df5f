package com.heal.controlcenter.dao.mysql.entity;

import com.heal.controlcenter.beans.CompInstanceAttributesBean;
import com.heal.controlcenter.beans.ViewApplicationServiceMappingBean;
import com.heal.controlcenter.beans.ViewClusterServicesBean;
import com.heal.controlcenter.beans.ViewComponentInstanceBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;

@Slf4j
@Repository
public class ComponentInstanceDao {

    @Autowired
    JdbcTemplate jdbcTemplate;

    public List<ViewComponentInstanceBean> getActiveInstanceDetailsForAccount(int accountId) {
        String query = "SELECT id, name, identifier, host_id AS hostId, host_name AS hostName, is_cluster AS isCluster, " +
                "discovery, host_address AS hostAddress, is_DR AS isDR, " +
                "mst_component_id AS mstComponentId, component_name AS mstComponentName, " +
                "mst_component_type_id AS mstComponentTypeId, component_type_name AS mstComponentTypeName, " +
                "mst_component_version_id AS mstComponentVersionId, component_version_name AS componentVersionName, " +
                "common_version_id AS commonVersionId, common_version_name AS commonVersionName, " +
                "updated_time AS updatedTime " +
                "FROM view_component_instance vci " +
                "WHERE account_id = ? AND status = 1 AND is_cluster = 0";

        try {
            log.debug("Fetching active instance details for accountId: {}", accountId);
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(ViewComponentInstanceBean.class), accountId);
        } catch (Exception e) {
            log.error("Error occurred while fetching active instance details for accountId: {}. Details:", accountId, e);
            return Collections.emptyList();
        }
    }

    public List<ViewClusterServicesBean> getAllClusterServices() {
        String query = "SELECT id AS clusterId, name AS clusterName, identifier AS clusterIdentifier, " +
                "host_cluster_id AS hostClusterId, mst_component_id AS mstComponentId, " +
                "mst_component_type_id AS mstComponentTypeId, mst_component_version_id AS mstComponentVersionId, " +
                "service_id AS serviceId, service_name AS serviceName, service_identifier AS serviceIdentifier " +
                "FROM view_cluster_services";

        try {
            log.debug("Fetching all cluster services");
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(ViewClusterServicesBean.class));
        } catch (Exception e) {
            log.error("Error occurred while fetching cluster services. Details:", e);
            return Collections.emptyList();
        }
    }

    public List<ViewApplicationServiceMappingBean> getAllServiceApplication(int accountId) {
        String query = "SELECT application_id AS applicationId, " +
                "application_name AS applicationName, " +
                "application_identifier AS applicationIdentifier, " +
                "service_id AS serviceId, " +
                "service_name AS serviceName, " +
                "service_identifier AS serviceIdentifier, " +
                "account_id AS accountId " +
                "FROM view_application_service_mapping " +
                "WHERE account_id = ?";

        try {
            log.debug("Fetching all service-application mappings for accountId: {}", accountId);
            return jdbcTemplate.query(query,
                    new BeanPropertyRowMapper<>(ViewApplicationServiceMappingBean.class),
                    accountId);
        } catch (Exception e) {
            log.error("Error occurred while fetching service-application mappings for accountId: {}. Details:", accountId, e);
            return Collections.emptyList();
        }
    }

    public List<CompInstanceAttributesBean> getInstAttributeMapping(String attributeName) {
        String query = "SELECT ciav.id, ciav.attribute_value AS attributeValue, " +
                "ciav.comp_instance_id AS compInstanceId, " +
                "ciav.mst_component_attribute_mapping_id AS mstComponentAttributeMappingId, " +
                "ciav.mst_common_attributes_id AS mstCommonAttributesId, " +
                "ciav.attribute_name AS attributeName " +
                "FROM comp_instance_attribute_values ciav, mst_common_attributes mca " +
                "WHERE ciav.mst_common_attributes_id = mca.id AND mca.attribute_name = ?";

        try {
            log.debug("Fetching instance attribute mapping for attributeName: {}", attributeName);
            return jdbcTemplate.query(query,
                    new BeanPropertyRowMapper<>(CompInstanceAttributesBean.class),
                    attributeName);
        } catch (Exception e) {
            log.error("Error occurred while fetching instance attribute mappings for attributeName: {}. Details:", attributeName, e);
            return Collections.emptyList();
        }
    }
}
