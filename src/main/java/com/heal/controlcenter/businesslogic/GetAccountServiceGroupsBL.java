package com.heal.controlcenter.businesslogic;

import com.heal.configuration.pojos.Account;
import com.heal.controlcenter.beans.AccountServiceValidationBean;
import com.heal.controlcenter.beans.ServiceGroupBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.beans.ViewApplicationServiceMappingBean;
import com.heal.controlcenter.dao.mysql.AccountServiceDao;
import com.heal.controlcenter.dao.mysql.UserDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.IdPojo;
import com.heal.controlcenter.pojo.ServiceGroupListPage;
import com.heal.controlcenter.util.ClientValidationUtils;
import com.heal.controlcenter.util.CommonUtils;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.PaginationUtils;
import com.heal.controlcenter.util.ServerValidationUtils;
import com.heal.controlcenter.util.LogMessages;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class GetAccountServiceGroupsBL implements BusinessLogic<Object, UtilityBean<AccountServiceValidationBean>, Page<ServiceGroupListPage>> {

    @Autowired
    private ClientValidationUtils clientValidationUtils;
    @Autowired
    private ServerValidationUtils serverValidationUtils;
    @Autowired
    private AccountServiceDao accountServiceDao;
    @Autowired
    private UserDao userDao;

    @Override
    public UtilityBean<Object> clientValidation(Object requestBody, String... requestParams) throws ClientException {
        String accountIdentifier = requestParams[0];
        clientValidationUtils.accountIdentifierValidation(accountIdentifier);

        String searchTerm = requestParams.length > 1 ? requestParams[1] : null;
        if (searchTerm != null) {
            searchTerm = searchTerm.trim();
            if (searchTerm.isEmpty()) {
                log.warn("Search term is empty after trimming. Defaulting to empty string.");
                searchTerm = "";
            }
        } else {
            log.warn("Search term is null in requestParams. Defaulting to empty string.");
            searchTerm = "";
        }

        HashMap<String, String> requestParamsMap = CommonUtils.buildRequestParams(null, accountIdentifier);
        requestParamsMap.put(Constants.SEARCH_TERM_KEY, searchTerm);

        return UtilityBean.builder()
                .requestParams(requestParamsMap)
                .metadata(new HashMap<>())
                .build();
    }

    @Override
    public UtilityBean<AccountServiceValidationBean> serverValidation(UtilityBean<Object> utilityBean) throws ServerException {
        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
        Account account = serverValidationUtils.accountValidation(accountIdentifier);

        AccountServiceValidationBean accountServiceValidationBean = AccountServiceValidationBean.builder()
                .account(account)
                .build();

        return UtilityBean.<AccountServiceValidationBean>builder()
                .pojoObject(accountServiceValidationBean)
                .metadata(utilityBean.getMetadata())
                .pageable(utilityBean.getPageable())
                .requestParams(utilityBean.getRequestParams())
                .build();
    }

    @Override
    public Page<ServiceGroupListPage> process(UtilityBean<AccountServiceValidationBean> utilityBean) throws DataProcessingException {
        long startTime = System.currentTimeMillis();
        log.info("[process] Started processing service group retrieval");
        Account account = null;
        try {
            AccountServiceValidationBean accountServiceValidationBean = utilityBean.getPojoObject();
            account = accountServiceValidationBean.getAccount();

            Pageable pageable = utilityBean.getPageable();
            String searchTerm = utilityBean.getRequestParams().get(Constants.SEARCH_TERM_KEY);

            PaginationUtils.validatePagination(pageable);

            if (searchTerm == null) {
                log.warn("Search term is null in UtilityBean requestParams. Defaulting to empty string.");
                searchTerm = "";
            }
            log.info("Processing service groups for account {} with Pageable: {} and SearchTerm: '{}'", account.getIdentifier(), pageable, searchTerm);

            List<ServiceGroupBean> serviceGroups = accountServiceDao.getServiceGroups(account.getId(), searchTerm, pageable);
            int totalServiceGroups = accountServiceDao.getServiceGroupsCount(account.getId(), searchTerm);

            if (serviceGroups.isEmpty()) {
                log.info(LogMessages.NO_SERVICE_GROUPS_FOUND, account.getIdentifier());
                return new PageImpl<>(Collections.emptyList(), pageable, totalServiceGroups);
            }

            List<Integer> serviceGroupIds = serviceGroups.stream().map(ServiceGroupBean::getId).collect(Collectors.toList());
            List<ViewApplicationServiceMappingBean> allAppMappings = userDao.getApplicationsForServiceGroups(serviceGroupIds);
            Map<Integer, List<ViewApplicationServiceMappingBean>> appMappingsByGroupId = allAppMappings.stream()
                    .collect(Collectors.groupingBy(ViewApplicationServiceMappingBean::getServiceGroupId));

            List<Integer> linkedSvcGroupIds = serviceGroups.stream()
                    .map(ServiceGroupBean::getServiceGroupId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            List<ServiceGroupBean> linkedSvcGroups = accountServiceDao.getServiceGroupsByIds(linkedSvcGroupIds);
            Map<Integer, ServiceGroupBean> linkedSvcGroupById = linkedSvcGroups.stream()
                    .collect(Collectors.toMap(ServiceGroupBean::getId, g -> g));

            List<ServiceGroupListPage> serviceGroupListPages = serviceGroups.stream()
                    .map(serviceGroup -> {
                        ServiceGroupListPage bean = new ServiceGroupListPage();
                        bean.setId(serviceGroup.getId());
                        bean.setName(serviceGroup.getName());
                        bean.setIdentifier(serviceGroup.getIdentifier());

                        // Applications associated with this service group
                        List<ViewApplicationServiceMappingBean> appMappings = appMappingsByGroupId.get(serviceGroup.getId());
                        if (appMappings != null) {
                            Set<IdPojo> applications = appMappings.stream()
                                    .map(m -> IdPojo.builder().id(m.getApplicationId()).name(m.getApplicationName()).identifier(m.getApplicationIdentifier()).build())
                                    .collect(Collectors.toSet());
                            bean.setApplication(applications);
                        } else {
                            bean.setApplication(Collections.emptySet());
                        }

                        // Linked Service Group (self-reference)
                        if (serviceGroup.getServiceGroupId() != null) {
                            bean.setLinkedServiceGroup(linkedSvcGroupById.get(serviceGroup.getServiceGroupId()));
                        }

                        // CreatedBy and CreatedOn
                        try {
                            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            if (serviceGroup.getCreatedTime() != null) {
                                bean.setCreatedOn(sdf.parse(serviceGroup.getCreatedTime()).getTime());
                            }
                            if (serviceGroup.getUpdatedTime() != null) {
                                bean.setLastModifiedOn(sdf.parse(serviceGroup.getUpdatedTime()).getTime());
                            }
                        } catch (Exception e) {
                            log.warn(LogMessages.ERROR_PARSING_DATES_FOR_SERVICE_GROUP, serviceGroup.getName(), e.getMessage());
                        }

                        // Inbound/Outbound (placeholder for now, as per mockup)
                        bean.setInbound(Collections.singletonMap("count", 0));
                        bean.setOutbound(Collections.singletonMap("count", 0));

                        // LastModifiedBy
                        bean.setLastModifiedBy(serviceGroup.getUserDetailsId()); // Assuming userDetailsId is the user ID

                        return bean;
                    })
                    .collect(Collectors.toList());

            return PaginationUtils.createPage(serviceGroupListPages, pageable, totalServiceGroups);

        } catch (HealControlCenterException e) {
            log.error("Error while fetching service groups for account: {}. Details: ", account.getIdentifier(), e);
            throw new DataProcessingException(e.getMessage());
        } finally {
            long endTime = System.currentTimeMillis();
            log.info("[process] Completed in {} ms", (endTime - startTime));
        }
    }
}