package com.heal.controlcenter.dao.mysql;

import com.heal.configuration.pojos.ViewTypes;
import com.heal.controlcenter.beans.CompInstClusterDetailsBean;
import com.heal.controlcenter.beans.MasterComponentTypeBean;
import com.heal.controlcenter.beans.TagDetailsBean;
import com.heal.controlcenter.beans.TimezoneBean;
import com.heal.controlcenter.beans.ViewTypesBean;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.pojo.TagMappingDetails;
import com.heal.controlcenter.pojo.TimezoneDetail;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.*;

@Slf4j
@Repository
public class MasterDataDao {

    @Autowired
    JdbcTemplate jdbcTemplate;

    public TimezoneBean getTimeZoneWithId(String timeZoneId) throws HealControlCenterException {
        String query = "select id, time_zone_id timeZoneId, timeoffset offset, user_details_id userDetailsId, account_id accountId, " +
                "status status from mst_timezone where status=1 and time_zone_id=?";
        try {
            return jdbcTemplate.queryForObject(query, new BeanPropertyRowMapper<>(TimezoneBean.class), timeZoneId);
        } catch (Exception ex) {
            log.error("Error while fetching timezone details for id [{}]. Details: ", timeZoneId, ex);
            throw new HealControlCenterException("Error while fetching timezone details");
        }
    }

    public ViewTypesBean getMstSubTypeBySubTypeId(int subTypeId)  {
        String query = "select type typeName, typeid typeId, name subTypeName, subtypeid subTypeId from view_types " +
                "where subtypeid = ?";
        try {
            log.debug("getting type details.");
            return jdbcTemplate.queryForObject(query, new BeanPropertyRowMapper<>(ViewTypesBean.class), subTypeId);
        } catch (Exception e) {
            log.error("Error occurred while fetching master type and sub type info from 'view_types' table for subTypeId [{}]. Details: ", subTypeId, e);
            //throw new HealControlCenterException("Error occurred while fetching master type and sub type info.");
        }
        return null;
    }

    public List<ViewTypesBean> getMstSubTypeByTypeId(int typeId) throws HealControlCenterException {
        String query = "select type typeName, typeid typeId, name subTypeName, subtypeid subTypeId from view_types " +
                "where typeid = " + typeId;
        try {
            log.debug("getting type details.");
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(ViewTypesBean.class));
        } catch (Exception e) {
            log.error("Error occurred while fetching master type and sub type info from 'view_types' table for typeId [{}]. Details: ", typeId, e);
            throw new HealControlCenterException("Error occurred while fetching master type and sub type info.");
        }
    }

    public ViewTypesBean getViewTypesFromMstTypeAndSubTypeName(String typeName, String subTypeName) throws HealControlCenterException {
        String query = "select type typeName, typeid typeId, name subTypeName, subtypeid subTypeId from view_types where type=? and name=?";
        try {
            return jdbcTemplate.queryForObject(query, new BeanPropertyRowMapper<>(ViewTypesBean.class), typeName, subTypeName);
        } catch (Exception e) {
            log.error("Error while fetching view type using typeName [{}] and subTypeName [{}]. Details: ", typeName, subTypeName, e);
            throw new HealControlCenterException("Error while fetching view type");
        }
    }

    public List<ViewTypesBean> getMstTypeByTypeName(String typeName) throws HealControlCenterException {
        String query = "select type typeName, typeid typeId, name subTypeName, subtypeid subTypeId from view_types where type=?";
        try {
            log.debug("getting type details.");
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(ViewTypesBean.class), typeName);
        } catch (Exception e) {
            log.error("Error occurred while fetching master type and sub type info from 'view_types' table for typeName [{}]. Details: ", typeName, e);
            throw new HealControlCenterException("Error occurred while fetching master type and sub type info.");
        }
    }

    public List<CompInstClusterDetailsBean> getCompInstanceDetails(int accountId) throws HealControlCenterException {
        String query = "select id instanceId,common_version_id commonVersionId, common_version_name commonVersionName,mst_component_id compId,component_name componentName, " +
                "mst_component_type_id mstComponentTypeId,component_type_name componentTypeName, " +
                "mst_component_version_id compVersionId,component_version_name componentVersionName,name instanceName,host_id hostId,status, " +
                "host_name hostName,is_cluster isCluster,identifier, " +
                "host_address hostAddress, supervisor_id supervisorId from view_component_instance where account_id = ? and status = 1";
        try {
            log.debug("getting component instance details.");
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(CompInstClusterDetailsBean.class), accountId);
        } catch (Exception e) {
            log.error("Error occurred while fetching component instance list from 'view_component_instance' table for accountId [{}]. Details: ", accountId, e);
            throw new HealControlCenterException("Error occurred while fetching component instance details.");
        }
    }

    public List<TagMappingDetails> getTagMappingDetails(int accountId) throws HealControlCenterException {
        String query = "select id,tag_id tagId,object_id objectId,object_ref_table objectRefTable," +
                "tag_key tagKey,tag_value tagValue,created_time createdTime,updated_time updatedTime," +
                "account_id accountId,user_details_id userDetailsId from tag_mapping " +
                "where object_ref_table != 'transaction' and account_id = ?";
        try {
            log.debug("getting tag mapping details.");
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(TagMappingDetails.class), accountId);
        } catch (Exception e) {
            log.error("Error occurred while fetching tag mapping details list from 'tag_mapping' table for accountId [{}]. Details: ", accountId, e);
            throw new HealControlCenterException("Error occurred while fetching tag mapping details.");
        }
    }

    public List<ViewTypes> getAllViewTypes() throws HealControlCenterException {
        String query = "select type typeName, typeid typeId, name subTypeName, subtypeid subTypeId from view_types";
        try {
            log.debug("getting view type details.");
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(ViewTypes.class));
        } catch (Exception e) {
            log.error("Error occurred while fetching all data from 'view_types' table. Details: ", e);
            throw new HealControlCenterException("Error occurred while fetching all view types.");
        }
    }

    public List<TimezoneBean> getTimezones() throws HealControlCenterException {
        String query = "select id, time_zone_id timeZoneId, timeoffset offset, user_details_id userDetailsId, " +
                "account_id accountId, status from mst_timezone where status = 1";
        try {
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(TimezoneBean.class));
        } catch (Exception e) {
            log.error("Error occurred while fetching timezones. Details: ", e);
            throw new HealControlCenterException("Error occurred while fetching timezones.");
        }
    }

    public List<MasterComponentTypeBean> getMasterComponentTypesData(int accountId) throws HealControlCenterException {
        String query = "SELECT id, name, description, is_custom isCustom, status, created_time createdTime, " +
                "updated_time updatedTime, user_details_id userDetailsId, account_id accountId " +
                "FROM mst_component_type WHERE account_id IN (1, ?)";

        try {
            log.debug("Getting master component types data for accountId: {}", accountId);
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(MasterComponentTypeBean.class), accountId);
        } catch (Exception e) {
            log.error("Error occurred while fetching master component type data for accountId [{}]:", accountId, e);
            throw new HealControlCenterException("Error occurred while fetching master component type data.");
        }
    }

    public TagDetailsBean findTagDetailsByName(String tagName, String accountId) throws HealControlCenterException {
        String query = "SELECT id, name, tag_type_id AS tagTypeId, is_predefined AS isPredefined, " +
                "ref_table AS refTable, created_time AS createdTime, updated_time AS updatedTime, " +
                "account_id AS accountId, user_details_id AS userDetailsId " +
                "FROM tag_details WHERE name = ? AND account_id IN (1, ?)";

        try {
            log.debug("Querying for tag details with name: {}", tagName);
            return jdbcTemplate.queryForObject(query,
                    new BeanPropertyRowMapper<>(TagDetailsBean.class),
                    tagName, accountId);
        } catch (EmptyResultDataAccessException e) {
            log.warn("No active tag found with name [{}].", tagName);
            return null; // or throw an exception based on your design
        } catch (Exception e) {
            log.error("Error occurred while fetching tag details by name [{}]. Details: ", tagName, e);
            throw new HealControlCenterException("Error occurred while fetching tag details by name.");
        }
    }

    public List<TimezoneDetail> getAllActiveTimezones() throws HealControlCenterException {
        // This is the same query used by the old system to get all active timezones.
        String query = "SELECT id, time_zone_id AS timeZoneId, timeoffset AS offset, user_details_id AS userDetailsId, " +
                "account_id AS accountId, status FROM mst_timezone WHERE status=1";
        try {
            log.debug("Querying for all active timezones.");
            // Use jdbcTemplate.query for multiple results, mapping them to the TimezoneDetail bean.
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(TimezoneDetail.class));
        } catch (Exception e) {
            log.error("Error occurred while fetching all active timezones from 'mst_timezone' table. Details: ", e);
            // Return an empty list to prevent NullPointerExceptions in the calling code.
            throw new HealControlCenterException("Error occurred while fetching all active timezones.");
        }
    }

}

