package com.heal.controlcenter.controller;

import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.GetForensicActionsBL;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.ForensicActionsPojo;
import com.heal.controlcenter.util.JsonFileParser;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.web.servlet.MockMvc;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(ActionsController.class)
class ActionsControllerIT {

    @Autowired
    private MockMvc mockMvc;
    @MockBean
    GetForensicActionsBL getForensicActionsBL;
    @MockBean
    JsonFileParser headersParser;

    List<ForensicActionsPojo> forensicActionsList = null;
    UtilityBean<Object> mockUtilityBean = null;
    String[] requestParams = new String[2];

    @BeforeEach
    void setUp() {
        requestParams[0] = "mockAuthToken";
        requestParams[1] = "mockAccountIdentifier";
        forensicActionsList = new ArrayList<>();
        mockUtilityBean = UtilityBean.builder()
                .requestParams(Map.of(
                        "Authorization", "mockAuthToken",
                        "accountIdentifier", "mockAccountIdentifier"
                ))
                .metadata(Map.of(
                        "userId", "mockUserId"
                ))
                .build();
    }

    @AfterEach
    void tearDown() {
        forensicActionsList = null;
    }

    @Test
    void getForensicActions_WhenSuccess() throws Exception {
        when(getForensicActionsBL.clientValidation(null, requestParams)).thenReturn(mockUtilityBean);
        when(getForensicActionsBL.serverValidation(mockUtilityBean)).thenReturn(1);
        when(getForensicActionsBL.process(anyInt())).thenReturn(forensicActionsList);
        when(headersParser.loadHeaderConfiguration()).thenReturn(new HttpHeaders() {{
            set("authorization", "mockAuthorization");
        }});
        MockHttpServletResponse mockHttpServletResponse = mockMvc.perform(get("/accounts/{identifier}/forensic-actions", "mockIdentifier")
                        .header("authorization", "mockAuthorization"))
                .andExpect(status().isOk())
                .andExpect(header().string("authorization", "mockAuthorization"))
                .andExpect(jsonPath("$.responseStatus", Matchers.equalTo("OK")))
                .andExpect(jsonPath("$.message", Matchers.equalTo("Forensic actions fetched successfully.")))
                .andExpect(jsonPath("$.data", Matchers.hasSize(0)))
                .andReturn()
                .getResponse();
        assertEquals(mockHttpServletResponse.getStatus(), HttpStatus.OK.value());
    }

    @Test
    void getForensicActions_WhenServerException() throws Exception {
        mockUtilityBean = null;
        when(getForensicActionsBL.clientValidation("", requestParams)).thenReturn(mockUtilityBean);
        when(getForensicActionsBL.serverValidation(mockUtilityBean)).thenThrow(ServerException.class);
        when(headersParser.loadHeaderConfiguration()).thenReturn(new HttpHeaders() {{
            set("authorization", "mockAuthorization");
        }});
        MockHttpServletResponse mockHttpServletResponse = mockMvc.perform(get("/accounts/{identifier}/forensic-actions", "mockIdentifier")
                        .header("authorization", "mockAuthorization"))
                .andExpect(status().isBadRequest())
                .andExpect(header().string("authorization", "mockAuthorization"))
                .andExpect(jsonPath("$.responseStatus", Matchers.equalTo("BAD_REQUEST")))
                .andExpect(jsonPath("$.data", Matchers.aMapWithSize(6)))
                .andExpect(jsonPath("$.data", Matchers.hasKey("status")))
                .andExpect(jsonPath("$.data", Matchers.hasKey("type")))
                .andExpect(jsonPath("$.data", Matchers.hasKey("path")))
                .andExpect(jsonPath("$.data", Matchers.hasKey("error")))
                .andReturn()
                .getResponse();
        assertEquals(mockHttpServletResponse.getStatus(), HttpStatus.BAD_REQUEST.value());
    }
}
