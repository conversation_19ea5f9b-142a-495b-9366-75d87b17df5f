package com.heal.controlcenter.dao.mysql;

import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.util.DateTimeUtil;
import com.heal.controlcenter.util.HealthMetrics;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;

@Slf4j
@Repository
public class UserDao {

    @Autowired
    JdbcTemplate jdbcTemplate;
    @Autowired
    DateTimeUtil dateTimeUtil;
    @Autowired
    HealthMetrics healthMetrics;

    public UserAccessBean fetchUserAccessDetailsUsingIdentifier(String userIdentifier) {
        try {
            String query = "select a.access_details, a.user_identifier from user_access_details a where user_identifier=?";
            return jdbcTemplate.queryForObject(query, new BeanPropertyRowMapper<>(UserAccessBean.class),
                    userIdentifier);
        } catch (DataAccessException e) {
            log.error("Error while fetching user access information for user [{}]. Details: ", userIdentifier, e);
        }

        return null;
    }

    public String getUsernameFromIdentifier(String userId) throws ControlCenterException {
        String query = "select username from user_attributes where user_identifier= ?";
        try {
            return jdbcTemplate.queryForObject(query, String.class, userId);
        } catch (Exception ex) {
            log.error("Error in fetching username for userId [{}]. Details: ", userId, ex);
            throw new ControlCenterException("Error in fetching username");
        }
    }

    /**
     * Retrieves detailed user information based on the provided user identifier.
     *
     * This method performs a join between the `user_attributes` and `user_access_details` tables
     * to gather complete user data, including access profile, contact details, and timezone preferences.
     *
     * @param userId the unique user identifier (e.g., UUID or username)
     * @return a populated {@link UserInfoBean} object containing user details
     * @throws ControlCenterException if any error occurs while fetching data from the database
     */
    public UserInfoBean getUserDetails(String userId) throws ControlCenterException {
        String query = "SELECT u.id mysqlId, u.user_identifier id, u.username userName, u.mst_access_profile_id profileId, " +
                "u.mst_role_id roleId, u.status, a.access_details accessDetailsJSON, email_address emailId, contact_number contactNumber, " +
                "u.is_timezone_mychoice isTimezoneMychoice, u.is_notifications_timezone_mychoice isNotificationsTimezoneMychoice " +
                "FROM user_attributes u, user_access_details a " +
                "where u.user_identifier = a.user_identifier and u.user_identifier = ? ";
        try {
            log.debug("getting user details");
            return jdbcTemplate.queryForObject(query, new BeanPropertyRowMapper<>(UserInfoBean.class), userId);
        } catch (Exception ex) {
            log.error("Error in getting user details for user identifier [{}]. Details: ", userId, ex);
            throw new ControlCenterException("Error in getting user details");
        }
    }

    /**
     * Retrieves user profile information for the given profile ID.
     *
     * This method fetches the access profile name and associated role
     * by performing a join between the `mst_access_profiles` and `mst_roles` tables.
     *
     * @param profileId the ID of the access profile to retrieve
     * @return a {@link UserProfileBean} object containing the user profile name and role
     * @throws ControlCenterException if any error occurs while querying the database
     */
    public UserProfileBean getUserProfile(int profileId) throws ControlCenterException {
        String query = "select map.id userProfileId, map.name userProfileName, mr.name role " +
                "from mst_access_profiles map, mst_roles mr where mr.id=map.mst_role_id and map.id=?";
        try {
            return jdbcTemplate.queryForObject(query, new BeanPropertyRowMapper<>(UserProfileBean.class), profileId);
        } catch (Exception ex) {
            log.error("Error in getting user profiles.");
            throw new ControlCenterException("Error in getting user profiles.");
        }
    }

    public UserAttributesBean getRoleProfileInfoForUserId(String userIdentifier) throws ControlCenterException {
        String ROLE_PROFILE_FOR_USER = "select mr.name roleName, mr.id roleId, map.id accessProfileId, map.name accessProfileName " +
                "from mst_roles mr join mst_access_profiles map join user_attributes ua on mr.id=ua.mst_role_id " +
                "and mr.id=map.mst_role_id and map.id=mst_access_profile_id where ua.user_identifier='" + userIdentifier + "'";
        try {
            return jdbcTemplate.queryForObject(ROLE_PROFILE_FOR_USER, new BeanPropertyRowMapper<>(UserAttributesBean.class));
        } catch (Exception e) {
            log.error("Exception encountered while fetching user role and profile. Details: ", e);
            throw new ControlCenterException("Exception encountered while fetching user role and profile");
        }
    }

    public List<String> getUserAccessibleActions(int accessProfileId) throws ControlCenterException {
        String USER_ACCESSIBLE_ACTION = "select vrd.action_identifier from mst_access_profile_mapping mapm join " +
                "view_route_details vrd on mst_big_feature_id=vrd.big_feature_id where " +
                "dashboard_name='ControlCenter' and mst_access_profile_id= ?";
        try {
            return jdbcTemplate.queryForList(USER_ACCESSIBLE_ACTION, String.class, accessProfileId);
        } catch (Exception e) {
            log.error("Exception encountered while fetching user attributes information. Details: ", e);
            throw new ControlCenterException("Exception encountered while fetching user attributes information");
        }
    }

    public String getHealSetup() {
        String defaultSetupType = "Keycloak";
        String setup = defaultSetupType;
        try {
            String query = "SELECT value FROM a1_installation_attributes where name = 'SetupType'";
            setup = jdbcTemplate.queryForObject(query, String.class);
            if (setup == null) {
                return defaultSetupType;
            }
        } catch (Exception e) {
            log.error("Exception while getting setup type: ", e);
        }
        return setup;
    }

    public List<UserDetailsBean> getNonSuperUsers() throws ControlCenterException {
        String query = "SELECT u.user_identifier userId, u.username userName, u.user_details_id updatedBy, a.name userProfile, " +
                "r.name role, u.status, u.updated_time updatedOn FROM user_attributes u, mst_roles r, mst_access_profiles a " +
                "where u.mst_access_profile_id = a.id and u.mst_role_id = r.id and a.mst_role_id = r.id and r.name != 'Super Admin'";
        try {
            return jdbcTemplate.query(query, (rs, rowNum) -> {
                UserDetailsBean userDetailsBean = new UserDetailsBean();
                userDetailsBean.setUserId(rs.getString("userId"));
                userDetailsBean.setUpdatedBy(rs.getString("updatedBy"));
                userDetailsBean.setUserName(rs.getString("userName"));
                userDetailsBean.setUpdatedOn(dateTimeUtil.getGMTToEpochTime(rs.getString("updatedOn")));
                userDetailsBean.setStatus(rs.getInt("status"));
                userDetailsBean.setUserProfile(rs.getString("userProfile"));
                userDetailsBean.setRole(rs.getString("role"));
                return userDetailsBean;
            });
        } catch (Exception e) {
            log.error("Exception encountered while fetching users. Reason: ", e);
            throw new ControlCenterException("Exception encountered while fetching users");
        }
    }

    public List<IdBean> getRoles() throws ControlCenterException {
        String query = "select id, name from mst_roles where ui_visible = 1";
        try {
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(IdBean.class));
        } catch (Exception ex) {
            log.info("Error while getting user roles details from DB", ex);
            throw new ControlCenterException("Error in fetching user roles details");
        }
    }

    public List<UserProfileBean> getUserProfiles() throws ControlCenterException {
        String query = "select map.id userProfileId, map.name userProfileName, mr.name role from mst_access_profiles map," +
                "mst_roles mr where mr.id=map.mst_role_id";
        try {
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(UserProfileBean.class));
        } catch (Exception ex) {
            log.info("Error in fetching user profile details from DB", ex);
            throw new ControlCenterException("Error in fetching user profile details");
        }
    }

    public List<String> getAccessProfileMapping(int profileId) throws ControlCenterException {
        String query = "select mb.name from mst_access_profile_mapping ma, mst_big_features mb " +
                "where ma.mst_big_feature_id=mb.id and mb.ui_visible=1 and ma.mst_access_profile_id=?";
        try {
            return jdbcTemplate.queryForList(query, String.class, profileId);
        } catch (Exception ex) {
            log.info("Error in fetching user profiles mapping details from DB", ex);
            throw new ControlCenterException("Error in fetching user profiles mapping");
        }
    }

    public List<IdBean> getRolesById(Long roleId) throws ControlCenterException {
        String query = "select id, name from mst_roles where ui_visible = 1 and id = ?";
        try {
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(IdBean.class), roleId);
        } catch (Exception ex) {
            log.info("Error while getting user roles details from DB", ex);
            throw new ControlCenterException("Error in fetching user roles details");
        }
    }

    /**
     * Updates the last login time for a user in the database.
     *
     * @param userIdentifier the unique identifier of the user
     * @param lastLoginTime  the last login time to be set (as a String)
     * @return the number of rows affected, or -1 if an error occurs
     */
    public int updateUserLoginTime(String userIdentifier, String lastLoginTime) {
        String query = "UPDATE user_attributes SET last_login_time = ? WHERE user_identifier = ?";

        try {
            return jdbcTemplate.update(query, lastLoginTime, userIdentifier);
        } catch (Exception e) {
            healthMetrics.updateHealApiServiceErrors();
            log.error("Exception occurred when updating last login time for user [{}]. Details: ", userIdentifier, e);
            return -1;
        }
    }

    public List<ViewApplicationServiceMappingBean> getUserAccessibleApplicationsServices(String userId, String accountIdentifier) throws ControlCenterException {
        String query = "SELECT vasm.application_id applicationId, vasm.application_name applicationName, " +
                "vasm.application_identifier applicationIdentifier, vasm.service_id serviceId, " +
                "vasm.service_name serviceName, vasm.service_identifier serviceIdentifier, " +
                "vasm.account_id accountId FROM view_application_service_mapping vasm " +
                "JOIN user_attributes ua ON ua.user_identifier = ? " +
                "WHERE vasm.account_id = (SELECT id FROM account WHERE identifier = ?)";
        try {
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(ViewApplicationServiceMappingBean.class), userId, accountIdentifier);
        } catch (Exception ex) {
            log.error("Error in getting user accessible applications services for user [{}] and account [{}]", userId, accountIdentifier, ex);
            throw new ControlCenterException("Error in getting user accessible applications services");
        }
    }

    public List<ViewApplicationServiceMappingBean> getApplicationsForServiceGroup(int serviceGroupId) throws ControlCenterException {
        String query = "SELECT DISTINCT vasm.application_id AS applicationId, vasm.application_name AS applicationName, " +
                "vasm.application_identifier AS applicationIdentifier " +
                "FROM service_group_mapping sgm " +
                "JOIN view_application_service_mapping vasm ON sgm.service_id = vasm.service_id " +
                "WHERE sgm.service_group_id = ?";
        try {
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(ViewApplicationServiceMappingBean.class), serviceGroupId);
        } catch (Exception e) {
            log.error("Error in getting applications for service group [{}]", serviceGroupId, e);
            throw new ControlCenterException("Error in getting applications for service group");
        }
    }

    /**
     * Retrieves a list of applications associated with a list of service groups.
     *
     * @param serviceGroupIds A list of service group IDs.
     * @return A list of {@link ViewApplicationServiceMappingBean} objects, each representing an application.
     * @throws ControlCenterException If an error occurs while fetching the data.
     */
    public List<ViewApplicationServiceMappingBean> getApplicationsForServiceGroups(List<Integer> serviceGroupIds) throws ControlCenterException {
        String query = "SELECT DISTINCT vasm.application_id AS applicationId, vasm.application_name AS applicationName, " +
                "vasm.application_identifier AS applicationIdentifier, sgm.service_group_id AS serviceGroupId " +
                "FROM service_group_mapping sgm " +
                "JOIN view_application_service_mapping vasm ON sgm.service_id = vasm.service_id " +
                "WHERE sgm.service_group_id IN (" + String.join(",", Collections.nCopies(serviceGroupIds.size(), "?")) + ")";
        try {
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(ViewApplicationServiceMappingBean.class), serviceGroupIds.toArray());
        } catch (Exception e) {
            log.error("Error in getting applications for service groups", e);
            throw new ControlCenterException("Error in getting applications for service groups");
        }
    }
}
