package com.heal.controlcenter.businesslogic;

import com.heal.controlcenter.beans.AccountBean;
import com.heal.controlcenter.beans.SMTPDetailsBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.beans.ViewTypesBean;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.dao.mysql.MasterDataDao;
import com.heal.controlcenter.dao.mysql.NotificationsDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.SMTPDetailsPojo;
import com.heal.controlcenter.util.*;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.crypto.DataLengthException;
import org.bouncycastle.crypto.InvalidCipherTextException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class PutEmailConfigurationsBL implements BusinessLogic<SMTPDetailsPojo, UtilityBean<SMTPDetailsPojo>, Object> {

    @Autowired
    AccountsDao accountDao;
    @Autowired
    CommonUtils commonUtils;
    @Autowired
    NotificationsDao notificationsDao;
    @Autowired
    MasterDataDao masterDataDao;
    @Autowired
    DateTimeUtil dateTimeUtil;
    @Autowired
    AECSBouncyCastleUtil aecsBouncyCastleUtil;
    @Autowired
    ClientValidationUtils clientValidationUtils;
    @Autowired
    ServerValidationUtils serverValidationUtils;

    /**
     * Validates client-side inputs including auth key, account identifier, and SMTP payload.
     * Uses utility validation methods and internal validation logic of SMTPDetailsPojo.
     * Returns a UtilityBean containing request parameters and the SMTP details object.
     * Throws ClientException if validation fails.
     */
    @Override
    public UtilityBean<SMTPDetailsPojo> clientValidation(SMTPDetailsPojo smtpDetailsPojo, String... requestParams) throws ClientException {

        String authKey = requestParams[0];
        clientValidationUtils.authKeyValidation(authKey);
        String accountIdentifier = requestParams[1];
        clientValidationUtils.accountIdentifierValidation(accountIdentifier);

        Map<String, String> error = smtpDetailsPojo.validate();
        if (!error.isEmpty()) {
            String err = error.toString();
            log.error(err);
            throw new ClientException(err);
        }

        HashMap<String, String> requestParamsMap = CommonUtils.buildRequestParams(authKey, accountIdentifier);

        return UtilityBean.<SMTPDetailsPojo>builder()
                .requestParams(requestParamsMap)
                .pojoObject(smtpDetailsPojo)
                .build();
    }

    /**
     * Validates server-side inputs such as auth key and account identifier.
     * Fetches and attaches the AccountBean to the UtilityBean for further processing.
     * Adds the validated userId to the request parameters.
     * Throws ServerException if the account or user is invalid.
     */
    @Override
    public UtilityBean<SMTPDetailsPojo> serverValidation(UtilityBean<SMTPDetailsPojo> utilityBean) throws ServerException {
        String authKey = utilityBean.getRequestParams().get(Constants.AUTH_KEY);
        String userId = serverValidationUtils.authKeyValidation(authKey);

        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
        AccountBean account = accountDao.getAccountByIdentifier(accountIdentifier);
        if (account == null) {
            String message = String.format(UIMessages.ACCOUNT_IDENTIFIER_UNAVAILABLE, accountIdentifier);
            log.error(message);
            throw new ServerException(message);
        }

        utilityBean.setAccount(account);

        return UtilityBean.<SMTPDetailsPojo>builder()
                .metadata(Map.of(Constants.USER_ID_KEY, userId))
                .build();
    }

    /**
     * Updates existing SMTP configuration in the database for a valid account.
     * Handles password decryption and re-encryption, and validates security protocol.
     * Throws DataProcessingException if update fails or if data is not in expected state.
     * Ensures transaction rollback on exceptions.
     */
    @Override
    @Transactional(rollbackFor = {Exception.class, RuntimeException.class})
    public Object process(UtilityBean<SMTPDetailsPojo> smtpUtilityBean) throws DataProcessingException {
        Date time = dateTimeUtil.getCurrentTimestampInGMT();
        DateFormat dateFormat = new SimpleDateFormat(Constants.DATE_TIME);
        String createdTime = dateFormat.format(time);
        SMTPDetailsPojo smtpDetails = smtpUtilityBean.getPojoObject();
        AccountBean account = smtpUtilityBean.getAccount();
        String userId = (String) smtpUtilityBean.getMetadata().get(Constants.USER_ID_KEY);
        String plainTxt = "";

        SMTPDetailsBean smtpDetailsBeanExists = notificationsDao.getSMTPDetails(account.getId());
        if (smtpDetailsBeanExists == null) {
            log.error("Email details not found for accountId [{}].", account.getId());
            throw new DataProcessingException("Error occurred, Email details not found.");
        }

        ViewTypesBean securityType;
        try {
            securityType = masterDataDao.getViewTypesFromMstTypeAndSubTypeName(Constants.SMTP_PROTOCOLS, smtpDetails.getSecurity());
        } catch (ControlCenterException e) {
            throw new DataProcessingException(e.getMessage());
        }
        if (securityType == null) {
            log.error("Security type details unavailable for type [{}].", smtpDetails.getSecurity());
            throw new DataProcessingException("Security type details unavailable");
        }

        if (smtpDetails.getPassword() == null || smtpDetails.getPassword().trim().isEmpty()) {
            smtpDetails.setPassword("");

        } else {
            try {
                plainTxt = aecsBouncyCastleUtil.decrypt(smtpDetails.getPassword());
                if (plainTxt.isEmpty()) {
                    String err = "Password is not encrypted properly";
                    log.error(err);
                    throw new DataProcessingException(err);
                }
            } catch (InvalidCipherTextException | DataLengthException e) {
                log.error("Exception encountered while decrypting the password. Details: ", e);
                throw new DataProcessingException("Error occurred while decrypting the password.");
            }
        }

        try {
            smtpDetails.setPassword(commonUtils.encryptInBCEC(plainTxt));
        } catch (Exception e) {
            log.error("Exception encountered while encrypting the password. Details: ", e);
            throw new DataProcessingException("Error occurred while encrypting the password from the database");
        }

        SMTPDetailsBean smtpDetailsBean = SMTPDetailsBean.builder()
                .accountId(account.getId())
                .address(smtpDetails.getAddress())
                .port(smtpDetails.getPort())
                .lastModifiedBy(userId)
                .username(smtpDetails.getUsername())
                .password(smtpDetails.getPassword())
                .securityId(securityType.getSubTypeId())
                .createdTime(createdTime)
                .updatedTime(createdTime)
                .status(1)
                .fromRecipient(smtpDetails.getFromRecipient())
                .build();

        try {
            notificationsDao.updateSMTPDetails(smtpDetailsBean);
        } catch (Exception e) {
            throw new DataProcessingException(e.getMessage());
        }

        return null;
    }
}
