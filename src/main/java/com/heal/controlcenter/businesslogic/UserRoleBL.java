package com.heal.controlcenter.businesslogic;

import com.heal.controlcenter.beans.IdBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.UserDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.util.ClientValidationUtils;
import com.heal.controlcenter.util.CommonUtils;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.ServerValidationUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> - 18-10-2021
 */
@Service
@Slf4j
public class UserRoleBL implements BusinessLogic<String, String, List<IdBean>> {

    @Autowired
    CommonUtils commonUtils;
    @Autowired
    UserDao userRolesAndProfilesDao;
    @Autowired
    ClientValidationUtils clientValidationUtils;
    @Autowired
    ServerValidationUtils serverValidationUtils;

    /**
     * Validates the client-side request parameters, such as authKey.
     * Constructs a UtilityBean containing the validated request parameters.
     * Throws ClientException if validation fails.
     */
    @Override
    public UtilityBean<String> clientValidation(String requestBody, String... requestParams) throws ClientException {
        String authKey = requestParams[0];
        clientValidationUtils.authKeyValidation(authKey);

        HashMap<String, String> requestParamsMap = new HashMap<>();
        requestParamsMap.put(Constants.AUTH_KEY, authKey);

        return UtilityBean.<String>builder()
                .requestParams(requestParamsMap)
                .build();
    }

    /**
     * Performs server-side validation using the provided authKey.
     * Ensures the authKey is valid in the server context.
     * Throws ServerException if validation fails.
     */
    @Override
    public String serverValidation(UtilityBean<String> utilityBean) throws ServerException {
        String authKey = utilityBean.getRequestParams().get(Constants.AUTH_KEY);
        serverValidationUtils.authKeyValidation(authKey);
        return null;
    }

    @Override
    public List<IdBean> process(String bean) throws DataProcessingException {
        List<IdBean> data;
        try {
            log.debug("getting role details");
            data = userRolesAndProfilesDao.getRoles();
        } catch (ControlCenterException e) {
            throw new DataProcessingException(e.getMessage());
        }
        if (Objects.isNull(data) || data.isEmpty()) {
            throw new DataProcessingException("User roles information unavailable");
        }
        log.info("User roles information fetched successfully");
        return data;
    }
}
