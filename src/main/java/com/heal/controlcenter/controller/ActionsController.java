package com.heal.controlcenter.controller;

import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.GetForensicActionsBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.ForensicActionsPojo;
import com.heal.controlcenter.pojo.ResponsePojo;
import com.heal.controlcenter.swagger.CommonApiResponses;
import com.heal.controlcenter.util.JsonFileParser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@Configuration
@RestController
public class ActionsController {

    @Autowired
    GetForensicActionsBL getForensicActionsBL;
    @Autowired
    JsonFileParser headersParser;

    @Operation(
            summary = "Retrieves forensic actions for the specified account.",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Forensic actions fetched successfully.",
                            content = @Content(schema = @Schema(implementation = ForensicActionsPojo.class))
                    )
            }
    )
    @CommonApiResponses
    @RequestMapping(value ="/accounts/{identifier}/forensic-actions", method = RequestMethod.GET)
    public ResponseEntity<ResponsePojo<List<ForensicActionsPojo>>> getForensicActions(
            @Parameter(
                    name = "Authorization",
                    description = "Bearer token for authentication",
                    required = true,
                    example = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            )
            @RequestHeader("Authorization") String authorization,
            @Parameter(
                    name = "identifier",
                    description = "Unique identifier of the account",
                    required = true,
                    example = "account-001"
            )
            @PathVariable("identifier") String accountIdentifier)
            throws ClientException, ServerException, DataProcessingException {
        log.trace("Method Invoked : getForensicActions");

        UtilityBean<Object> compInstanceUtilityBean = getForensicActionsBL.clientValidation(null, authorization, accountIdentifier);
        Integer accountId = getForensicActionsBL.serverValidation(compInstanceUtilityBean);
        List<ForensicActionsPojo> data = getForensicActionsBL.process(accountId);

        ResponsePojo<List<ForensicActionsPojo>> responseBean = new ResponsePojo<>("Forensic actions fetched successfully.", data, HttpStatus.OK);

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responseBean);
    }
}
