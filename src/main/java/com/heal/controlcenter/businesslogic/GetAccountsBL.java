package com.heal.controlcenter.businesslogic;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.dao.mysql.TagsDao;
import com.heal.controlcenter.dao.mysql.UserDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.Account;
import com.heal.controlcenter.pojo.Tags;
import com.heal.controlcenter.pojo.ThresholdSeverity;
import com.heal.controlcenter.util.*;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Business logic class for handling account retrieval operations.
 * Implements validation, authorization, and data transformation logic.
 */
@Slf4j
@Component
public class GetAccountsBL implements BusinessLogic<String, UtilityBean<AccessDetailsBean>, Page<Account>> {

    @Autowired
    TagsDao tagsDao;
    @Autowired
    UserDao userDao;
    @Autowired
    AccountsDao accountDao;
    @Autowired
    ObjectMapper objectMapper;
    @Autowired
    KeyCloakAuthService keyCloakAuthService;
    @Autowired
    ClientValidationUtils clientValidationUtils;
    @Autowired
    ServerValidationUtils serverValidationUtils;

    /**
     * Performs client-side validation for the Get Accounts request.
     * This method validates only the authorization key and search term.
     *
     * @param requestBody   Not used for validation (kept for interface compatibility).
     * @param requestParams Varargs: [0] = authorization token, [1] = optional search term.
     * @return A UtilityBean containing request parameters (auth key, search term) for further processing.
     * @throws ClientException If the authorization key is missing or invalid.
     */
    @Override
    public UtilityBean<String> clientValidation(String requestBody, String... requestParams) throws ClientException {
        long startTime = System.currentTimeMillis();
        try {
            String authKey = requestParams[0];
            clientValidationUtils.authKeyValidation(authKey);

            String searchTerm = requestParams.length > 1 ? requestParams[1] : null;
            if (searchTerm != null) {
                searchTerm = searchTerm.trim();
                if (searchTerm.isEmpty()) {
                    log.warn("Search term is empty after trimming. Defaulting to empty string.");
                    searchTerm = "";
                }
            } else {
                log.warn("Search term is null in requestParams. Defaulting to empty string.");
                searchTerm = "";
            }

            HashMap<String, String> requestParamsMap = CommonUtils.buildRequestParams(authKey, null);
            requestParamsMap.put(Constants.SEARCH_TERM_KEY, searchTerm);

            return UtilityBean.<String>builder()
                    .requestParams(requestParamsMap)
                    .build();
        } finally {
            long endTime = System.currentTimeMillis();
            log.info("clientValidation(requestParams: {}) completed in {} ms", Arrays.toString(requestParams), (endTime - startTime));
        }
    }

    /**
     * Performs server-side validation by extracting and validating user access context.
     * The Pageable is expected to be present in the incoming utilityBean's dedicated field.
     * SearchTerm is now expected in the requestParams map.
     *
     * @param utilityBean The UtilityBean generated during client validation (and enriched by controller),
     *                    containing request parameters, metadata, and now pageable field.
     * @return A UtilityBean enriched with the user's access context, user ID metadata, and copied pageable.
     * @throws ServerException If the user ID is invalid, or if the access details cannot be fetched or parsed.
     */
    @Override
    public UtilityBean<AccessDetailsBean> serverValidation(UtilityBean<String> utilityBean) throws ServerException {
        long startTime = System.currentTimeMillis();
        String authKey = utilityBean.getRequestParams().get(Constants.AUTH_KEY);
        String userId = null;
        try {
            userId = serverValidationUtils.authKeyValidation(authKey);
            UserAccessBean accessDetails = userDao.fetchUserAccessDetailsUsingIdentifier(userId);
            AccessDetailsBean accessDetailsBean;
            try {
                accessDetailsBean = objectMapper.readValue(accessDetails.getAccessDetails(), AccessDetailsBean.class);
            } catch (Exception e) {
                log.error("Error occurred while mapping userAccessDetails Json to java object of AccessDetailsBean class");
                throw new ServerException(UIMessages.USER_ACCESS_DETAILS_NOT_FOUND);
            }
            if (accessDetailsBean == null) {
                log.error(UIMessages.INVALID_USER_ACCESS_DETAILS);
                throw new ServerException(UIMessages.INVALID_USER_ACCESS_DETAILS);
            }
            return UtilityBean.<AccessDetailsBean>builder()
                    .pojoObject(accessDetailsBean)
                    .metadata(Map.of(Constants.USER_ID_KEY, userId))
                    .pageable(utilityBean.getPageable())
                    .requestParams(utilityBean.getRequestParams())
                    .build();
        } finally {
            long endTime = System.currentTimeMillis();
            log.info("serverValidation(authKey: {}, userId: {}) completed in {} ms", authKey, userId, (endTime - startTime));
        }

    }

    /**
     * Processes the business logic to retrieve paginated and filtered accounts.
     *
     * <p>This method:
     * <ul>
     * <li>Fetches all accessible accounts based on user access details.</li>
     * <li>Filters accounts based on the user's access permissions.</li>
     * <li>Retrieves timezone and anomaly configuration for each account.</li>
     * <li>Maps account data to Account objects with necessary transformations.</li>
     * <li>Applies filtering, sorting, and pagination to the final list of accounts using a common utility.</li>
     * </ul>
     *
     * @param utilityBean The UtilityBean containing access details, Pageable, and searchTerm in requestParams.
     * @return A Page object containing the processed list of Account objects.
     * @throws DataProcessingException If any error occurs during data processing or fetching accounts.
     */
    @Override
    public Page<Account> process(UtilityBean<AccessDetailsBean> utilityBean) throws DataProcessingException {
        long startTime = System.currentTimeMillis();
        AccessDetailsBean bean = utilityBean.getPojoObject();
        String userId = (String) utilityBean.getMetadata().get(Constants.USER_ID_KEY);
        Pageable pageable = utilityBean.getPageable();
        String searchTerm = utilityBean.getRequestParams().get(Constants.SEARCH_TERM_KEY);
        if (pageable == null) {
            log.warn("Pageable object is null in UtilityBean. Defaulting to PageRequest.of(0, 10). This usually means it was not provided in the request or set correctly.");
            pageable = org.springframework.data.domain.PageRequest.of(0, 10);
        }
        if (searchTerm == null) {
            log.warn("Search term is null in UtilityBean requestParams. Defaulting to empty string.");
            searchTerm = "";
        }
        log.info("Processing accounts with Pageable: {} and SearchTerm: '{}'", pageable, searchTerm);
        try {
            log.info("Starting process method for user: {} with searchTerm: '{}' and pageable: {}", userId, searchTerm, pageable);
            log.debug("Fetching accounts from DB with searchTerm: '{}' page: {} size: {}", searchTerm, pageable.getPageNumber(), pageable.getPageSize());
            List<AccountBean> accessibleAccounts;
            int total;
            try {
                accessibleAccounts = accountDao.getAccounts(searchTerm, pageable.getPageNumber(), pageable.getPageSize());
                total = accountDao.countAccounts(searchTerm);
            } catch (ControlCenterException e) {
                log.error("Error fetching accounts from DB (userId: {}, searchTerm: '{}', pageable: {})", userId, searchTerm, pageable, e);
                throw new DataProcessingException(e.getMessage());
            }
            log.debug("Fetched {} accounts from DB. Total count: {}", accessibleAccounts.size(), total);
            log.info("Accessible accounts identifiers: {}", accessibleAccounts.stream().map(AccountBean::getIdentifier).limit(5).toList());
            // If user has restricted access, filter by identifiers in memory
            if (!bean.getAccounts().contains("*")) {
                log.debug("Filtering accounts in-memory for user-restricted access");
                accessibleAccounts = accessibleAccounts.stream()
                        .filter(acc -> bean.getAccounts().contains(acc.getIdentifier()))
                        .toList();
                total = accessibleAccounts.size();
                log.debug("After filtering, {} accounts remain for user.", total);
                log.info("Filtered accounts identifiers: {}", accessibleAccounts.stream().map(AccountBean::getIdentifier).limit(5).toList());
            }
            // Sorting in-memory if requested (Spring Data's Pageable sort)
            if (pageable.getSort().isSorted() && !accessibleAccounts.isEmpty()) {
                log.debug("Applying in-memory sorting as per pageable sort: {}", pageable.getSort());
                Comparator<AccountBean> comparator = getAccountBeanComparator(pageable);
                if (comparator != null) {
                    accessibleAccounts = accessibleAccounts.stream().sorted(comparator).toList();
                    log.debug("Sorting complete. First account after sort: {}", accessibleAccounts.isEmpty() ? "none" : accessibleAccounts.get(0).getIdentifier());
                }
            }
            List<Account> accountsList = new ArrayList<>();
            for (AccountBean accBean : accessibleAccounts) {
                log.debug("Processing account bean with id: {} identifier: {}", accBean.getId(), accBean.getIdentifier());
                TimezoneBean timezoneBean;
                try {
                    timezoneBean = accountDao.getAccountTimezoneDetails(accBean.getId());
                } catch (ControlCenterException e) {
                    log.warn("Could not fetch timezone for account ID {} (userId: {}, searchTerm: '{}', pageable: {}). Skipping this account for now.", accBean.getId(), userId, searchTerm, pageable, e);
                    continue;
                }
                if (timezoneBean == null) {
                    log.warn("Timezone details are null for account ID {} (userId: {}, searchTerm: '{}', pageable: {}). Skipping this account for now.", accBean.getId(), userId, searchTerm, pageable);
                    continue;
                }

                AccountAnomalyConfigurationBean config = null;
                try {
                    config = accountDao.getAccountAnomalyConfiguration(accBean.getId());
                } catch (ControlCenterException e) {
                    log.warn("Could not fetch anomaly configuration for account ID {} (userId: {}, searchTerm: '{}', pageable: {}). Continuing with defaults.", accBean.getId(), userId, searchTerm, pageable, e);
                }

                List<Tags> tagsList;
                try {
                    tagsList = tagsDao.getTagsByObjectId(accBean.getId(), "account")
                            .stream()
                            .map(tag -> new Tags(tag.getType(), tag.getKey()))
                            .collect(Collectors.toList());
                } catch (ControlCenterException e) {
                    log.warn("Could not fetch tags for account ID {} (userId: {}, searchTerm: '{}', pageable: {}). Continuing with empty tags.", accBean.getId(), userId, searchTerm, pageable, e);
                    tagsList = List.of();
                }

                ThresholdSeverity thresholdSeverity = new ThresholdSeverity(
                        config != null && config.isLowEnable(),
                        config != null && config.isMediumEnable(),
                        config != null && config.isHighEnable()
                );

                Account account = Account.builder()
                        .accountId(accBean.getId())
                        .accountName(accBean.getName())
                        .identifier(accBean.getIdentifier())
                        .privateKey(accBean.getPrivateKey())
                        .publicKey(accBean.getPublicKey())
                        .updatedTime(accBean.getUpdatedTime() != null
                                ? Timestamp.valueOf(accBean.getUpdatedTime()).getTime()
                                : null)
                        .timezoneMilli(timezoneBean.getOffset())
                        .timeZoneString(timezoneBean.getTimeZoneId())
                        .status(accBean.getStatus())
                        .dateFormat("YYYY-MM-DD")
                        .timeFormat("HH:mm")
                        .tags(tagsList)
                        .closingWindow(config != null ? config.getClosingWindow() : 0)
                        .maxDataBreaks(config != null ? config.getMaxDataBreaks() : 0)
                        .thresholdSeverity(thresholdSeverity)
                        .build();

                account.getUserNameFromIdentifier(accBean.getLastModifiedBy(), keyCloakAuthService);
                accountsList.add(account);
                log.info("Account processed and added to list: {}", account);
            }

            log.info("Total accounts processed: {}", accountsList.size());

            int pageSize = pageable.getPageSize();
            int fromIndex = Math.min(pageable.getPageNumber() * pageSize, accountsList.size());
            int toIndex = Math.min(fromIndex + pageSize, accountsList.size());
            List<Account> paginatedAccounts = accountsList.subList(fromIndex, toIndex);

            log.info("Returning {} accounts to client (userId: {}, searchTerm: '{}', pageable: {})", paginatedAccounts.size(), userId, searchTerm, pageable);
            return new PageImpl<>(paginatedAccounts, pageable, accountsList.size());
        } catch (Exception e) {
            String errorMessage = String.format("Unexpected error in process method (userId: %s, searchTerm: '%s', pageable: %s)", userId, searchTerm, pageable);
            log.error(errorMessage, e);
            throw new DataProcessingException(e.getMessage());
        } finally {
            long endTime = System.currentTimeMillis();
            log.info("process(utilityBean) completed in {} ms (userId: {}, searchTerm: '{}', pageable: {})", (endTime - startTime), userId, searchTerm, pageable);
        }
    }

    @Nullable
    private static Comparator<AccountBean> getAccountBeanComparator(Pageable pageable) {
        Comparator<AccountBean> comparator = null;
        for (org.springframework.data.domain.Sort.Order order : pageable.getSort()) {
            Comparator<AccountBean> currentComparator = null;
            if (order.getProperty().equalsIgnoreCase("lastModifiedBy")) {
                currentComparator = Comparator.comparing(
                        AccountBean::getLastModifiedBy,
                        Comparator.naturalOrder()
                );
            } else if (order.getProperty().equalsIgnoreCase("updatedTime")) {
                currentComparator = Comparator.comparing(
                        acc -> acc.getUpdatedTime() != null ? acc.getUpdatedTime() : "",
                        Comparator.naturalOrder()
                );
            }
            if (currentComparator != null) {
                if (!order.isAscending()) {
                    currentComparator = currentComparator.reversed();
                }
                comparator = comparator == null ? currentComparator : comparator.thenComparing(currentComparator);
            }
        }
        return comparator;
    }
}