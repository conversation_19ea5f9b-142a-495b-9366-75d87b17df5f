package com.heal.controlcenter.businesslogic;

import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.dao.mysql.MasterDataDao;
import com.heal.controlcenter.dao.mysql.NotificationsDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.SMSDetailsPojo;
import com.heal.controlcenter.pojo.SMSParameterPojo;
import com.heal.controlcenter.util.CommonUtils;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.DateTimeUtil;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.test.util.ReflectionTestUtils;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@PrepareForTest({PostSMSConfigurationsBL.class})
class PostSMSConfigurationsBLTest {

    @InjectMocks
    PostSMSConfigurationsBL postSMSConfigurationsBL;

    @Mock
    CommonUtils commonUtils;
    @Mock
    AccountsDao accountsDao;
    @Mock
    NotificationsDao notificationsDao;
    @Mock
    MasterDataDao masterDataDao;
    @Mock
    DateTimeUtil dateTimeUtil;

    String[] requestParams = new String[2];
    UtilityBean<SMSDetailsPojo> mockUtilityBean = null;
    SMSDetailsPojo smsDetailsPojo = new SMSDetailsPojo();
    List<SMSParameterPojo> smsParameterPojoList = new ArrayList<>();
    SMSParameterPojo smsParameterPojo = new SMSParameterPojo();

    ViewTypesBean protocolTypes = new ViewTypesBean();
    ViewTypesBean httpMethodTypes = new ViewTypesBean();
    List<ViewTypesBean> parameterTypes = new ArrayList<>();
    List<ViewTypesBean> placeholdersTypes = new ArrayList<>();
    SMSDetailsBean smsDetailsBean = new SMSDetailsBean();
    AccountBean accountBean = new AccountBean();

    @BeforeEach
    void setUp() throws ControlCenterException {
        requestParams[0] = "mockAuthToken";
        requestParams[1] = "mockAccountIdentifier";

        smsDetailsPojo.setAddress("www.healsoftware.ai");
        smsDetailsPojo.setPort(8996);
        smsDetailsPojo.setCountryCode("91");
        smsDetailsPojo.setProtocolName("HTTP");
        smsDetailsPojo.setHttpMethod("GET");
        smsDetailsPojo.setHttpRelativeUrl("/home/<USER>");
        smsDetailsPojo.setIsMultiRequest(0);

        smsParameterPojo.setParameterName("Phone number");
        smsParameterPojo.setParameterType("QueryParameter");
        smsParameterPojo.setParameterValue("{MobileNumber}");
        smsParameterPojo.setAction("ADD");
        smsParameterPojo.setIsPlaceholder(Boolean.TRUE);
        smsParameterPojoList.add(smsParameterPojo);
        smsDetailsPojo.setParameters(smsParameterPojoList);

        accountBean.setId(2);

        mockUtilityBean = UtilityBean.<SMSDetailsPojo>builder()
                .requestParams(Map.of(
                        "Authorization", "mockAuthToken",
                        "accountIdentifier", "mockAccountIdentifier"
                ))
                .metadata(Map.of(
                        "userId", "mockUserId"
                ))
                .pojoObject(smsDetailsPojo)
                .build();

        protocolTypes.setTypeId(52);
        protocolTypes.setSubTypeId(161);
        protocolTypes.setSubTypeName("HTTP");
        protocolTypes.setTypeName("SMSGatewayProtocols");

        httpMethodTypes.setTypeId(53);
        httpMethodTypes.setSubTypeId(163);
        httpMethodTypes.setSubTypeName("GET");
        httpMethodTypes.setTypeName("HTTPSMSRequestMethods");

        ViewTypesBean parameterType = new ViewTypesBean();
        parameterType.setTypeId(54);
        parameterType.setSubTypeId(165);
        parameterType.setSubTypeName("QueryParameter");
        parameterType.setTypeName("SMSParameterTypes");
        parameterTypes.add(parameterType);

        ViewTypesBean placeholderType = new ViewTypesBean();
        placeholderType.setTypeId(57);
        placeholderType.setSubTypeId(168);
        placeholderType.setSubTypeName("{MobileNumber}");
        placeholderType.setTypeName("SMSPlaceHolders");
        placeholdersTypes.add(placeholderType);

        smsDetailsBean.setId(1);
        smsDetailsBean.setAddress("www.healsoftware.ai");
        smsDetailsBean.setPort(8996);
        smsDetailsBean.setCountryCode("91");
        smsDetailsBean.setProtocolId(161);
        smsDetailsBean.setHttpMethod("GET");
        smsDetailsBean.setHttpRelativeUrl("/home/<USER>");
        smsDetailsBean.setIsMultiRequest(0);

        lenient().when(masterDataDao.getViewTypesFromMstTypeAndSubTypeName(Constants.SMS_PROTOCOLS, mockUtilityBean.getPojoObject().getProtocolName())).thenReturn(protocolTypes);
        lenient().when(masterDataDao.getViewTypesFromMstTypeAndSubTypeName(Constants.SMS_HTTP_METHODS, mockUtilityBean.getPojoObject().getHttpMethod())).thenReturn(httpMethodTypes);
        lenient().when(masterDataDao.getMstTypeByTypeName(Constants.SMS_PARAMETER_TYPE_NAME)).thenReturn(parameterTypes);
        lenient().when(masterDataDao.getMstTypeByTypeName(Constants.SMS_PLACEHOLDERS)).thenReturn(placeholdersTypes);

        ReflectionTestUtils.setField(postSMSConfigurationsBL, "protocolTypes", protocolTypes);
        ReflectionTestUtils.setField(postSMSConfigurationsBL, "parameterTypes", parameterTypes);
    }

    @AfterEach
    void tearDown() {
        requestParams = null;
        mockUtilityBean = null;
        smsDetailsPojo = null;
        smsParameterPojoList = null;
        smsParameterPojo = null;
        smsDetailsBean = null;
    }

    @Test
    void clientValidations_EmptyAuthorizationToken() {
        requestParams[0] = "";
        requestParams[1] = "mockAccountIdentifier";

        String expectedMessage = "ClientException : Invalid authorization token";
        ClientException requestException = assertThrows(ClientException.class, () ->
                postSMSConfigurationsBL.clientValidation(smsDetailsPojo, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidations_EmptyAccountIdentifier() {
        requestParams[0] = "mockAuthToken";
        requestParams[1] = "";

        String expectedMessage = "ClientException : Invalid account identifier";
        ClientException requestException = assertThrows(ClientException.class, () ->
                postSMSConfigurationsBL.clientValidation(smsDetailsPojo, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidations_ErrorWhileFetchingUserId() throws Exception {
        String expectedMessage = "ClientException : Error occurred while fetching UserId.";
        when(commonUtils.getUserId("mockAuthToken")).thenThrow(ControlCenterException.class);
        ClientException requestException = assertThrows(ClientException.class, () ->
                postSMSConfigurationsBL.clientValidation(smsDetailsPojo, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidations_InvalidRequestBody() throws Exception {
        String expectedMessage = "ClientException : {SMS address=Address is either NULL, empty or its length is greater than 128 characters.}";
        smsDetailsPojo.setAddress("");

        when(commonUtils.getUserId("mockAuthToken")).thenReturn(any());
        ClientException requestException = assertThrows(ClientException.class, () ->
                postSMSConfigurationsBL.clientValidation(smsDetailsPojo, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidations_Success() throws Exception {
        String userId = "mockUserId";
        when(commonUtils.getUserId("mockAuthToken")).thenReturn(userId);
        UtilityBean<SMSDetailsPojo> utilityBean = postSMSConfigurationsBL.clientValidation(smsDetailsPojo, requestParams);
        assertEquals(utilityBean.getMetadata().get("userId"), userId);
    }

    @Test
    void serverValidation_Failure_SettingsAlreadyPresent() {
        String expectedMessage = "ServerException : SMS settings already available";
        AccountBean account = new AccountBean();
        account.setId(1);

        when(accountsDao.getAccountByIdentifier("mockAccountIdentifier")).thenReturn(account);
        when(notificationsDao.getSMSDetails(account.getId())).thenReturn(smsDetailsBean);
        ServerException requestException = assertThrows(ServerException.class, () ->
                postSMSConfigurationsBL.serverValidation(mockUtilityBean));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void serverValidation_InvalidAccountIdentifier() {
        String expectedMessage = "ServerException : Invalid account identifier. Reason: It should not be null or empty.";
        when(accountsDao.getAccountByIdentifier("mockAccountIdentifier")).thenReturn(any());
        ServerException requestException = assertThrows(ServerException.class, () ->
                postSMSConfigurationsBL.serverValidation(mockUtilityBean));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void serverValidation_Success() throws Exception {
        String userId = "mockUserId";

        when(accountsDao.getAccountByIdentifier("mockAccountIdentifier")).thenReturn(accountBean);
        when(notificationsDao.getSMSDetails(accountBean.getId())).thenReturn(null);
        UtilityBean<SMSDetailsPojo> utilityBean = postSMSConfigurationsBL.serverValidation(mockUtilityBean);
        assertEquals(utilityBean.getMetadata().get("userId"), userId);
    }

    @Test
    void process_Success() throws Exception {
        when(dateTimeUtil.getCurrentTimestampInGMT()).thenReturn(Timestamp.valueOf("2021-12-02 02:02:02"));
        postSMSConfigurationsBL.process(mockUtilityBean);
    }

    @Test
    void process_Failure() throws Exception {
        List<SMSParameterBean> smsParameterBeans = new ArrayList<>();
        SMSParameterBean smsParameterBean = new SMSParameterBean();
        smsParameterBean.setId(0);
        smsParameterBean.setParameterName("Phone number");
        smsParameterBean.setParameterValue("{MobileNumber}");
        smsParameterBean.setParameterTypeId(165);
        smsParameterBean.setLastModifiedBy("mockUserId");
        smsParameterBean.setIsPlaceholder(1);
        smsParameterBean.setCreatedTime("0");
        smsParameterBean.setUpdatedTime("0");
        smsParameterBean.setSmsDetailsId(0);
        smsParameterBeans.add(smsParameterBean);

        String expectedMessage = "DataProcessingException : Error occurred while adding SMS parameters to account.";
        ControlCenterException controlCenterException = new ControlCenterException("Error occurred while adding SMS parameters to account.");
        when(dateTimeUtil.getCurrentTimestampInGMT()).thenReturn(Timestamp.valueOf("2021-12-02 02:02:02"));
        doReturn(1).when(notificationsDao).addSMSDetails(any());
        doThrow(controlCenterException).when(notificationsDao).addSMSParameter(any());
        DataProcessingException requestException = assertThrows(DataProcessingException.class, () ->
                postSMSConfigurationsBL.process(mockUtilityBean));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }
}
