package com.heal.controlcenter.dao.mysql;

import com.heal.controlcenter.beans.ForensicActionArgumentsBean;
import com.heal.controlcenter.beans.ForensicActionBean;
import com.heal.controlcenter.exception.HealControlCenterException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;

@Slf4j
@Repository
public class ActionScriptDao {

    @Autowired
    JdbcTemplate jdbcTemplate;

    public Integer getForensicCountForAccount(int accountId) throws HealControlCenterException {
        String query = "select count(distinct Actions.id)  from actions as Actions, mst_sub_type as MasterSubType "+
                "where MasterSubType.name = 'Forensic' and Actions.account_id in (1, " + accountId + ")";
        try {
            log.debug("getting forensic count.");
            return jdbcTemplate.queryForObject(query, Integer.class);
        } catch (Exception e) {
            log.error("Exception encountered while fetching forensic count from 'actions' table for accountId [{}]. Details: ", accountId, e);
            throw new HealControlCenterException("Error occurred while getting forensic count.");
        }
    }

    public List<ForensicActionBean> getForensicActions(Integer accountId) throws HealControlCenterException {
        String query = "SELECT Actions.id actionId, Actions.name actionName, MasterSubType.name actionType, CmdDetails.name commandName, " +
                "CmdDetails.identifier commandIdentifier, CmdDetails.id commandId, CmdDetails.timeout_in_secs commandTimeoutInSeconds, " +
                "ACM.ttl_in_secs supCtrlTimeoutInSeconds, ACM.retries supCtrlRetryCount, Actions.status status, UA.username lastModifiedBy, " +
                "Actions.updated_time lastModifiedOn, MasterCategoryDetails.id categoryId, MasterCategoryDetails.name categoryName, " +
                "MasterCategoryDetails.identifier categoryIdentifier, MasterCategoryDetails.is_custom isCustomCategory " +
                "FROM actions Actions, mst_sub_type MasterSubType, command_details CmdDetails, " +
                "mst_category_details MasterCategoryDetails, action_category_mapping ACM, user_attributes UA " +
                "WHERE Actions.action_type_id = 289 and Actions.account_id in (1, ?) " +
                "and Actions.standard_type_id = MasterSubType.id and Actions.id = ACM.action_id " +
                "and ACM.object_id = CmdDetails.id and ACM.category_id = MasterCategoryDetails.id " +
                "and UA.user_identifier = Actions.user_details_id GROUP BY ACM.action_id ORDER BY Actions.id asc";
        try {
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(ForensicActionBean.class), accountId);
        } catch (Exception e) {
            log.error("Exception encountered while fetching forensic actions for accountId [{}]. Details: ", accountId, e);
            throw new HealControlCenterException("Error occurred while getting forensic actions.");
        }
    }

    public List<ForensicActionArgumentsBean> getForensicsParameters() throws HealControlCenterException {
        String query = "SELECT distinct CA.id id, CA.argument_key, CA.argument_value value, CA.default_value defaultValue, MST.name valueType, CA.command_id commandId, " +
                "(SELECT name from mst_sub_type where id = CA.argument_type_id) type " +
                "FROM command_arguments CA, mst_sub_type MST " +
                "WHERE CA.argument_value_type_id = MST.id";
        try {
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(ForensicActionArgumentsBean.class));
        } catch (Exception e) {
            log.error("Exception encountered while fetching command arguments. Details: ", e);
            throw new HealControlCenterException("Error occurred while fetching command arguments.");
        }
    }
}
