package com.heal.controlcenter.businesslogic;

import com.heal.controlcenter.beans.AccountBean;
import com.heal.controlcenter.beans.NotificationSettingsBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.beans.ViewTypesBean;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.dao.mysql.MasterDataDao;
import com.heal.controlcenter.dao.mysql.NotificationsDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.NotificationSettings;
import com.heal.controlcenter.pojo.UserAccountPojo;
import com.heal.controlcenter.util.CommonUtils;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.DateTimeUtil;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.beans.factory.annotation.Autowired;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@PrepareForTest({GetNotificationSettingsBL.class})
class GetNotificationSettingsBLTest {

    @Autowired
    @InjectMocks
    GetNotificationSettingsBL getNotificationSettingsBL;

    @Mock
    CommonUtils commonUtils;
    @Mock
    AccountsDao accountsDao;
    @Mock
    NotificationsDao notificationsDao;
    @Mock
    MasterDataDao masterDataDao;
    @Mock
    DateTimeUtil dateTimeUtil;

    String[] requestParams = new String[3];
    UtilityBean<Object> mockUtilityBean = null;
    List<NotificationSettingsBean> notificationSettings = new ArrayList<>();
    List<NotificationSettingsBean> defaultNotificationSettings = new ArrayList<>();
    UserAccountPojo userAccountPojo = new UserAccountPojo();
    ViewTypesBean notificationLongSubType =new ViewTypesBean();
    ViewTypesBean notificationTooLongSubType =new ViewTypesBean();

    @BeforeEach
    void setUp() {
        requestParams[0] = "mockAuthToken";
        requestParams[1] = "mockAccountIdentifier";

        mockUtilityBean = UtilityBean.builder()
                .requestParams(Map.of(
                        "Authorization", "mockAuthToken",
                        "accountIdentifier", "mockAccountIdentifier"
                ))
                .metadata(Map.of(
                        "userId", "mockUserId"
                ))
                .build();

        NotificationSettingsBean notificationSettingsBean = new NotificationSettingsBean();
        notificationSettingsBean.setTypeId(292);
        notificationSettingsBean.setDurationInMin(15);
        notificationSettingsBean.setAccountId(2);
        notificationSettingsBean.setProperties(new HashMap<>());
        notificationSettings.add(notificationSettingsBean);
        notificationSettingsBean = new NotificationSettingsBean();
        notificationSettingsBean.setTypeId(293);
        notificationSettingsBean.setDurationInMin(30);
        notificationSettingsBean.setAccountId(2);
        notificationSettingsBean.setProperties(new HashMap<>());
        notificationSettings.add(notificationSettingsBean);

        userAccountPojo.setUserId("mockUserId");
        AccountBean accountBean = new AccountBean();
        accountBean.setId(2);
        userAccountPojo.setAccount(accountBean);

        notificationLongSubType.setSubTypeId(292);
        notificationLongSubType.setTypeId(102);
        notificationLongSubType.setTypeName("NotificationType");
        notificationLongSubType.setSubTypeName("Open for long");

        notificationTooLongSubType.setSubTypeId(293);
        notificationTooLongSubType.setTypeId(102);
        notificationTooLongSubType.setTypeName("NotificationType");
        notificationTooLongSubType.setSubTypeName("Open for too long");

        NotificationSettingsBean defaultNotificationSettingsBean = new NotificationSettingsBean();
        defaultNotificationSettingsBean.setTypeId(292);
        defaultNotificationSettingsBean.setDurationInMin(120);
        defaultNotificationSettingsBean.setAccountId(1);
        defaultNotificationSettingsBean.setProperties(new HashMap<>());
        defaultNotificationSettings.add(defaultNotificationSettingsBean);
        defaultNotificationSettingsBean = new NotificationSettingsBean();
        defaultNotificationSettingsBean.setTypeId(293);
        defaultNotificationSettingsBean.setDurationInMin(240);
        defaultNotificationSettingsBean.setAccountId(1);
        defaultNotificationSettingsBean.setProperties(new HashMap<>());
        defaultNotificationSettings.add(defaultNotificationSettingsBean);
    }

    @AfterEach
    void tearDown() {
        requestParams = null;
        notificationSettings = null;
        defaultNotificationSettings = null;
    }

    @Test
    void clientValidations_EmptyAuthorizationToken() {
        requestParams[0] = "";
        requestParams[1] = "mockAccountIdentifier";

        String expectedMessage = "ClientException : Invalid authorization token";
        ClientException requestException = assertThrows(ClientException.class, () ->
                getNotificationSettingsBL.clientValidation(null, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidations_EmptyAccountIdentifier() {
        requestParams[0] = "mockAuthToken";
        requestParams[1] = "";

        String expectedMessage = "ClientException : Invalid account identifier";
        ClientException requestException = assertThrows(ClientException.class, () ->
                getNotificationSettingsBL.clientValidation(null, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidations_ErrorWhileFetchingUserId() throws Exception {
        String expectedMessage = "ClientException : Error occurred while fetching userId from authorization token";
        when(commonUtils.getUserId("mockAuthToken")).thenThrow(ControlCenterException.class);
        ClientException requestException = assertThrows(ClientException.class, () ->
                getNotificationSettingsBL.clientValidation(null, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidations_AuthKeyInvalid() throws Exception {
        String expectedMessage = "ClientException : Error occurred while fetching userId from authorization token";
        when(commonUtils.getUserId("mockAuthToken")).thenReturn(any());
        ClientException requestException = assertThrows(ClientException.class, () ->
                getNotificationSettingsBL.clientValidation(null, requestParams));
        System.out.println(requestException.getMessage());
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidations_Success() throws Exception {
        String userId = "mockUserId";
        when(commonUtils.getUserId("mockAuthToken")).thenReturn(userId);
        UtilityBean<Object> utilityBean = getNotificationSettingsBL.clientValidation(null, requestParams);
        assertEquals(utilityBean.getMetadata().get("userId"), userId);
    }

    @Test
    void serverValidation_Success() throws Exception {
        AccountBean account = new AccountBean();
        account.setId(1);

        when(accountsDao.getAccountByIdentifier("mockAccountIdentifier")).thenReturn(account);
        UserAccountPojo userAccountPojo = getNotificationSettingsBL.serverValidation(mockUtilityBean);
        assertEquals(userAccountPojo.getAccount().getId(), account.getId());
    }

    @Test
    void serverValidation_InvalidAccountIdentifier() {
        String expectedMessage = "ServerException : Account with identifier [mockAccountIdentifier] is unavailable";
        when(accountsDao.getAccountByIdentifier("mockAccountIdentifier")).thenReturn(any());
        ServerException requestException = assertThrows(ServerException.class, () ->
                getNotificationSettingsBL.serverValidation(mockUtilityBean));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void process_Success_NotificationSettingsPresent() throws Exception {
        when(notificationsDao.getNotificationSetting(userAccountPojo.getAccount().getId())).thenReturn(notificationSettings);
        when(masterDataDao.getMstSubTypeBySubTypeId(notificationSettings.get(0).getTypeId())).thenReturn(notificationLongSubType);
        when(masterDataDao.getMstSubTypeBySubTypeId(notificationSettings.get(1).getTypeId())).thenReturn(notificationTooLongSubType);

        List<NotificationSettings> data = getNotificationSettingsBL.process(userAccountPojo);
        assertEquals(data.size(), 2);
    }

    @Test
    void process_Success_NotificationSettingsNotPresent() throws Exception {
        int[] response = new int[2];
        response[0] = 1;
        response[1] = 2;

        when(dateTimeUtil.getDateInGMT(anyLong())).thenReturn(Timestamp.valueOf("2021-12-02 02:02:02"));
        lenient().when(notificationsDao.getNotificationSetting(userAccountPojo.getAccount().getId())).thenReturn(new ArrayList<>(), notificationSettings);
        lenient().when(notificationsDao.getNotificationSetting(1)).thenReturn(defaultNotificationSettings);
        lenient().when(masterDataDao.getMstSubTypeBySubTypeId(notificationSettings.get(0).getTypeId())).thenReturn(notificationLongSubType);
        lenient().when(masterDataDao.getMstSubTypeBySubTypeId(notificationSettings.get(0).getTypeId())).thenReturn(notificationLongSubType);
        lenient().when(masterDataDao.getMstSubTypeBySubTypeId(notificationSettings.get(1).getTypeId())).thenReturn(notificationTooLongSubType);
        when(masterDataDao.getViewTypesFromMstTypeAndSubTypeName(Constants.NOTIFICATION_TYPE_LITERAL, Constants.LONG)).thenReturn(notificationLongSubType);
        when(masterDataDao.getViewTypesFromMstTypeAndSubTypeName(Constants.NOTIFICATION_TYPE_LITERAL, Constants.TOO_LONG)).thenReturn(notificationTooLongSubType);
        when(notificationsDao.addNotificationSettings(anyList())).thenReturn(response);

        List<NotificationSettings> data = getNotificationSettingsBL.process(userAccountPojo);
        assertEquals(data.size(), 2);
    }
}
