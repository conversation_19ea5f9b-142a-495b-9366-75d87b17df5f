package com.heal.controlcenter.dao.mysql;


import com.heal.controlcenter.beans.ServiceSuppPersistenceConfigurationBean;
import com.heal.controlcenter.exception.HealControlCenterException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.List;

@Slf4j
@Repository
public class ServiceConfigurationDao {

    @Autowired
    JdbcTemplate jdbcTemplate;

    public void updateServiceAnomalyPsConfig(List<ServiceSuppPersistenceConfigurationBean> serviceConfigList) throws HealControlCenterException {
        String query = "UPDATE service_anomaly_ps_configurations SET user_details_id = ?, " +
                "low_suppression = ?, low_persistence = ?, " +
                "medium_suppression = ?, medium_persistence = ?, " +
                "high_suppression = ?, high_persistence = ?, updated_time = ?, " +
                "start_collection_interval = ?, end_collection_interval = ? " +
                "WHERE account_id = ? AND service_id = ? AND id = ?";
        try {
            log.info("Bulk updating service level persistence and suppression configs.");

            jdbcTemplate.batchUpdate(query, new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps, int i) throws SQLException {
                    ServiceSuppPersistenceConfigurationBean configBean = serviceConfigList.get(i);
                    ps.setString(1, configBean.getUserDetailsId());
                    ps.setInt(2, configBean.getLowSuppression());
                    ps.setInt(3, configBean.getLowPersistence());
                    ps.setInt(4, configBean.getMediumSuppression());
                    ps.setInt(5, configBean.getMediumPersistence());
                    ps.setInt(6, configBean.getHighSuppression());
                    ps.setInt(7, configBean.getHighPersistence());
                    ps.setTimestamp(8, Timestamp.valueOf(configBean.getUpdatedTime()));
                    ps.setInt(9, configBean.getStartCollectionInterval());
                    ps.setInt(10, configBean.getEndCollectionInterval());
                    ps.setInt(11, configBean.getAccountId());
                    ps.setInt(12, configBean.getServiceId());
                    ps.setInt(13, configBean.getId());
                }

                @Override
                public int getBatchSize() {
                    return serviceConfigList.size();
                }
            });

        } catch (Exception e) {
            log.error("Exception encountered while bulk updating service anomaly PS configurations. Details: ", e);
            throw new HealControlCenterException("Error occurred while bulk updating service anomaly PS configurations.");
        }
    }

    public void updateServiceConfigFlags(List<ServiceSuppPersistenceConfigurationBean> serviceConfigList) throws HealControlCenterException {
        String query = "UPDATE service_configurations SET " +
                "low_enable = ?, medium_enable = ?, high_enable = ?, " +
                "closing_window = ?, max_data_breaks = ?, user_details_id = ?, updated_time = ?" +
                "WHERE account_id = ? AND service_id = ?";
        try {
            log.info("Bulk updating service configuration flags.");

            jdbcTemplate.batchUpdate(query, new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps, int i) throws SQLException {
                    ServiceSuppPersistenceConfigurationBean configBean = serviceConfigList.get(i);
                    ps.setBoolean(1, configBean.isLowEnable());
                    ps.setBoolean(2, configBean.isMediumEnable());
                    ps.setBoolean(3, configBean.isHighEnable());
                    ps.setInt(4, configBean.getClosingWindow());
                    ps.setInt(5, configBean.getMaxDataBreaks());
                    ps.setString(6, configBean.getUserDetailsId());
                    ps.setTimestamp(8, Timestamp.valueOf(configBean.getUpdatedTime()));
                    ps.setInt(8, configBean.getAccountId());
                    ps.setInt(9, configBean.getServiceId());
                }

                @Override
                public int getBatchSize() {
                    return serviceConfigList.size();
                }
            });

        } catch (Exception e) {
            log.error("Exception encountered while bulk updating service configuration flags. Details: ", e);
            throw new HealControlCenterException("Error occurred while bulk updating service configuration flags.");
        }
    }

    public List<ServiceSuppPersistenceConfigurationBean> getServiceConfiguration(int accountId, int serviceId) throws HealControlCenterException {
        String query = "SELECT ps.id, ps.account_id, ps.service_id, ps.user_details_id, ps.created_time, ps.updated_time, " +
                "ps.start_collection_interval, ps.end_collection_interval, " +
                "sc.low_enable, ps.low_persistence, ps.low_suppression, " +
                "sc.medium_enable, ps.medium_persistence, ps.medium_suppression, " +
                "sc.high_enable, ps.high_persistence, ps.high_suppression, " +
                "sc.closing_window, sc.max_data_breaks " +
                "FROM service_anomaly_ps_configurations ps " +
                "JOIN service_configurations sc ON ps.service_id = sc.service_id AND ps.account_id = sc.account_id " +
                "WHERE ps.account_id = ? AND ps.service_id = ?";

        try {
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(ServiceSuppPersistenceConfigurationBean.class), accountId, serviceId);
        } catch (Exception e) {
            log.error("Error occurred while fetching service configuration from 'service_anomaly_ps_configurations' table for accountId [{}] and serviceId [{}]. Details: ", accountId, serviceId, e);
            throw new HealControlCenterException("Error occurred while fetching service configuration.");
        }
    }

    public int[] addServiceConfiguration(List<ServiceSuppPersistenceConfigurationBean> serviceDetails) throws HealControlCenterException {
        String sql = "INSERT INTO service_anomaly_configurations (" +
                "service_id, account_id, user_details_id, created_time, updated_time, " +
                "start_collection_interval, end_collection_interval, low_persistence, low_suppression, " +
                "medium_persistence, medium_suppression, high_persistence, high_suppression, low_enable, medium_enable, high_enable, closing_window, max_data_breaks) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        try {
            return jdbcTemplate.batchUpdate(sql, new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps, int i) throws SQLException {
                    ServiceSuppPersistenceConfigurationBean bean = serviceDetails.get(i);
                    ps.setLong(1, bean.getServiceId());
                    ps.setLong(2, bean.getAccountId());
                    ps.setString(3, bean.getUserDetailsId());
                    ps.setTimestamp(4, Timestamp.valueOf(bean.getCreatedTime()));
                    ps.setTimestamp(5, Timestamp.valueOf(bean.getUpdatedTime()));
                    ps.setInt(6, bean.getStartCollectionInterval());
                    ps.setInt(7, bean.getEndCollectionInterval());
                    ps.setInt(8, bean.getLowPersistence());
                    ps.setInt(9, bean.getLowSuppression());
                    ps.setInt(10, bean.getMediumPersistence());
                    ps.setInt(11, bean.getMediumSuppression());
                    ps.setInt(12, bean.getHighPersistence());
                    ps.setInt(13, bean.getHighSuppression());
                    ps.setBoolean(14, bean.isLowEnable());
                    ps.setBoolean(15, bean.isMediumEnable());
                    ps.setBoolean(16, bean.isHighEnable());
                    ps.setInt(17, bean.getClosingWindow());
                    ps.setInt(18, bean.getMaxDataBreaks());
                }

                @Override
                public int getBatchSize() {
                    return serviceDetails.size();
                }
            });
        } catch (Exception e) {
            log.error("Error occurred while adding service configuration. Details: ", e);
            throw new HealControlCenterException("Error occurred while adding service configuration.");
        }
    }
}
