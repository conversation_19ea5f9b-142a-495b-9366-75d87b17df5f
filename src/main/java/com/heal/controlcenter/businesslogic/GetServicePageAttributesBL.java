package com.heal.controlcenter.businesslogic;

import com.heal.controlcenter.beans.AccountBean;
import com.heal.controlcenter.beans.ControllerBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.dao.mysql.ControllerDao;
import com.heal.controlcenter.enums.AttributeSelectionType;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.ServicePageAttributePojo;
import com.heal.controlcenter.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class GetServicePageAttributesBL implements BusinessLogic<Integer, Integer, List<ServicePageAttributePojo>> {

    @Autowired
    CommonUtils commonUtils;
    @Autowired
    AccountsDao accountDao;
    @Autowired
    ControllerDao controllerDao;
    @Autowired
    ClientValidationUtils clientValidationUtils;
    @Autowired
    ServerValidationUtils serverValidationUtils;

    @Override
    public UtilityBean<Integer> clientValidation(Integer requestBody, String... requestParams) throws ClientException {
        String authKey = requestParams[0];
        clientValidationUtils.authKeyValidation(authKey);

        String accountIdentifier = requestParams[1];
        clientValidationUtils.accountIdentifierValidation(accountIdentifier);

        if (requestParams[2].isEmpty()) {
            log.error("Invalid serviceId. Reason: It is either null or empty.");
            throw new ClientException("Invalid serviceId");
        }

        int serviceId;
        try {
            serviceId = Integer.parseInt(requestParams[2]);
        } catch (NumberFormatException e) {
            log.error("Invalid serviceId [{}]. Reason: It is not a valid integer.", requestParams[2]);
            throw new ClientException("Invalid serviceId");
        }

        if(serviceId <= 0) {
            log.error("Invalid serviceId [{}]. Reason: It is not a valid integer.", serviceId);
            throw new ClientException("Invalid serviceId");
        }

        HashMap<String, String> requestParamsMap = CommonUtils.buildRequestParams(authKey, accountIdentifier);

        return UtilityBean.<Integer>builder()
                .requestParams(requestParamsMap)
                .pojoObject(serviceId)
                .build();
    }

    @Override
    public Integer serverValidation(UtilityBean<Integer> utilityBean) throws ServerException {
        String authKey = utilityBean.getRequestParams().get(Constants.AUTH_KEY);
        serverValidationUtils.authKeyValidation(authKey);

        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
        AccountBean account = accountDao.getAccountByIdentifier(accountIdentifier);
        if (account == null) {
            String message = String.format(UIMessages.ACCOUNT_IDENTIFIER_UNAVAILABLE,accountIdentifier);
            log.error(message);
            throw new ServerException(message);
        }

        ControllerBean service = controllerDao.getServiceById(utilityBean.getPojoObject(), account.getId());
        if (null == service) {
            String message = String.format("ServiceId [%d] is unavailable for account [%s]", utilityBean.getPojoObject(), utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER));
            log.error(message);
            throw new ServerException(message);
        }

        return service.getId();
    }

    @Override
    public List<ServicePageAttributePojo> process(Integer serviceId) throws DataProcessingException {
        List<ServicePageAttributePojo> attributes = new ArrayList<>();

        int id = 1;
        attributes.add(new ServicePageAttributePojo(id++, null, "Name", "name", "name",
                null, null, null, new ServicePageAttributePojo.AttributeProperties(1, 128,
                null, "^[a-zA-Z0-9._-]+$", AttributeSelectionType.TextBox, 1, 0,
                0, 1, null)));

        attributes.add(new ServicePageAttributePojo(id++, null, "Identifier", "identifier", "identifier",
                null, null, null, new ServicePageAttributePojo
                .AttributeProperties(1, 128, null, "^[a-zA-Z0-9._-]+$", AttributeSelectionType.TextBox,
                0, serviceId == 0 ? 0 : 1, 0, 1, null)));

        attributes.add(new ServicePageAttributePojo(id++, null, "Application(s)", "appIdentifiers",
                "application.identifier", "name", "identifier",
                "accounts/{accountIdentifier}/applications", new ServicePageAttributePojo.AttributeProperties(0,
                0, null, "", AttributeSelectionType.Dropdown, 1, 0, 1,
                0, null)));

        attributes.add(new ServicePageAttributePojo(id++, null, "Layer", "layer", "layer",
                null, null, null, new ServicePageAttributePojo.AttributeProperties(1, 128,
                controllerDao.getLayers(Constants.SERVICES_LAYER_TYPE), "^[a-zA-Z0-9._-]+$", AttributeSelectionType.Dropdown,
                1, 0, 0, 0, null)));

        attributes.add(new ServicePageAttributePojo(id++, null, "Timezone", "timezone", "timezone",
                "timeZoneId", "timeZoneId", "/timezones", new ServicePageAttributePojo.AttributeProperties(0,
                0, null, "", AttributeSelectionType.Dropdown, 1, 0, 0,
                0, null)));

        if (serviceId == 0) {
            Map<String, String> options = new HashMap<>();
            options.put("0", "No");
            options.put("1", "Yes");
            attributes.add(new ServicePageAttributePojo(id, 0, "Mark as Entry Point", "isEntryPointService",
                    "isEntryPoint", null, null, null, new ServicePageAttributePojo.
                    AttributeProperties(0, 0, options, "", AttributeSelectionType.Switch, 0,
                    0, 0, 0, null)));
        }

        return attributes;
    }
}
