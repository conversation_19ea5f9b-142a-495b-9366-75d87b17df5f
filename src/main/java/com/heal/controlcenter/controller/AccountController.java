package com.heal.controlcenter.controller;

import com.heal.controlcenter.aop.AopCustomAnnotation;
import com.heal.controlcenter.beans.AccessDetailsBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.AddAccountBL;
import com.heal.controlcenter.businesslogic.GetAccountsBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.Account;
import com.heal.controlcenter.pojo.BasicUserDetails;
import com.heal.controlcenter.pojo.ResponsePojo;
import com.heal.controlcenter.swagger.CommonApiResponses;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.HealthMetrics;
import com.heal.controlcenter.util.JsonFileParser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * REST Controller for managing Account resources within the Control Center application.
 * This controller provides endpoints for retrieving and creating user accounts,
 * integrating with business logic components for validation and processing.
 */
@Slf4j
@RestController
public class AccountController {

    @Autowired
    GetAccountsBL getAccountsBL;
    @Autowired
    JsonFileParser headersParser;
    @Autowired
    AddAccountBL addAccountBL;
    @Autowired
    HealthMetrics healthMetrics;

    @Operation(summary = "Retrieves all active accounts for the authenticated user.",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Accounts fetched successfully.",
                            content = @Content(schema = @Schema(implementation = Account.class))
                    ),
                    @ApiResponse(
                            responseCode = "500",
                            description = "Exception encountered while fetching accounts.",
                            content = @Content(schema = @Schema(implementation = ResponsePojo.class))
                    ),
                    @ApiResponse(
                            responseCode = "400",
                            description = "Invalid request parameters or token.",
                            content = @Content(schema = @Schema(implementation = ResponsePojo.class))
                    ),
                    @ApiResponse(
                            responseCode = "401",
                            description = "Unauthorized - Invalid or missing authentication token.",
                            content = @Content(schema = @Schema(implementation = ResponsePojo.class))
                    )
            }
    )
    /**
     * Retrieves all active accounts for the authenticated user with pagination, search filtering, and sorting.
     *
     * <p>This endpoint provides a paginated view of accounts, allowing clients to
     * search by term and define sorting preferences.
     *
     * @param pageable      Pagination and sorting information (e.g., page number, page size, sort order).
     * @param searchTerm    An optional string to filter accounts by name or identifier.
     * @return {@link ResponseEntity} containing a paginated list of {@link Account} objects and a success message.
     */
    @AopCustomAnnotation
    @GetMapping(value = "/accounts")
    public ResponseEntity<ResponsePojo<Page<Account>>> getAllAccounts(
            @Parameter(
                    description = "Pagination and sorting information. " +
                            "Use `page` (0-indexed), `size` (items per page), and `sort` (property[,asc|desc]). " +
                            "Example: `?page=0&size=10&sort=accountName,asc`",
                    required = false
            )
            Pageable pageable,
            @Parameter(
                    description = "Search term to filter accounts by name or identifier.",
                    example = "TestAccount")
            @RequestParam(required = false) String searchTerm, BasicUserDetails userDetails) {
        try {
            UtilityBean<String> utilityBean = getAccountsBL.clientValidation(null, searchTerm);
            utilityBean.getMetadata().put(Constants.USER_ID_KEY, userDetails.getUserIdentifier());
            utilityBean.setPageable(pageable);
            UtilityBean<AccessDetailsBean> accessDetailsBean = getAccountsBL.serverValidation(utilityBean);
            Page<Account> data = getAccountsBL.process(accessDetailsBean);
            ResponsePojo<Page<Account>> responseBean = new ResponsePojo<>("Accounts fetched successfully.", data, HttpStatus.OK);
            return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responseBean);
        } catch (ClientException | ServerException | DataProcessingException e) {
            log.error("Error occurred while getting all accounts.", e);
            healthMetrics.updateHealApiServiceErrors();
            healthMetrics.addStatusCodes(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE, 1);
            return ResponseEntity.badRequest().body(new ResponsePojo<>(e.getMessage(), null, HttpStatus.BAD_REQUEST));
        } catch (Exception e) {
            log.error("Error occurred while GET getAllAccounts.", e);
            healthMetrics.updateHealApiServiceErrors();
            healthMetrics.addStatusCodes(Constants.INTERNAL_SERVER_ERROR_STATUS_CODE, 1);
            return ResponseEntity.internalServerError().body(new ResponsePojo<>(e.getMessage(), null, HttpStatus.INTERNAL_SERVER_ERROR));
        }
    }

    @Operation(
            summary = "Creates a new account",
            description = "Validates and creates a new account in the system, including database persistence, tag mappings, and Redis updates.",
            security = @SecurityRequirement(name = "bearerAuth"),
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Account details for creation",
                    required = true,
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = Account.class)
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Account created successfully",
                            content = @Content(mediaType = "application/json",
                                    schema = @Schema(implementation = ResponsePojo.class)))
            }
    )
    @CommonApiResponses
    /**
     * Creates a new account in the system.
     *
     * <p>This method handles the creation of a new account, performing necessary
     * validations and orchestrating the business logic for persistence and configuration.
     *
     * @param body          The {@link Account} object representing the incoming account creation payload.
     * @return A {@link ResponseEntity} containing a success message and HTTP headers.
     *
     */
    @AopCustomAnnotation
    @PostMapping(value = "/accounts")
    public ResponseEntity<ResponsePojo<Account>> createAccount(
            @Validated @RequestBody Account body, BasicUserDetails userDetails) {
        try {
            log.trace("Method Invoked: createAccount");
            UtilityBean<Account> utilityBean = addAccountBL.clientValidation(body, body.getIdentifier());
            utilityBean.getMetadata().put(Constants.USER_ID_KEY, userDetails.getUserIdentifier());
            UtilityBean<Account> updatedUtilityBean = addAccountBL.serverValidation(utilityBean);
            addAccountBL.process(updatedUtilityBean);
            ResponsePojo<Account> responseBean = new ResponsePojo<>("Account created successfully .", null, HttpStatus.OK);
            return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responseBean);
        } catch (ClientException | ServerException | DataProcessingException e) {
            log.error("Error occurred while creating account.", e);
            healthMetrics.updateHealApiServiceErrors();
            healthMetrics.addStatusCodes(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE, 1);
            return ResponseEntity.badRequest().body(new ResponsePojo<>(e.getMessage(), null, HttpStatus.BAD_REQUEST));
        } catch (Exception e) {
            log.error("Error occurred while POST createAccount.", e);
            healthMetrics.updateHealApiServiceErrors();
            healthMetrics.addStatusCodes(Constants.INTERNAL_SERVER_ERROR_STATUS_CODE, 1);
            return ResponseEntity.internalServerError().body(new ResponsePojo<>(e.getMessage(), null, HttpStatus.INTERNAL_SERVER_ERROR));
        }
    }
}