package com.heal.controlcenter.businesslogic;

import com.heal.controlcenter.beans.AccountBean;
import com.heal.controlcenter.beans.NotificationSettingsBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.beans.ViewTypesBean;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.dao.mysql.MasterDataDao;
import com.heal.controlcenter.dao.mysql.NotificationsDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.NotificationSettingsPojo;
import com.heal.controlcenter.util.CommonUtils;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.DateTimeUtil;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.util.ReflectionTestUtils;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@PrepareForTest({PutNotificationSettingsBL.class})
class PutNotificationSettingsBLTest {

    @Autowired
    @InjectMocks
    PutNotificationSettingsBL putNotificationSettingsBL;

    @Mock
    CommonUtils commonUtils;
    @Mock
    AccountsDao accountsDao;
    @Mock
    MasterDataDao masterDataDao;
    @Mock
    DateTimeUtil dateTimeUtil;
    @Mock
    NotificationsDao notificationsDao;

    String[] requestParams = new String[3];
    UtilityBean<List<NotificationSettingsPojo>> mockUtilityBean = null;
    List<NotificationSettingsPojo> notificationSettings = new ArrayList<>();
    ViewTypesBean openForLong = new ViewTypesBean();
    ViewTypesBean openForTooLong = new ViewTypesBean();
    List<NotificationSettingsBean> notificationSettingsList = new ArrayList<>();

    @BeforeEach
    void setUp() throws ControlCenterException {
        requestParams[0] = "mockAuthToken";
        requestParams[1] = "mockAccountIdentifier";

        NotificationSettingsPojo notificationSettingsPojo = new NotificationSettingsPojo();
        notificationSettingsPojo.setDurationInMin(30);
        notificationSettingsPojo.setTypeId(292);
        notificationSettingsPojo.setTypeName("Open for long");
        notificationSettings.add(notificationSettingsPojo);
        notificationSettingsPojo = new NotificationSettingsPojo();
        notificationSettingsPojo.setDurationInMin(60);
        notificationSettingsPojo.setTypeId(293);
        notificationSettingsPojo.setTypeName("Open for too long");
        notificationSettings.add(notificationSettingsPojo);

        openForLong.setSubTypeName("Open for long");
        openForLong.setTypeId(102);
        openForLong.setSubTypeId(292);
        openForLong.setTypeName("NotificationType");
        lenient().when(masterDataDao.getViewTypesFromMstTypeAndSubTypeName(Constants.NOTIFICATION_TYPE_LITERAL, Constants.LONG)).thenReturn(openForLong);
        openForTooLong.setSubTypeName("Open for too long");
        openForTooLong.setTypeId(102);
        openForTooLong.setSubTypeId(293);
        openForTooLong.setTypeName("NotificationType");
        lenient().when(masterDataDao.getViewTypesFromMstTypeAndSubTypeName(Constants.NOTIFICATION_TYPE_LITERAL, Constants.TOO_LONG)).thenReturn(openForTooLong);

        NotificationSettingsBean notificationSettingsBean = new NotificationSettingsBean();
        notificationSettingsBean.setTypeId(292);
        notificationSettingsBean.setDurationInMin(15);
        notificationSettingsBean.setAccountId(2);
        notificationSettingsList.add(notificationSettingsBean);
        notificationSettingsBean = new NotificationSettingsBean();
        notificationSettingsBean.setTypeId(293);
        notificationSettingsBean.setDurationInMin(30);
        notificationSettingsBean.setAccountId(2);
        notificationSettingsList.add(notificationSettingsBean);

        AccountBean accountBean = new AccountBean();
        accountBean.setId(2);

        mockUtilityBean = UtilityBean.<List<NotificationSettingsPojo>>builder()
                .requestParams(Map.of(
                        "Authorization", "mockAuthToken",
                        "accountIdentifier", "mockAccountIdentifier"
                ))
                .metadata(Map.of(
                        "userId", "mockUserId"
                ))
                .pojoObject(notificationSettings)
                .build();

        ReflectionTestUtils.setField(putNotificationSettingsBL, "longId", 292);
        ReflectionTestUtils.setField(putNotificationSettingsBL, "tooLongId", 293);
        ReflectionTestUtils.setField(putNotificationSettingsBL, "longName", "Open for long");
        ReflectionTestUtils.setField(putNotificationSettingsBL, "tooLongName", "Open for too long");

        ReflectionTestUtils.setField(putNotificationSettingsBL, "MIN_OPEN_FOR_LONG", 15);
        ReflectionTestUtils.setField(putNotificationSettingsBL, "MIN_OPEN_FOR_TOO_LONG", 30);
        ReflectionTestUtils.setField(putNotificationSettingsBL, "MAX_OPEN_FOR_LONG", 1440);
        ReflectionTestUtils.setField(putNotificationSettingsBL, "MAX_OPEN_FOR_TOO_LONG", 2880);
    }

    @AfterEach
    void tearDown() {
        requestParams = null;
        notificationSettings = null;
        notificationSettingsList = null;
    }

    @Test
    void clientValidations_EmptyAuthorizationToken() {
        requestParams[0] = "";
        requestParams[1] = "mockAccountIdentifier";

        String expectedMessage = "ClientException : Invalid authorization token";
        ClientException requestException = assertThrows(ClientException.class, () ->
                putNotificationSettingsBL.clientValidation(null, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidations_EmptyAccountIdentifier() {
        requestParams[0] = "mockAuthToken";
        requestParams[1] = "";

        String expectedMessage = "ClientException : Invalid account identifier";
        ClientException requestException = assertThrows(ClientException.class, () ->
                putNotificationSettingsBL.clientValidation(null, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidations_ErrorWhileFetchingUserId() throws Exception {
        String expectedMessage = "ClientException : Error occurred while fetching userId from authorization token";
        when(commonUtils.getUserId("mockAuthToken")).thenThrow(ControlCenterException.class);
        ClientException requestException = assertThrows(ClientException.class, () ->
                putNotificationSettingsBL.clientValidation(null, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidations_AuthKeyInvalid() throws Exception {
        String expectedMessage = "ClientException : Error occurred while fetching userId from authorization token";
        when(commonUtils.getUserId("mockAuthToken")).thenReturn(any());
        ClientException requestException = assertThrows(ClientException.class, () ->
                putNotificationSettingsBL.clientValidation(null, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidations_EmptyRequestBody() throws Exception {
        List<NotificationSettingsPojo> requestBody = new ArrayList<>();
        String userId = "mockUserId";
        String expectedMessage = "ClientException : Request body is null or empty.";
        when(commonUtils.getUserId("mockAuthToken")).thenReturn(userId);
        ClientException requestException = assertThrows(ClientException.class, () ->
                putNotificationSettingsBL.clientValidation(requestBody, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidations_InvalidRequestBody() throws Exception {
        notificationSettings.get(0).setDurationInMin(-1);
        String userId = "mockUserId";
        String expectedMessage = "ClientException : {Duration In Minutes=Invalid Duration.}";
        when(commonUtils.getUserId("mockAuthToken")).thenReturn(userId);
        ClientException requestException = assertThrows(ClientException.class, () ->
                putNotificationSettingsBL.clientValidation(notificationSettings, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidations_Success() throws Exception {
        String userId = "mockUserId";
        when(commonUtils.getUserId("mockAuthToken")).thenReturn(userId);
        UtilityBean<List<NotificationSettingsPojo>> utilityBean = putNotificationSettingsBL.clientValidation(notificationSettings, requestParams);
        assertEquals(utilityBean.getMetadata().get("userId"), userId);
    }

    @Test
    void serverValidation_Success() throws Exception {
        AccountBean account = new AccountBean();
        account.setId(1);

        mockUtilityBean.setMetadata(Map.of(Constants.ACCOUNT, account));

        when(accountsDao.getAccountByIdentifier("mockAccountIdentifier")).thenReturn(account);
        UtilityBean<List<NotificationSettingsPojo>> utilityBean = putNotificationSettingsBL.serverValidation(mockUtilityBean);
        assertEquals(((AccountBean)utilityBean.getMetadata().get(Constants.ACCOUNT)).getId(), account.getId());
    }

    @Test
    void serverValidation_InvalidAccountIdentifier() {
        String expectedMessage = "ServerException : Account with identifier [mockAccountIdentifier] is unavailable";
        when(accountsDao.getAccountByIdentifier("mockAccountIdentifier")).thenReturn(any());
        ServerException requestException = assertThrows(ServerException.class, () ->
                putNotificationSettingsBL.serverValidation(mockUtilityBean));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void process_Success_NotificationSettingsPresent() throws Exception {
        AccountBean account = new AccountBean();
        account.setId(1);
        mockUtilityBean.setMetadata(Map.of(Constants.ACCOUNT, account));
        when(dateTimeUtil.getDateInGMT(anyLong())).thenReturn(Timestamp.valueOf("2021-12-02 02:02:02"));
        when(notificationsDao.getNotificationSetting(((AccountBean)mockUtilityBean.getMetadata().get(Constants.ACCOUNT)).getId())).thenReturn(notificationSettingsList);
        putNotificationSettingsBL.process(mockUtilityBean);
    }

    @Test
    void process_Failure_NotificationSettingsNotPresent() throws Exception {
        AccountBean account = new AccountBean();
        account.setId(1);
        mockUtilityBean.setMetadata(Map.of(Constants.ACCOUNT, account));
        String expectedMessage = "DataProcessingException : Notification settings unavailable for the account.";
        when(dateTimeUtil.getDateInGMT(anyLong())).thenReturn(Timestamp.valueOf("2021-12-02 02:02:02"));
        when(notificationsDao.getNotificationSetting(((AccountBean)mockUtilityBean.getMetadata().get(Constants.ACCOUNT)).getId())).thenReturn(anyList());
        DataProcessingException requestException = assertThrows(DataProcessingException.class, () ->
                putNotificationSettingsBL.process(mockUtilityBean));
        System.out.println(requestException.getMessage());
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }
}
