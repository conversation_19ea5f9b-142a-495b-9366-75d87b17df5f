package com.heal.controlcenter.dao.redis;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.configuration.pojos.OSIndexZoneDetails;
import com.heal.configuration.pojos.ViewTypes;
import com.heal.controlcenter.util.RedisUtilities;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
@Slf4j
public class MasterDataRepo {

    @Autowired
    ObjectMapper objectMapper;
    @Autowired
    RedisUtilities redisUtilities;

    public List<OSIndexZoneDetails> getHealOSIndexToZoneMapping() {
        String key = "/heal/index/zones";
        String hashKey = "HEAL_INDEX_ZONES";
        try {
            String osIndexToZoneMapping = redisUtilities.getKey(key, hashKey);
            if (osIndexToZoneMapping == null || osIndexToZoneMapping.trim().isEmpty()) {
                log.warn("Null/Empty value found for key {} in redis.", key);
                return null;
            }

            return objectMapper.readValue(osIndexToZoneMapping, new TypeReference<>() {
            });

        } catch (Exception e) {
            log.error("Exception encountered while getting key '{}': {}", key, hashKey, e);
            return null;
        }
    }

    public List<ViewTypes> getViewTypes() {
        String HEAL_KEY = "/heal";
        String TYPE_KEY = "/types";
        String HEAL_HASH = "HEAL";
        String TYPE_HASH = "_TYPES";
        try {
            String typesObject = redisUtilities.getKey(HEAL_KEY + TYPE_KEY, HEAL_HASH + TYPE_HASH);
            if (typesObject == null) {
                log.debug("Type Details not found.");
                return Collections.emptyList();
            }
            return objectMapper.readValue(typesObject, new TypeReference<>() {
            });
        } catch (Exception e) {
            log.error("Error occurred while getting All Types details.");
        }

        return Collections.emptyList();
    }
}
