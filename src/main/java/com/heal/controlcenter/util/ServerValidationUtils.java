package com.heal.controlcenter.util;

import com.heal.configuration.entities.UserAccessDetails;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.Service;
import com.heal.controlcenter.dao.redis.AccountRepo;
import com.heal.controlcenter.dao.redis.ServiceRepo;
import com.heal.controlcenter.dao.redis.UserRepo;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.ServerException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class ServerValidationUtils {
    @Autowired
    AccountRepo accountRepo;
    @Autowired
    ServiceRepo serviceRepo;
    @Autowired
    CommonUtils commonUtils;
    @Autowired
    UserRepo userRepo;

    public Account accountValidation(String accountIdentifier) throws ServerException {
        Account account = accountRepo.getAccount(accountIdentifier);
        if (account == null) {
            log.error("Account {} details unavailable in redis.", accountIdentifier);
            throw new ServerException(String.format("Account %s details unavailable", accountIdentifier));
        }

        if (account.getStatus() != 1) {
            log.error("Account {} is inactive.", accountIdentifier);
            throw new ServerException(String.format("Account %s is inactive.", accountIdentifier));
        }

        if (account.getId() == 1) {
            log.error("GLOBAL Account {} is not supported for query.", accountIdentifier);
            throw new ServerException(String.format("GLOBAL Account %s is not supported for query.", accountIdentifier));
        }

        return account;
    }

    public Service serviceValidation(String userId, String accountIdentifier, String inputServiceIdentifier, UserAccessDetails userAccessDetails) throws ServerException {
        Service service = serviceRepo.getServiceConfigurationByIdentifier(accountIdentifier, inputServiceIdentifier);
        if (service == null) {
            log.error("Service {} detail not found for account {} in redis.", inputServiceIdentifier, accountIdentifier);
            throw new ServerException(String.format("Service %s detail not found for account %s in redis.", inputServiceIdentifier, accountIdentifier));
        }

        if (service.getStatus() != 1) {
            log.error("Service {} is inactive for account {}.", inputServiceIdentifier, accountIdentifier);
            throw new ServerException(String.format("Service %s is inactive for account %s.", inputServiceIdentifier, accountIdentifier));
        }

        if (userAccessDetails.isAccountWiseAccessible()) {
            log.trace("User has access to the entire account {}", accountIdentifier);
            return service;
        }

        if (userAccessDetails.getServices().stream().filter(Objects::nonNull).noneMatch(a -> a.getIdentifier().equals(inputServiceIdentifier))) {
            log.error("User {} does not have access to the service {}", userId, inputServiceIdentifier);
            throw new ServerException(String.format(UIMessages.USER_NOT_ALLOWED_TO_ACCESS_SERVICE, userId, inputServiceIdentifier));
        }

        return service;
    }

    public String authKeyValidation(String authKey) throws ServerException {
        try {
            String userId = commonUtils.getUserId(authKey);
            if (userId == null || userId.trim().isEmpty()) {
                log.error(UIMessages.AUTH_KEY_INVALID);
                throw new ServerException(UIMessages.USERID_EXTRACTION_EXCEPTION_MESSAGE);
            }
            return userId;
        } catch (HealControlCenterException e) {
            log.error("Error while fetching userId from authKey. Details: ", e);
            throw new ServerException(UIMessages.USERID_EXTRACTION_EXCEPTION_MESSAGE);
        }
    }

    public UserAccessDetails userAccessDetailsValidation(String userId, String accountIdentifier) throws ServerException {
        List<UserAccessDetails> userAccessDetails = userRepo.getUserAccessDetails(userId);
        if (userAccessDetails == null || userAccessDetails.isEmpty()) {
            String errorMessage = String.format(UIMessages.USER_ACCESS_DETAILS_NOT_FOUND, userId);
            log.error(errorMessage);
            throw new ServerException(errorMessage);
        }

        UserAccessDetails currentAccDetails = commonUtils.getUserAccessDetailsByAccount(userAccessDetails, accountIdentifier).get(accountIdentifier);
        if (currentAccDetails == null) {
            log.error("User {} doesn't have access to account {}", userId, accountIdentifier);
            throw new ServerException(String.format("User %s doesn't have access to account %s.", userId, accountIdentifier));
        }

        return currentAccDetails;
    }
}
