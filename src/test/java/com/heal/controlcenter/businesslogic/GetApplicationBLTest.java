package com.heal.controlcenter.businesslogic;


import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.pojo.Application;
import com.heal.controlcenter.util.CommonUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
public class GetApplicationBLTest {

    @Autowired
    @InjectMocks
    GetApplicationsBL applications;

    @Mock
    CommonUtils commonUtils;

    @BeforeEach
    void setUp() {
    }

    @AfterEach
    void tearDown() {
    }

    @Test
    void getClientValidations_InvalidUser()  {
        String expectedMessage = "ClientException : Invalid authorization token";
        String[] params = new String[3];
        params[0] = "fjskhfjk";
        params[1] = "d681ef13-d690-4917-jkhg-6c79b-1";
        params[2] = "true";
        ClientException requestException = assertThrows(ClientException.class, () ->
                applications.clientValidation("",params));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void getClientValidations_Invalid()  {
        String expectedMessage = "ClientException : Invalid authorization token";
        String[] params = new String[3];
        params[0] = "fjskhfjk";
        params[1] = "d681ef13-d690-4917-jkhg-6c79b-1";
        params[2] = "true";
        ClientException requestException = assertThrows(ClientException.class, () ->
                applications.clientValidation("",params));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

}
