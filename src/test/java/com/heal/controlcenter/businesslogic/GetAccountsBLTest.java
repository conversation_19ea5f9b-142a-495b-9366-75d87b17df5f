package com.heal.controlcenter.businesslogic;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.dao.mysql.TagsDao;
import com.heal.controlcenter.dao.mysql.UserDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.Account;
import com.heal.controlcenter.pojo.ThresholdSeverity;
import com.heal.controlcenter.util.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;

/**
 * Unit tests for GetAccountsBL class which handles fetching account information.
 */
@ExtendWith(MockitoExtension.class)
class GetAccountsBLTest {

    @InjectMocks
    GetAccountsBL getAccountsBL;

    @Mock
    AccountsDao accountsDao;
    @Mock
    UserDao userDao;
    @Mock
    TagsDao tagsDao;
    @Mock
    ObjectMapper objectMapper;
    @Mock
    KeyCloakAuthService keyCloakAuthService;
    @Mock
    ClientValidationUtils clientValidationUtils;
    @Mock
    ServerValidationUtils serverValidationUtils;

    private final String AUTH_TOKEN = "valid-token";
    private final String USER_ID = "user-001";
    private String[] requestParams;

    @BeforeEach
    void setup() {
        requestParams = new String[]{AUTH_TOKEN};
    }

    /**
     * Tests successful execution of clientValidation method when valid auth token is provided.
     * Expects a non-null UtilityBean with the correct token in requestParams.
     */
    @Test
    void testClientValidation_success() throws Exception {
        Account mockAccount = new Account();

        UtilityBean<Account> result = getAccountsBL.clientValidation(mockAccount, requestParams);

        assertNotNull(result);
        assertEquals(AUTH_TOKEN, result.getRequestParams().get(Constants.AUTH_KEY));
    }

    /**
     * Tests clientValidation when auth token is an empty string.
     * Expects ClientException to be thrown with appropriate message.
     */
    @Test
    void testClientValidation_authTokenNull_throwsClientException() throws ClientException {
        Account mockAccount = new Account();
        String[] emptyParams = new String[]{""};

        doThrow(new ClientException(UIMessages.AUTH_KEY_EXCEPTION_MESSAGE))
                .when(clientValidationUtils).authKeyValidation("");

        ClientException exception = assertThrows(ClientException.class,
                () -> getAccountsBL.clientValidation(mockAccount, emptyParams));
        assertEquals("ClientException : " + UIMessages.AUTH_KEY_EXCEPTION_MESSAGE, exception.getMessage());
    }

    /**
     * Tests clientValidation when auth token is null.
     * Expects ClientException to be thrown with appropriate message.
     */
    @Test
    void testClientValidation_authTokenIsNull_throwsClientException() throws ClientException {
        Account mockAccount = new Account();
        String[] params = new String[]{null};

        doThrow(new ClientException(UIMessages.AUTH_KEY_EXCEPTION_MESSAGE))
                .when(clientValidationUtils).authKeyValidation(null);

        ClientException exception = assertThrows(ClientException.class,
                () -> getAccountsBL.clientValidation(mockAccount, params));

        assertEquals("ClientException : " + UIMessages.AUTH_KEY_EXCEPTION_MESSAGE, exception.getMessage());
    }

    /**
     * Tests successful server validation where access details are valid and returned from UserDao.
     * Expects a UtilityBean with access details and user ID metadata.
     */
    @Test
    void testServerValidation_success() throws Exception {
        UtilityBean<Account> utilityBean = UtilityBean.<Account>builder()
                .requestParams(Map.of(Constants.AUTH_KEY, AUTH_TOKEN))
                .build();

        AccessDetailsBean accessDetailsBean = new AccessDetailsBean(List.of("*"), any());
        String jsonAccessDetails = new Gson().toJson(accessDetailsBean);

        UserAccessBean userAccessBean = new UserAccessBean();
        userAccessBean.setAccessDetails(jsonAccessDetails);

        when(serverValidationUtils.authKeyValidation(AUTH_TOKEN)).thenReturn(USER_ID);
        when(userDao.fetchUserAccessDetailsUsingIdentifier(USER_ID)).thenReturn(userAccessBean);
        when(objectMapper.readValue(jsonAccessDetails, AccessDetailsBean.class)).thenReturn(accessDetailsBean);

        UtilityBean<AccessDetailsBean> result = getAccountsBL.serverValidation(utilityBean);

        assertNotNull(result);
        assertEquals("*", result.getPojoObject().getAccounts().get(0));
        assertEquals(USER_ID, result.getMetadata().get(Constants.USER_ID_KEY));
    }

    /**
     * Tests serverValidation when invalid JSON access details are returned.
     * Simulates parsing failure and expects ServerException with USER_ACCESS_DETAILS_NOT_FOUND.
     */
    @Test
    void testServerValidation_invalidAccessDetails_throwsServerException() throws ServerException, JsonProcessingException {
        UtilityBean<Account> utilityBean = UtilityBean.<Account>builder()
                .requestParams(Map.of(Constants.AUTH_KEY, AUTH_TOKEN))
                .build();

        UserAccessBean accessBean = new UserAccessBean();
        accessBean.setAccessDetails("null");

        when(serverValidationUtils.authKeyValidation(AUTH_TOKEN)).thenReturn(USER_ID);
        when(userDao.fetchUserAccessDetailsUsingIdentifier(USER_ID)).thenReturn(accessBean);
        when(objectMapper.readValue("null", AccessDetailsBean.class)).thenThrow(new RuntimeException());

        ServerException exception = assertThrows(ServerException.class,
                () -> getAccountsBL.serverValidation(utilityBean));

        assertEquals("ServerException : " + UIMessages.USER_ACCESS_DETAILS_NOT_FOUND, exception.getMessage());
    }

    /**
     * Tests serverValidation when extracting user ID throws ServerException.
     * Expects the exception message to indicate user ID extraction failure.
     */
    @Test
    void testServerValidation_userIdExtractionThrowsControlCenterException() throws ServerException {
        UtilityBean<Account> utilityBean = UtilityBean.<Account>builder()
                .requestParams(Map.of(Constants.AUTH_KEY, AUTH_TOKEN))
                .build();

        when(serverValidationUtils.authKeyValidation(AUTH_TOKEN))
                .thenThrow(new ServerException(UIMessages.USERID_EXTRACTION_EXCEPTION_MESSAGE));

        ServerException exception = assertThrows(ServerException.class,
                () -> getAccountsBL.serverValidation(utilityBean));

        assertEquals("ServerException : " + UIMessages.USERID_EXTRACTION_EXCEPTION_MESSAGE, exception.getMessage());
    }

    /**
     * Tests serverValidation when parsed AccessDetailsBean is null.
     * Expects ServerException indicating invalid user access details.
     */
    @Test
    void testServerValidation_accessDetailsBeanIsNull_throwsServerException() throws ServerException, JsonProcessingException {
        UtilityBean<Account> utilityBean = UtilityBean.<Account>builder()
                .requestParams(Map.of(Constants.AUTH_KEY, AUTH_TOKEN))
                .build();

        String accessDetailsJson = "{}";

        UserAccessBean userAccessBean = new UserAccessBean();
        userAccessBean.setAccessDetails(accessDetailsJson);

        when(serverValidationUtils.authKeyValidation(AUTH_TOKEN)).thenReturn(USER_ID);
        when(userDao.fetchUserAccessDetailsUsingIdentifier(USER_ID)).thenReturn(userAccessBean);
        when(objectMapper.readValue(accessDetailsJson, AccessDetailsBean.class)).thenReturn(null); // Simulate null return

        ServerException exception = assertThrows(ServerException.class,
                () -> getAccountsBL.serverValidation(utilityBean));

        assertEquals("ServerException : " + UIMessages.INVALID_USER_ACCESS_DETAILS, exception.getMessage());
    }

    /**
     * Tests process method to filter accounts when user has restricted access (non-wildcard).
     * Only accounts matching access list should be returned.
     */
    @Test
    void testProcess_filtersAccessibleAccounts_whenNotWildcard() throws Exception {
        AccessDetailsBean accessDetailsBean = new AccessDetailsBean(List.of("acc-002"), null); // not "*"
        UtilityBean<AccessDetailsBean> utilityBean = UtilityBean.<AccessDetailsBean>builder()
                .pojoObject(accessDetailsBean)
                .metadata(Map.of("UserId", USER_ID))
                .build();

        // Two accounts, only one is allowed
        AccountBean acc1 = new AccountBean();
        acc1.setId(1);
        acc1.setIdentifier("acc-001");

        AccountBean acc2 = new AccountBean();
        acc2.setId(2);
        acc2.setIdentifier("acc-002");

        // Only acc2 should be included
        TimezoneBean timezoneBean = new TimezoneBean();
        timezoneBean.setOffset(3600000L);
        timezoneBean.setTimeZoneId("Europe/Paris");

        when(accountsDao.getAccounts()).thenReturn(List.of(acc1, acc2));
        when(accountsDao.getAccountTimezoneDetails(2)).thenReturn(timezoneBean);
        when(accountsDao.getAccountAnomalyConfiguration(2)).thenReturn(null);
        when(tagsDao.getTagsByObjectId(2, "account")).thenReturn(List.of());

        List<Account> result = getAccountsBL.process(utilityBean);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("acc-002", result.get(0).getIdentifier()); // Only filtered one
    }

    /**
     * Tests process method to skip accounts with null timezone information.
     * Expects such accounts to be excluded from final result list.
     */
    @Test
    void testProcess_timezoneBeanIsNull_skipsAccount() throws Exception {
        AccessDetailsBean accessDetailsBean = new AccessDetailsBean(List.of("*"), null);
        UtilityBean<AccessDetailsBean> utilityBean = UtilityBean.<AccessDetailsBean>builder()
                .pojoObject(accessDetailsBean)
                .metadata(Map.of("UserId", USER_ID))
                .build();

        AccountBean accBean = new AccountBean();
        accBean.setId(2);
        accBean.setIdentifier("acc-002");

        when(accountsDao.getAccounts()).thenReturn(List.of(accBean));
        when(accountsDao.getAccountTimezoneDetails(2)).thenReturn(null);

        List<Account> result = getAccountsBL.process(utilityBean);

        assertNotNull(result);
        assertEquals(0, result.size());
    }

    /**
     * Tests process method when getAccountTimezoneDetails throws ControlCenterException.
     * Expects DataProcessingException with propagated error message.
     */
    @Test
    void testProcess_timezoneDetailsThrowsControlCenterException_throwsDataProcessingException() throws Exception {
        AccessDetailsBean accessDetailsBean = new AccessDetailsBean(List.of("*"), null);
        UtilityBean<AccessDetailsBean> utilityBean = UtilityBean.<AccessDetailsBean>builder()
                .pojoObject(accessDetailsBean)
                .metadata(Map.of("UserId", USER_ID))
                .build();

        AccountBean accBean = new AccountBean();
        accBean.setId(10);
        accBean.setIdentifier("acc-010");

        when(accountsDao.getAccounts()).thenReturn(List.of(accBean));
        when(accountsDao.getAccountTimezoneDetails(10))
                .thenThrow(new ControlCenterException("Timezone error"));

        String expectedMessage = "DataProcessingException : Timezone error";
        DataProcessingException exception = assertThrows(DataProcessingException.class,
                () -> getAccountsBL.process(utilityBean));

        assertEquals(expectedMessage, exception.getMessage());
    }

    /**
     * Tests process method when getAccountAnomalyConfiguration throws ControlCenterException.
     * Expects DataProcessingException with propagated error message.
     */
    @Test
    void testProcess_anomalyConfigurationThrowsControlCenterException_throwsDataProcessingException() throws Exception {
        AccessDetailsBean accessDetailsBean = new AccessDetailsBean(List.of("*"), null);
        UtilityBean<AccessDetailsBean> utilityBean = UtilityBean.<AccessDetailsBean>builder()
                .pojoObject(accessDetailsBean)
                .metadata(Map.of("UserId", USER_ID))
                .build();

        AccountBean accBean = new AccountBean();
        accBean.setId(20);
        accBean.setIdentifier("acc-020");

        TimezoneBean timezoneBean = new TimezoneBean();
        timezoneBean.setOffset(7200000L);
        timezoneBean.setTimeZoneId("Europe/Berlin");

        when(accountsDao.getAccounts()).thenReturn(List.of(accBean));
        when(accountsDao.getAccountTimezoneDetails(20)).thenReturn(timezoneBean);
        when(accountsDao.getAccountAnomalyConfiguration(20))
                .thenThrow(new ControlCenterException("Anomaly config error"));

        String expectedMessage = "DataProcessingException : Anomaly config error";
        DataProcessingException exception = assertThrows(DataProcessingException.class,
                () -> getAccountsBL.process(utilityBean));

        assertEquals(expectedMessage, exception.getMessage());
    }

    /**
     * Tests process method to verify correct mapping of ThresholdSeverity based on config flags.
     * Expects flags (low, medium, high) to reflect AccountAnomalyConfigurationBean.
     */
    @Test
    void testProcess_configPresent_thresholdSeverityMappedCorrectly() throws Exception {
        AccessDetailsBean accessDetailsBean = new AccessDetailsBean(List.of("*"), null);
        UtilityBean<AccessDetailsBean> utilityBean = UtilityBean.<AccessDetailsBean>builder()
                .pojoObject(accessDetailsBean)
                .metadata(Map.of("UserId", USER_ID))
                .build();

        AccountBean accBean = new AccountBean();
        accBean.setId(1);
        accBean.setName("Test Account");
        accBean.setIdentifier("acc-001");
        accBean.setStatus(1);
        accBean.setPrivateKey("private-key");
        accBean.setPublicKey("public-key");
        accBean.setUpdatedTime("2024-06-19 10:00:00");
        accBean.setLastModifiedBy("test-user");

        // Config with known values
        AccountAnomalyConfigurationBean config = new AccountAnomalyConfigurationBean();
        config.setLowEnable(true);
        config.setMediumEnable(false);
        config.setHighEnable(true);
        config.setClosingWindow(10);
        config.setMaxDataBreaks(2);

        TimezoneBean timezoneBean = new TimezoneBean();
        timezoneBean.setOffset(19800000L);
        timezoneBean.setTimeZoneId("Asia/Kolkata");

        when(accountsDao.getAccounts()).thenReturn(List.of(accBean));
        when(accountsDao.getAccountTimezoneDetails(1)).thenReturn(timezoneBean);
        when(accountsDao.getAccountAnomalyConfiguration(1)).thenReturn(config);
        when(tagsDao.getTagsByObjectId(1, "account")).thenReturn(List.of());

        List<Account> result = getAccountsBL.process(utilityBean);

        assertNotNull(result);
        assertEquals(1, result.size());

        Account account = result.get(0);
        ThresholdSeverity severity = account.getThresholdSeverity();

        assertNotNull(severity);
        assertTrue(severity.isLow(), "Low should be true");
    }

    /**
     * Tests process method to verify that account updatedTime is correctly set as timestamp.
     * Expects updatedTime in result Account object to be non-null and valid.
     */
    @Test
    void testProcess_accountWithUpdatedTime_setsTimestamp() throws Exception {
        AccessDetailsBean accessDetailsBean = new AccessDetailsBean(List.of("*"), null);
        UtilityBean<AccessDetailsBean> utilityBean = UtilityBean.<AccessDetailsBean>builder()
                .pojoObject(accessDetailsBean)
                .metadata(Map.of(Constants.USER_ID_KEY, USER_ID))
                .build();

        AccountBean accBean = new AccountBean();
        accBean.setId(5);
        accBean.setIdentifier("acc-005");
        accBean.setUpdatedTime("2024-06-10 12:34:56");
        accBean.setStatus(1);

        TimezoneBean timezoneBean = new TimezoneBean();
        timezoneBean.setOffset(19800000L);
        timezoneBean.setTimeZoneId("Asia/Kolkata");

        when(accountsDao.getAccounts()).thenReturn(List.of(accBean));
        when(accountsDao.getAccountTimezoneDetails(5)).thenReturn(timezoneBean);
        when(accountsDao.getAccountAnomalyConfiguration(5)).thenReturn(null);
        when(tagsDao.getTagsByObjectId(5, "account")).thenReturn(List.of());

        List<Account> result = getAccountsBL.process(utilityBean);

        assertNotNull(result);
        assertEquals(1, result.size());
        Account account = result.get(0);
        assertNotNull(account.getUpdatedTime());
        assertEquals("acc-005", account.getIdentifier());
    }


    /**
     * Tests process method when account retrieval fails with ControlCenterException.
     * Expects DataProcessingException indicating failure to fetch accounts.
     */
    @Test
    void testProcess_accountFetchThrowsControlCenterException() throws Exception {
        AccessDetailsBean accessDetailsBean = new AccessDetailsBean(List.of("*"), null);
        UtilityBean<AccessDetailsBean> utilityBean = UtilityBean.<AccessDetailsBean>builder()
                .pojoObject(accessDetailsBean)
                .metadata(Map.of(Constants.USER_ID_KEY, USER_ID))
                .build();

        when(accountsDao.getAccounts()).thenThrow(new ControlCenterException("DB fetch failed"));

        String expectedMessage = "DataProcessingException : Error in fetching accounts details";
        DataProcessingException exception = assertThrows(DataProcessingException.class,
                () -> getAccountsBL.process(utilityBean));

        assertEquals(expectedMessage, exception.getMessage());
    }
}
