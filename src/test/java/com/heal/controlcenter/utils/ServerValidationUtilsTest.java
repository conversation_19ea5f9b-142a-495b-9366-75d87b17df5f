package com.heal.controlcenter.utils;

import com.heal.configuration.entities.UserAccessDetails;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.Service;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.dao.redis.AccountRepo;
import com.heal.controlcenter.dao.redis.ServiceRepo;
import com.heal.controlcenter.dao.redis.UserRepo;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.util.CommonUtils;
import com.heal.controlcenter.util.ServerValidationUtils;
import com.heal.controlcenter.util.UIMessages;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class ServerValidationUtilsTest {

    @InjectMocks
    private ServerValidationUtils serverValidationUtils;

    @Mock
    private AccountRepo accountRepo;

    @Mock
    private ServiceRepo serviceRepo;

    @Mock
    private AccountsDao accountDao;

    @Mock
    private CommonUtils commonUtils;

    @Mock
    private UserRepo userRepo;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void accountValidation_ValidAccount_ReturnsAccount() throws ServerException {
        Account account = new Account();
        account.setId(2);
        account.setStatus(1);
        when(accountRepo.getAccount("validAccount")).thenReturn(account);

        Account result = serverValidationUtils.accountValidation("validAccount");

        assertNotNull(result);
        assertEquals(2, result.getId());
    }

    @Test
    void accountValidation_NullAccount_ThrowsServerException() {
        when(accountRepo.getAccount("invalidAccount")).thenReturn(null);

        ServerException exception = assertThrows(ServerException.class, () ->
                serverValidationUtils.accountValidation("invalidAccount"));
        assertTrue(exception.getMessage().contains("details unavailable"));
    }

    @Test
    void accountValidation_InactiveAccount_ThrowsServerException() {
        Account account = new Account();
        account.setStatus(0);
        when(accountRepo.getAccount("inactiveAccount")).thenReturn(account);

        ServerException exception = assertThrows(ServerException.class, () ->
                serverValidationUtils.accountValidation("inactiveAccount"));
        assertTrue(exception.getMessage().contains("is inactive"));
    }

    @Test
    void accountValidation_GlobalAccount_ThrowsServerException() {
        Account account = new Account();
        account.setId(1);
        when(accountRepo.getAccount("globalAccount")).thenReturn(account);

        ServerException exception = assertThrows(ServerException.class, () ->
                serverValidationUtils.accountValidation("globalAccount"));
        assertTrue(exception.getMessage().contains("is inactive"));
    }

    @Test
    void serviceValidation_ValidService_ReturnsService() throws ServerException {
        Service service = new Service();
        service.setStatus(1);
        service.setIdentifier("validService");
        UserAccessDetails userAccessDetails = new UserAccessDetails();
        userAccessDetails.setAccountWiseAccessible(true);

        when(serviceRepo.getServiceConfigurationByIdentifier("validAccount", "validService")).thenReturn(service);

        Service result = serverValidationUtils.serviceValidation("userId", "validAccount", "validService", userAccessDetails);

        assertNotNull(result);
        assertEquals("validService", result.getIdentifier());
    }

    @Test
    void serviceValidation_NullService_ThrowsServerException() {
        when(serviceRepo.getServiceConfigurationByIdentifier("validAccount", "invalidService")).thenReturn(null);

        ServerException exception = assertThrows(ServerException.class, () ->
                serverValidationUtils.serviceValidation("userId", "validAccount", "invalidService", new UserAccessDetails()));
        assertTrue(exception.getMessage().contains("detail not found"));
    }

    @Test
    void serviceValidation_InactiveService_ThrowsServerException() {
        Service service = new Service();
        service.setStatus(0);
        when(serviceRepo.getServiceConfigurationByIdentifier("validAccount", "inactiveService")).thenReturn(service);

        ServerException exception = assertThrows(ServerException.class, () ->
                serverValidationUtils.serviceValidation("userId", "validAccount", "inactiveService", new UserAccessDetails()));
        assertTrue(exception.getMessage().contains("is inactive"));
    }

    @Test
    void authKeyValidation_ValidAuthKey_ReturnsUserId() throws ServerException, ControlCenterException {
        when(commonUtils.getUserId("validAuthKey")).thenReturn("userId");

        String result = serverValidationUtils.authKeyValidation("validAuthKey");

        assertEquals("userId", result);
    }

    @Test
    void authKeyValidation_InvalidAuthKey_ThrowsServerException() throws ControlCenterException {
        when(commonUtils.getUserId("invalidAuthKey")).thenThrow(new ControlCenterException("Error"));

        ServerException exception = assertThrows(ServerException.class, () ->
                serverValidationUtils.authKeyValidation("invalidAuthKey"));
        assertTrue(exception.getMessage().contains(UIMessages.USERID_EXTRACTION_EXCEPTION_MESSAGE));
    }

    @Test
    void userAccessDetailsValidation_ValidInput_ReturnsUserAccessDetails() throws ServerException {
        List<UserAccessDetails> userAccessDetailsList = new ArrayList<>();
        UserAccessDetails userAccessDetails = new UserAccessDetails();
        userAccessDetails.setAccount(Account.builder().identifier("validAccount").build());
        userAccessDetailsList.add(userAccessDetails);
        HashMap<String, UserAccessDetails> userAccessDetailsMap = new HashMap<>();
        userAccessDetailsMap.put("validAccount", userAccessDetails);

        when(userRepo.getUserAccessDetails("userId")).thenReturn(userAccessDetailsList);
        when(commonUtils.getUserAccessDetailsByAccount(userAccessDetailsList, "validAccount"))
                .thenReturn(userAccessDetailsMap);

        UserAccessDetails result = serverValidationUtils.userAccessDetailsValidation("userId", "validAccount");

        assertNotNull(result);
        assertEquals("validAccount", result.getAccount().getIdentifier());
    }

    @Test
    void userAccessDetailsValidation_NoAccessDetails_ThrowsServerException() {
        when(userRepo.getUserAccessDetails("userId")).thenReturn(Collections.emptyList());

        ServerException exception = assertThrows(ServerException.class, () ->
                serverValidationUtils.userAccessDetailsValidation("userId", "validAccount"));
        assertTrue(exception.getMessage().contains("User access details not found for user"));
    }

    @Test
    void userAccessDetailsValidation_NoAccessToAccount_ThrowsServerException() {
        List<UserAccessDetails> userAccessDetailsList = new ArrayList<>();
        userAccessDetailsList.add(UserAccessDetails.builder().build());
        when(userRepo.getUserAccessDetails("userId")).thenReturn(userAccessDetailsList);
        when(commonUtils.getUserAccessDetailsByAccount(userAccessDetailsList, "validAccount"))
                .thenReturn(Collections.emptyMap());

        ServerException exception = assertThrows(ServerException.class, () ->
                serverValidationUtils.userAccessDetailsValidation("userId", "validAccount"));
        assertTrue(exception.getMessage().contains("doesn't have access to account"));
    }
}
