package com.heal.controlcenter.businesslogic;

import com.heal.controlcenter.beans.AccountBean;
import com.heal.controlcenter.beans.ForensicActionArgumentsBean;
import com.heal.controlcenter.beans.ForensicActionBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.dao.mysql.ActionScriptDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.ForensicActionsPojo;
import com.heal.controlcenter.util.CommonUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@PrepareForTest({GetForensicActionsBL.class})
class GetForensicActionsBLTest {

    @Autowired
    @InjectMocks
    GetForensicActionsBL getForensicActionsBL;

    @Mock
    CommonUtils commonUtils;
    @Mock
    AccountsDao accountsDao;
    @Mock
    ActionScriptDao actionScriptDao;

    String[] requestParams = new String[2];
    UtilityBean<Object> mockUtilityBean = null;

    @BeforeEach
    void setUp() {
        requestParams[0] = "mockAuthToken";
        requestParams[1] = "mockAccountIdentifier";
        mockUtilityBean = UtilityBean.builder()
                .requestParams(Map.of(
                        "Authorization", "mockAuthToken",
                        "accountIdentifier", "mockAccountIdentifier"
                ))
                .metadata(Map.of(
                        "userId", "mockUserId"
                ))
                .build();
    }

    @AfterEach
    void tearDown() {
        requestParams = null;
    }

    @Test
    void clientValidations_EmptyAuthorizationToken() {
        requestParams[0] = "";
        requestParams[1] = "mockAccountIdentifier";

        String expectedMessage = "ClientException : Invalid authorization token";
        ClientException requestException = assertThrows(ClientException.class, () ->
                getForensicActionsBL.clientValidation(null, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidations_EmptyAccountIdentifier() {
        requestParams[0] = "mockAuthToken";
        requestParams[1] = "";

        String expectedMessage = "ClientException : Invalid account identifier";
        ClientException requestException = assertThrows(ClientException.class, () ->
                getForensicActionsBL.clientValidation(null, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidations_ErrorWhileFetchingUserId() throws Exception {
        String expectedMessage = "ClientException : Error occurred while fetching userId from authorization token";
        when(commonUtils.getUserId("mockAuthToken")).thenThrow(HealControlCenterException.class);
        ClientException requestException = assertThrows(ClientException.class, () ->
                getForensicActionsBL.clientValidation(null, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidations_AuthKeyInvalid() throws Exception {
        String expectedMessage = "ClientException : Error occurred while fetching userId from authorization token";
        when(commonUtils.getUserId("mockAuthToken")).thenReturn(any());
        ClientException requestException = assertThrows(ClientException.class, () ->
                getForensicActionsBL.clientValidation(null, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidations_Success() throws Exception {
        String userId = "mockUserId";
        when(commonUtils.getUserId("mockAuthToken")).thenReturn(userId);
        UtilityBean<Object> utilityBean = getForensicActionsBL.clientValidation(null, requestParams);
        assertEquals(utilityBean.getMetadata().get("userId"), userId);
    }

    @Test
    void serverValidation_Success() throws Exception {
        AccountBean account = new AccountBean();
        account.setId(1);

        when(accountsDao.getAccountByIdentifier("mockAccountIdentifier")).thenReturn(account);
        Integer accountId = getForensicActionsBL.serverValidation(mockUtilityBean);
        assertEquals(account.getId(), accountId);
    }

    @Test
    void serverValidation_InvalidAccountIdentifier() {
        String expectedMessage = "ServerException : Account with identifier [mockAccountIdentifier] is unavailable";
        when(accountsDao.getAccountByIdentifier("mockAccountIdentifier")).thenReturn(any());
        ServerException requestException = assertThrows(ServerException.class, () ->
                getForensicActionsBL.serverValidation(mockUtilityBean));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void process_Success() throws Exception {
        List<ForensicActionBean> forensicActions = new ArrayList<>();
        List<ForensicActionArgumentsBean> forensicActionsParametersList = new ArrayList<>();

        ForensicActionBean forensicActionBean = new ForensicActionBean();
        forensicActionBean.setActionId(1);
        forensicActionBean.setActionName("CPU");
        forensicActionBean.setActionType("OOB");
        forensicActionBean.setCommandName("Fetch CPU Utilization");
        forensicActionBean.setCategoryId(1);
        forensicActionBean.setCategoryName("CPU");
        forensicActionBean.setCategoryIdentifier("CPU");
        forensicActionBean.setIsCustomCategory(0);
        forensicActionBean.setCommandId(1);
        forensicActions.add(forensicActionBean);

        ForensicActionArgumentsBean actionArgumentsBean = new ForensicActionArgumentsBean();
        actionArgumentsBean.setArgument_key("ARGUMENT_KEY");
        actionArgumentsBean.setCommandId(1);
        actionArgumentsBean.setDefaultValue("DEFAULT_VALUE");
        actionArgumentsBean.setType("COMMANDLINE");
        actionArgumentsBean.setValue("VALUE");
        forensicActionsParametersList.add(actionArgumentsBean);

        when(actionScriptDao.getForensicActions(anyInt())).thenReturn(forensicActions);
        when(actionScriptDao.getForensicsParameters()).thenReturn(forensicActionsParametersList);

        List<ForensicActionsPojo> actions = getForensicActionsBL.process(anyInt());
        assertEquals(actions.size(), 1);
        assertEquals(actions.get(0).getName(), "CPU");
        assertEquals(actions.get(0).getCategoryList().size(), 1);
        assertEquals(actions.get(0).getParameters().size(), 1);
    }

    @Test
    void process_Failure() throws HealControlCenterException {
        String expectedMessage = "DataProcessingException : Error occurred while getting forensic actions.";
        HealControlCenterException healControlCenterException = new HealControlCenterException("Error occurred while getting forensic actions.");

        when(actionScriptDao.getForensicActions(anyInt())).thenThrow(healControlCenterException);
        DataProcessingException requestException = assertThrows(DataProcessingException.class, () ->
                getForensicActionsBL.process(anyInt()));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }
}
