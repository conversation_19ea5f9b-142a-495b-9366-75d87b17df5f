package com.heal.controlcenter.businesslogic;

import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.AnomalyConfiguration;
import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.dao.mysql.*;
import com.heal.controlcenter.dao.redis.ApplicationRepo;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.*;
import com.heal.controlcenter.util.ClientValidationUtils;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.DateTimeUtil;
import com.heal.controlcenter.util.ServerValidationUtils;
import com.heal.controlcenter.util.UIMessages;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class PutApplicationsBLTest {

    @Spy
    @InjectMocks
    private PutApplicationsBL putApplicationsBL;

    @Mock
    private ControllerDao controllerDao;
    @Mock
    private TagsDao tagsDao;
    @Mock
    private MasterDataDao masterDataDao;
    @Mock
    private DateTimeUtil dateTimeUtil;
    @Mock
    private AccountsDao accountsDao;
    @Mock
    private ClientValidationUtils clientValidationUtils;
    @Mock
    private ServerValidationUtils serverValidationUtils;
    @Mock
    private UserDao userDao;
    @Mock
    private ApplicationRepo applicationRepo;
    //@Mock
    //private ApplicationNotifAndPercentileDao applicationNotifAndPercentileDao;

    private List<Application> applicationPojos;
    private Application applicationPojo;
    private UtilityBean<List<Application>> clientValidationInput;
    private UtilityBean<List<ApplicationBean>> serverValidationInput;
    private Account account;
    private UserInfoBean userInfoBean;
    private UserProfileBean userProfileBean;
    private TimezoneBean timezoneBean;
    private ControllerBean existingApp;
    private List<ApplicationBean> applicationBeans;

    @BeforeEach
    void setUp() {
        // Setup application pojo
        applicationPojo = new Application();
        applicationPojo.setIdentifier("app-id-1");
        applicationPojo.setName("Test App");
        applicationPojo.setEnvironment("DC");
        applicationPojo.setTimezoneId("1");
        applicationPojo.setTags(Collections.singletonList(new ApplicationTags("tag-type", "tag-key", "tag-value", "action")));
        SeverityLevel severityLevel = new SeverityLevel();
        severityLevel.setType("low");
        severityLevel.setStatus(1);
        applicationPojo.setSeverity(Collections.singletonList(severityLevel));
        applicationPojos = Collections.singletonList(applicationPojo);

        // Setup account
        account = new Account();
        account.setId(1);
        account.setIdentifier("test-account");

        // Setup user info
        userInfoBean = new UserInfoBean();
        userInfoBean.setProfileId(1);
        userInfoBean.setUserDetailsId("test-user");

        // Setup user profile
        userProfileBean = new UserProfileBean();
        userProfileBean.setUserProfileName("Super Admin");

        // Setup timezone
        timezoneBean = new TimezoneBean();
        timezoneBean.setId(1);
        timezoneBean.setOffset(19800000L);
        timezoneBean.setTimeZoneId("Asia/Kolkata");

        // Setup existing application controller bean
        existingApp = new ControllerBean();
        existingApp.setId(101);
        existingApp.setIdentifier("app-id-1_dc");
        existingApp.setName("Test App_DC");
        existingApp.setAccountId(1);
        existingApp.setStatus(1);

        // Setup ApplicationBean list for process method
        ApplicationBean appBean = ApplicationBean.builder()
                .identifier("app-id-1_dc")
                .name("Test App")
                .accountId(1)
                .environment("DC")
                .severity(Collections.singletonList(severityLevel))
                .tags(Collections.singletonList(new ApplicationTags("tag-type", "tag-key", "tag-value", "action")))
                .userId("test-user")
                .build();
        applicationBeans = new ArrayList<>(Collections.singletonList(appBean));

        // Setup utility beans
        Map<String, String> requestParams = new HashMap<>();
        requestParams.put(Constants.ACCOUNT_IDENTIFIER, "test-account");

        Map<String, Object> metadata = new HashMap<>();
        metadata.put(Constants.USER_ID_KEY, "test-user");

        clientValidationInput = UtilityBean.<List<Application>>builder()
                .pojoObject(applicationPojos)
                .requestParams(requestParams)
                .metadata(metadata)
                .build();

        serverValidationInput = UtilityBean.<List<ApplicationBean>>builder()
                .pojoObject(applicationBeans)
                .requestParams(requestParams)
                .metadata(metadata)
                .build();
    }

    // clientValidation tests
    @Test
    @DisplayName("clientValidation - Success")
    void clientValidation_Success() throws ClientException {
        doNothing().when(clientValidationUtils).accountIdentifierValidation(anyString());
        doNothing().when(clientValidationUtils).applicationIdentifierValidation(anyString());
        // Create a spy of the application pojo to mock the validate method
        Application spyAppPojo = spy(applicationPojo);
        when(spyAppPojo.validate()).thenReturn(new HashMap<>());
        applicationPojos = Collections.singletonList(spyAppPojo);

        UtilityBean<List<Application>> result = putApplicationsBL.clientValidation(applicationPojos, "test-account");

        assertNotNull(result);
        assertEquals(applicationPojos, result.getPojoObject());
        assertEquals("test-account", result.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER));
    }

    @Test
    @DisplayName("clientValidation - Invalid Account Identifier")
    void clientValidation_InvalidAccountIdentifier_ThrowsClientException() throws ClientException {
        doThrow(new ClientException("Invalid account identifier"))
                .when(clientValidationUtils).accountIdentifierValidation("test-account");

        ClientException e = assertThrows(ClientException.class, () -> {
            putApplicationsBL.clientValidation(applicationPojos, "test-account");
        });
        assertTrue(e.getMessage().contains("Invalid account identifier"));
    }

    @Test
    @DisplayName("clientValidation - Invalid Application Identifier")
    void clientValidation_InvalidApplicationIdentifier_ThrowsClientException() throws ClientException {
        doNothing().when(clientValidationUtils).accountIdentifierValidation(anyString());
        doThrow(new ClientException("Invalid application identifier"))
                .when(clientValidationUtils).applicationIdentifierValidation(anyString());

        ClientException e = assertThrows(ClientException.class, () -> {
            putApplicationsBL.clientValidation(applicationPojos, "test-account");
        });
        assertTrue(e.getMessage().contains("Invalid application identifier"));
    }

    @Test
    @DisplayName("clientValidation - Application Validation Fails")
    void clientValidation_ApplicationValidationFails_ThrowsClientException() throws ClientException {
        doNothing().when(clientValidationUtils).accountIdentifierValidation(anyString());
        doNothing().when(clientValidationUtils).applicationIdentifierValidation(anyString());
        Map<String, String> errors = new HashMap<>();
        errors.put("name", "Application name cannot be empty");
        Application spyAppPojo = spy(applicationPojo);
        when(spyAppPojo.validate()).thenReturn(errors);
        applicationPojos = Collections.singletonList(spyAppPojo);

        ClientException e = assertThrows(ClientException.class, () -> {
            putApplicationsBL.clientValidation(applicationPojos, "test-account");
        });
        assertTrue(e.getMessage().contains("Application name cannot be empty"));
    }

    // serverValidation tests
    @Test
    @DisplayName("serverValidation - Success")
    void serverValidation_Success() throws ServerException, HealControlCenterException {
        when(serverValidationUtils.accountValidation(anyString())).thenReturn(account);
        when(userDao.getUserDetails(anyString())).thenReturn(userInfoBean);
        when(userDao.getUserProfile(anyInt())).thenReturn(userProfileBean);
        when(controllerDao.existsByApplicationIdentifier(anyString())).thenReturn(true);
        when(masterDataDao.getTimeZoneWithId(anyString())).thenReturn(timezoneBean);

        UtilityBean<List<ApplicationBean>> result = putApplicationsBL.serverValidation(clientValidationInput);

        assertNotNull(result);
        assertEquals(1, result.getPojoObject().size());
        ApplicationBean resultBean = result.getPojoObject().get(0);
        assertEquals("Test App", resultBean.getName());
        assertEquals("app-id-1", resultBean.getIdentifier());
        assertEquals(1, resultBean.getAccountId());
        assertEquals("test-user", resultBean.getUserId());
        assertEquals(timezoneBean, result.getMetadata().get(Constants.TIME_ZONE_TAG));
    }

    @Test
    @DisplayName("serverValidation - Account Not Found")
    void serverValidation_AccountNotFound_ThrowsServerException() throws ServerException {
        when(serverValidationUtils.accountValidation(anyString())).thenThrow(new ServerException("Account not found"));

        ServerException e = assertThrows(ServerException.class, () -> {
            putApplicationsBL.serverValidation(clientValidationInput);
        });
        assertTrue(e.getMessage().contains("Account not found"));
    }

    @Test
    @DisplayName("serverValidation - User Not Found")
    void serverValidation_UserNotFound_ThrowsServerException() throws ServerException, HealControlCenterException {
        when(serverValidationUtils.accountValidation(anyString())).thenReturn(account);
        when(userDao.getUserDetails(anyString())).thenThrow(new HealControlCenterException("User not found"));

        ServerException e = assertThrows(ServerException.class, () -> {
            putApplicationsBL.serverValidation(clientValidationInput);
        });
        assertTrue(e.getMessage().contains("User not found"));
    }

    @Test
    @DisplayName("serverValidation - User Not Authorized")
    void serverValidation_UserNotAuthorized_ThrowsServerException() throws ServerException, HealControlCenterException {
        userProfileBean.setUserProfileName("USER"); // A non-admin profile
        when(serverValidationUtils.accountValidation(anyString())).thenReturn(account);
        when(userDao.getUserDetails(anyString())).thenReturn(userInfoBean);
        when(userDao.getUserProfile(anyInt())).thenReturn(userProfileBean);

        ServerException e = assertThrows(ServerException.class, () -> {
            putApplicationsBL.serverValidation(clientValidationInput);
        });
        assertTrue(e.getMessage().contains(UIMessages.USER_NOT_ALLOWED_CREATE_APP));
    }

    @Test
    @DisplayName("serverValidation - Timezone Not Found")
    void serverValidation_TimezoneNotFound_ThrowsServerException() throws ServerException, HealControlCenterException {
        when(serverValidationUtils.accountValidation(anyString())).thenReturn(account);
        when(userDao.getUserDetails(anyString())).thenReturn(userInfoBean);
        when(userDao.getUserProfile(anyInt())).thenReturn(userProfileBean);
        when(controllerDao.existsByApplicationIdentifier(anyString())).thenReturn(true);
        when(masterDataDao.getTimeZoneWithId(anyString())).thenThrow(new HealControlCenterException("Timezone not found"));

        ServerException e = assertThrows(ServerException.class, () -> {
            putApplicationsBL.serverValidation(clientValidationInput);
        });
        assertTrue(e.getMessage().contains("Timezone not found"));
    }

    // process tests
    @Test
    @DisplayName("process - Success")
    void process_Success() throws DataProcessingException, HealControlCenterException {
        // Arrange
        serverValidationInput.getMetadata().put(Constants.TIME_ZONE_TAG, timezoneBean);
        when(dateTimeUtil.getCurrentTimestampInGMT()).thenReturn(new Timestamp(System.currentTimeMillis()));
        when(controllerDao.getFullApplicationDetailsByIdentifier(anyString())).thenReturn(existingApp);
        doNothing().when(controllerDao).updateApplication(any(ControllerBean.class));

        ApplicationAliases alias = new ApplicationAliases();
        alias.setCommonName("old-name");
        when(controllerDao.getApplicationAliasByDcIdentifier(anyString())).thenReturn(alias);
        doNothing().when(controllerDao).updateApplicationAlias(any(ApplicationAliases.class));

        TagMappingDetails timezoneTag = new TagMappingDetails();
        when(tagsDao.getTimezoneTagMappingByObjectId(anyInt(), anyString())).thenReturn(timezoneTag);
        when(tagsDao.updateTagMappingDetails(any(TagMappingDetails.class))).thenReturn(1);

        AnomalyConfiguration accountAnomalyConfig = new AnomalyConfiguration();
        accountAnomalyConfig.setClosingWindow(10);
        accountAnomalyConfig.setMaxDataBreaks(5);
        when(accountsDao.getAccountAnomalyConfiguration(anyInt())).thenReturn(accountAnomalyConfig);
        doNothing().when(controllerDao).updateApplicationAnomalyConfigurations(any(ApplicationAnomalyConfiguration.class));

        com.heal.configuration.pojos.Application redisApp = new com.heal.configuration.pojos.Application();
        redisApp.setId(101);
        List<com.heal.configuration.pojos.Application> redisApps = new ArrayList<>(Collections.singletonList(redisApp));
        when(applicationRepo.getApplicationsForAccount(anyString())).thenReturn(redisApps);
        doNothing().when(applicationRepo).updateApplication(anyString(), any(com.heal.configuration.pojos.Application.class));
        doNothing().when(applicationRepo).updateApplicationDetailsForAccount(anyString(), anyList());

        // Act
        List<IdPojo> result = putApplicationsBL.process(serverValidationInput);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(existingApp.getId(), result.get(0).getId());
        assertEquals("Test App_dc", result.get(0).getName());

        verify(controllerDao, times(1)).updateApplication(any(ControllerBean.class));
        verify(controllerDao, times(1)).updateApplicationAlias(any(ApplicationAliases.class));
        verify(tagsDao, times(1)).updateTagMappingDetails(any(TagMappingDetails.class));
        verify(controllerDao, times(1)).updateApplicationAnomalyConfigurations(any(ApplicationAnomalyConfiguration.class));
        verify(applicationRepo, times(1)).updateApplication(anyString(), any(com.heal.configuration.pojos.Application.class));
        verify(applicationRepo, times(1)).updateApplicationDetailsForAccount(anyString(), anyList());
    }

    @Test
    @DisplayName("process - Application Not Found Throws Exception")
    void process_AppNotFound_ThrowsDataProcessingException() throws HealControlCenterException {
        serverValidationInput.getMetadata().put(Constants.TIME_ZONE_TAG, timezoneBean);
        when(dateTimeUtil.getCurrentTimestampInGMT()).thenReturn(new Timestamp(System.currentTimeMillis()));
        when(controllerDao.getFullApplicationDetailsByIdentifier(anyString())).thenReturn(null);

        DataProcessingException e = assertThrows(DataProcessingException.class, () -> {
            putApplicationsBL.process(serverValidationInput);
        });

        assertTrue(e.getMessage().contains("Application with identifier not found"));
    }

    @Test
    @DisplayName("process - Application Update Fails Throws Exception")
    void process_AppUpdateFails_ThrowsDataProcessingException() throws HealControlCenterException {
        serverValidationInput.getMetadata().put(Constants.TIME_ZONE_TAG, timezoneBean);
        when(dateTimeUtil.getCurrentTimestampInGMT()).thenReturn(new Timestamp(System.currentTimeMillis()));
        when(controllerDao.getFullApplicationDetailsByIdentifier(anyString())).thenReturn(existingApp);
        doThrow(new HealControlCenterException("DB error")).when(controllerDao).updateApplication(any(ControllerBean.class));

        DataProcessingException e = assertThrows(DataProcessingException.class, () -> {
            putApplicationsBL.process(serverValidationInput);
        });

        assertTrue(e.getMessage().contains("Error updating application"));
    }

    @Test
    @DisplayName("process - Linked Environment Update")
    void process_LinkedEnvironment_Success() throws DataProcessingException, HealControlCenterException {
        // Arrange
        LinkedEnvironment linkedEnv = new LinkedEnvironment();
        linkedEnv.setMappedToApplication("mapped-app-name");
        linkedEnv.setEnvironment("DR");
        applicationBeans.get(0).setLinkedEnvironment(linkedEnv);
        serverValidationInput.setPojoObject(applicationBeans);
        serverValidationInput.getMetadata().put(Constants.TIME_ZONE_TAG, timezoneBean);

        when(dateTimeUtil.getCurrentTimestampInGMT()).thenReturn(new Timestamp(System.currentTimeMillis()));
        when(controllerDao.getFullApplicationDetailsByIdentifier(anyString())).thenReturn(existingApp);
        doNothing().when(controllerDao).updateApplication(any(ControllerBean.class));

        ApplicationAliases alias = new ApplicationAliases();
        alias.setDcApplicationIdentifier("old-mapped-app-id");
        when(controllerDao.getApplicationAliasByDcIdentifier(anyString())).thenReturn(alias);

        ControllerBean mappedApp = new ControllerBean();
        mappedApp.setIdentifier("new-mapped-app-id");
        when(controllerDao.getApplicationIdByName(anyString())).thenReturn(mappedApp);

        TagMappingDetails timezoneTag = new TagMappingDetails();
        when(tagsDao.getTimezoneTagMappingByObjectId(anyInt(), anyString())).thenReturn(timezoneTag);
        when(tagsDao.updateTagMappingDetails(any(TagMappingDetails.class))).thenReturn(1);

        AnomalyConfiguration accountAnomalyConfig = new AnomalyConfiguration();
        when(accountsDao.getAccountAnomalyConfiguration(anyInt())).thenReturn(accountAnomalyConfig);
        doNothing().when(controllerDao).updateApplicationAnomalyConfigurations(any(ApplicationAnomalyConfiguration.class));

        com.heal.configuration.pojos.Application redisApp = new com.heal.configuration.pojos.Application();
        redisApp.setId(101);
        List<com.heal.configuration.pojos.Application> redisApps = new ArrayList<>(Collections.singletonList(redisApp));
        when(applicationRepo.getApplicationsForAccount(anyString())).thenReturn(redisApps);
        doNothing().when(applicationRepo).updateApplication(anyString(), any(com.heal.configuration.pojos.Application.class));
        doNothing().when(applicationRepo).updateApplicationDetailsForAccount(anyString(), anyList());

        // Act
        putApplicationsBL.process(serverValidationInput);

        // Assert
        ArgumentCaptor<ApplicationAliases> captor = ArgumentCaptor.forClass(ApplicationAliases.class);
        verify(controllerDao, times(1)).updateApplicationAlias(captor.capture());
        ApplicationAliases capturedAlias = captor.getValue();
        assertEquals("new-mapped-app-id", capturedAlias.getDcApplicationIdentifier());
        assertEquals("Test App", capturedAlias.getCommonName());
    }

    @Test
    @DisplayName("process - Anomaly Config Update")
    void process_AnomalyConfigUpdate_Success() throws DataProcessingException, HealControlCenterException {
        // Arrange
        serverValidationInput.getMetadata().put(Constants.TIME_ZONE_TAG, timezoneBean);
        when(dateTimeUtil.getCurrentTimestampInGMT()).thenReturn(new Timestamp(System.currentTimeMillis()));
        when(controllerDao.getFullApplicationDetailsByIdentifier(anyString())).thenReturn(existingApp);
        when(accountsDao.getAccountAnomalyConfiguration(anyInt())).thenReturn(new AnomalyConfiguration());
        doNothing().when(controllerDao).updateApplication(any(ControllerBean.class));

        ApplicationAliases alias = new ApplicationAliases();
        when(controllerDao.getApplicationAliasByDcIdentifier(anyString())).thenReturn(alias);
        doNothing().when(controllerDao).updateApplicationAlias(any(ApplicationAliases.class));

        TagMappingDetails timezoneTag = new TagMappingDetails();
        when(tagsDao.getTimezoneTagMappingByObjectId(anyInt(), anyString())).thenReturn(timezoneTag);
        when(tagsDao.updateTagMappingDetails(any(TagMappingDetails.class))).thenReturn(1);

        com.heal.configuration.pojos.Application redisApp = new com.heal.configuration.pojos.Application();
        redisApp.setId(101);
        List<com.heal.configuration.pojos.Application> redisApps = new ArrayList<>(Collections.singletonList(redisApp));
        when(applicationRepo.getApplicationsForAccount(anyString())).thenReturn(redisApps);
        doNothing().when(applicationRepo).updateApplication(anyString(), any(com.heal.configuration.pojos.Application.class));
        doNothing().when(applicationRepo).updateApplicationDetailsForAccount(anyString(), anyList());

        // Act
        putApplicationsBL.process(serverValidationInput);

        // Assert
        ArgumentCaptor<ApplicationAnomalyConfiguration> captor = ArgumentCaptor.forClass(ApplicationAnomalyConfiguration.class);
        verify(controllerDao).updateApplicationAnomalyConfigurations(captor.capture());
        ApplicationAnomalyConfiguration capturedConfig = captor.getValue();
        assertEquals(101, capturedConfig.getApplicationId());
        assertTrue(capturedConfig.isLowEnable());
        assertFalse(capturedConfig.isMediumEnable());
        assertFalse(capturedConfig.isHighEnable());
    }

    @Test
    @DisplayName("process - Redis Update")
    void process_RedisUpdate_Success() throws DataProcessingException, HealControlCenterException {
        // Arrange
        serverValidationInput.getMetadata().put(Constants.TIME_ZONE_TAG, timezoneBean);
        when(dateTimeUtil.getCurrentTimestampInGMT()).thenReturn(new Timestamp(System.currentTimeMillis()));
        when(controllerDao.getFullApplicationDetailsByIdentifier(anyString())).thenReturn(existingApp);
        when(accountsDao.getAccountAnomalyConfiguration(anyInt())).thenReturn(new AnomalyConfiguration());
        doNothing().when(controllerDao).updateApplication(any(ControllerBean.class));

        ApplicationAliases alias = new ApplicationAliases();
        when(controllerDao.getApplicationAliasByDcIdentifier(anyString())).thenReturn(alias);
        doNothing().when(controllerDao).updateApplicationAlias(any(ApplicationAliases.class));

        TagMappingDetails timezoneTag = new TagMappingDetails();
        when(tagsDao.getTimezoneTagMappingByObjectId(anyInt(), anyString())).thenReturn(timezoneTag);
        when(tagsDao.updateTagMappingDetails(any(TagMappingDetails.class))).thenReturn(1);

        doNothing().when(controllerDao).updateApplicationAnomalyConfigurations(any(ApplicationAnomalyConfiguration.class));

        com.heal.configuration.pojos.Application redisApp = new com.heal.configuration.pojos.Application();
        redisApp.setId(101);
        List<com.heal.configuration.pojos.Application> redisApps = new ArrayList<>(Collections.singletonList(redisApp));
        when(applicationRepo.getApplicationsForAccount(anyString())).thenReturn(redisApps);

        // Act
        putApplicationsBL.process(serverValidationInput);

        // Assert
        ArgumentCaptor<com.heal.configuration.pojos.Application> appCaptor = ArgumentCaptor.forClass(com.heal.configuration.pojos.Application.class);
        verify(applicationRepo, times(1)).updateApplication(anyString(), appCaptor.capture());
        assertEquals("Test App_dc", appCaptor.getValue().getName());

        @SuppressWarnings("unchecked")
        ArgumentCaptor<List<com.heal.configuration.pojos.Application>> listCaptor = ArgumentCaptor.forClass(List.class);
        verify(applicationRepo, times(1)).updateApplicationDetailsForAccount(anyString(), listCaptor.capture());
        assertEquals(1, listCaptor.getValue().size());
        assertEquals("Test App_dc", listCaptor.getValue().get(0).getName());
    }
}
