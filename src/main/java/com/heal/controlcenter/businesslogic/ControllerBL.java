package com.heal.controlcenter.businesslogic;

import com.heal.controlcenter.beans.ControllerBean;
import com.heal.controlcenter.beans.ServiceBean;
import com.heal.controlcenter.beans.TagDetailsBean;
import com.heal.controlcenter.dao.mysql.AccountServiceDao;
import com.heal.controlcenter.dao.mysql.ControllerDao;
import com.heal.controlcenter.pojo.Tags;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.DateTimeUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

@Service
public class ControllerBL {

    private static final Logger logger = LoggerFactory.getLogger(ControllerBL.class);

    @Autowired
    private TagMappingBL tagMappingBL;

    @Autowired
    private AccountServiceDao accountServiceDao;

    @Autowired
    private ControllerDao controllerDao;


    /**
     * Adds a new controller or updates an existing controller based on the provided ServiceBean.
     * Handles tag mapping and sets controller properties.
     *
     * @param bean             The ServiceBean containing service/controller details.
     * @param controllerTypeId The controller type ID to be set.

     * @return The populated ControllerBean after add or update.
     */
    public ControllerBean addOrUpdateController(ServiceBean bean, int controllerTypeId) {
        String name = bean.getName();
        String identifier = bean.getIdentifier();
        int accountId = bean.getAccountId();
        String userId = bean.getUserId();
        String layer = bean.getLayer();
        List<Integer> appIds = bean.getAppIds();
        List<Tags> tags = new ArrayList<>();

        ControllerBean controllerBean = new ControllerBean();
        controllerBean.setId(bean.getId());
        controllerBean.setName(name);
        controllerBean.setIdentifier(identifier);
        controllerBean.setAccountId(accountId);
        controllerBean.setLastModifiedBy(userId);
        controllerBean.setCreatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
        controllerBean.setUpdatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
        controllerBean.setControllerTypeId(controllerTypeId);
        controllerBean.setStatus(bean.getStatus());
        controllerBean.setEnvironment(bean.getEnvironment());
        controllerBean.setPluginSuppressionInterval(0);
        controllerBean.setPluginWhitelisted(false);

        try {
            int id = accountServiceDao.addController(controllerBean);
            if (id < 0) {
                logger.error("Failed to create controller: {} for account {}", identifier, accountId);
                throw new RuntimeException("Controller creation failed");
            }

            controllerBean.setId(id);
            logger.info("Controller {} with ID {} created successfully.", identifier, id);

            if (StringUtils.hasText(layer)) {
                TagDetailsBean tagDetailsBean = accountServiceDao.getTagDetails(Constants.LAYER_TAG, accountId);
                if (tagDetailsBean != null) {
                    boolean exists = tagMappingBL.checkTagMappingExists(tagDetailsBean.getId(), id, Constants.CONTROLLER, Constants.LAYER_DEFAULT, layer, accountId);
                    if (!exists) {
                        int tagId = tagMappingBL.addTagMapping(tagDetailsBean.getId(), id, Constants.CONTROLLER,
                                Constants.LAYER_DEFAULT, layer, userId, accountId);
                        if (tagId == -1) throw new RuntimeException("Failed to map DB Layer tag");
                    }
                    tags.add(Tags.builder().name(Constants.LAYER_TAG).identifier(layer).build());
                }
            }

            // AppIds Mapping
            if (appIds != null && !appIds.isEmpty()) {
                TagDetailsBean tagDetailsBean = accountServiceDao.getTagDetails(Constants.CONTROLLER_TAG, accountId);
                if (tagDetailsBean != null) {
                    for (int appId : appIds) {
                        int tagId = tagMappingBL.addTagMapping(tagDetailsBean.getId(), appId, Constants.CONTROLLER,
                                String.valueOf(id), identifier, userId, accountId);
                        if (tagId == -1) throw new RuntimeException("Failed to map AppId " + appId);
                        tags.add(Tags.builder()
                                .name(Constants.CONTROLLER_TAG)
                                .identifier(String.valueOf(id))
                                .build());
                    }
                }
            }

            // Service Type Tag Mapping
            if (StringUtils.hasText(bean.getType())) {
                TagDetailsBean tagDetailsBean = accountServiceDao.getTagDetails(Constants.SERVICE_TYPE_TAG, accountId);
                if (tagDetailsBean != null) {
                    boolean alreadyMapped = accountServiceDao.isTagMappingPresent(
                            tagDetailsBean.getId(), id, Constants.CONTROLLER,
                            Constants.SERVICE_TYPE_DEFAULT, bean.getType(), accountId);

                    if (!alreadyMapped) {
                        int tagId = tagMappingBL.addTagMapping(tagDetailsBean.getId(), id, Constants.CONTROLLER,
                                Constants.SERVICE_TYPE_DEFAULT, bean.getType(), userId, accountId);
                        if (tagId == -1) throw new RuntimeException("Failed to map Service Type tag");
                    }
                }
            }

        } catch (Exception ex) {
            logger.error("Exception while processing controller {} for account {}", identifier, accountId, ex);
            throw new RuntimeException("Controller processing failed", ex);
        }

        return controllerBean;
    }

    public ControllerBean updateControllerDetails(ServiceBean bean, int controllerTypeId) {
        ControllerBean controllerBean = new ControllerBean();
        controllerBean.setId(bean.getId());
        controllerBean.setName(bean.getName());
        controllerBean.setIdentifier(bean.getIdentifier());
        controllerBean.setAccountId(bean.getAccountId());
        controllerBean.setLastModifiedBy(bean.getUserId());
        controllerBean.setUpdatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
        controllerBean.setControllerTypeId(controllerTypeId);
        controllerBean.setStatus(bean.getStatus());
        controllerBean.setEnvironment(bean.getEnvironment());
        controllerBean.setPluginSuppressionInterval(0);
        controllerBean.setPluginWhitelisted(false);

        try {
            int updated = controllerDao.updateController(controllerBean);
            if (updated <= 0) {
                logger.error("Failed to update controller: {} for account {}", bean.getIdentifier(), bean.getAccountId());
                throw new RuntimeException("Controller update failed");
            }
            logger.info("Controller {} with ID {} updated successfully.", bean.getIdentifier(), bean.getId());
        } catch (Exception ex) {
            logger.error("Exception while processing controller {} for account {}", bean.getIdentifier(), bean.getAccountId(), ex);
            throw new RuntimeException("Controller processing failed", ex);
        }

        return controllerBean;
    }
}
