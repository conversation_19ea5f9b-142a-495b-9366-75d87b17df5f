{"data": {"content": [{"name": "Petclinic2_App_Service", "id": 32, "identifier": "Service_App_2", "status": "1", "createdBy": "7640123a-fbde-4fe5-9812-581cd1e3a9c1", "createdOn": 1747109543000, "lastModifiedOn": 1747115927000, "lastModifiedBy": "7640123a-fbde-4fe5-9812-581cd1e3a9c1", "componentType": "NA", "application": [{"id": 30, "name": "Petclinic_2", "identifier": "Demo_App_2"}], "inbound": {"count": 1, "name": ["Petclinic2_Web_Service"]}, "outbound": {"count": 1, "name": ["Petclinic2_DB_Service"]}, "environment": null}, {"name": "Petclinic2_Web_Service", "id": 33, "identifier": "Service_Web_2", "status": "1", "createdBy": "7640123a-fbde-4fe5-9812-581cd1e3a9c1", "createdOn": 1747109564000, "lastModifiedOn": 1747115971000, "lastModifiedBy": "7640123a-fbde-4fe5-9812-581cd1e3a9c1", "componentType": "NA", "application": [{"id": 30, "name": "Petclinic_2", "identifier": "Demo_App_2"}], "inbound": {"count": 0, "name": []}, "outbound": {"count": 1, "name": ["Petclinic2_App_Service"]}, "environment": null}, {"name": "Petclinic2_DB_Service", "id": 34, "identifier": "Service_DB_2", "status": "1", "createdBy": "7640123a-fbde-4fe5-9812-581cd1e3a9c1", "createdOn": 1747109604000, "lastModifiedOn": 1747115950000, "lastModifiedBy": "7640123a-fbde-4fe5-9812-581cd1e3a9c1", "componentType": "NA", "application": [{"id": 30, "name": "Petclinic_2", "identifier": "Demo_App_2"}], "inbound": {"count": 1, "name": ["Petclinic2_App_Service"]}, "outbound": {"count": 0, "name": []}, "environment": null}, {"name": "Petclinic_Web_Service", "id": 27, "identifier": "Service_Web_1", "status": "1", "createdBy": "7640123a-fbde-4fe5-9812-581cd1e3a9c1", "createdOn": 1747108830000, "lastModifiedOn": 1747115739000, "lastModifiedBy": "7640123a-fbde-4fe5-9812-581cd1e3a9c1", "componentType": "NA", "application": [{"id": 26, "name": "Petclinic", "identifier": "Demo_App_1"}], "inbound": {"count": 0, "name": []}, "outbound": {"count": 1, "name": ["Petclinic_App_Service"]}, "environment": null}, {"name": "Petclinic_App_Service", "id": 28, "identifier": "Service_App_1", "status": "1", "createdBy": "7640123a-fbde-4fe5-9812-581cd1e3a9c1", "createdOn": 1747108848000, "lastModifiedOn": 1747115643000, "lastModifiedBy": "7640123a-fbde-4fe5-9812-581cd1e3a9c1", "componentType": "NA", "application": [{"id": 26, "name": "Petclinic", "identifier": "Demo_App_1"}], "inbound": {"count": 1, "name": ["Petclinic_Web_Service"]}, "outbound": {"count": 1, "name": ["Petclinic_DB_Service"]}, "environment": null}, {"name": "Petclinic_DB_Service", "id": 29, "identifier": "Service_DB_1", "status": "1", "createdBy": "7640123a-fbde-4fe5-9812-581cd1e3a9c1", "createdOn": 1747108937000, "lastModifiedOn": 1747115723000, "lastModifiedBy": "7640123a-fbde-4fe5-9812-581cd1e3a9c1", "componentType": "NA", "application": [{"id": 26, "name": "Petclinic", "identifier": "Demo_App_1"}], "inbound": {"count": 1, "name": ["Petclinic_App_Service"]}, "outbound": {"count": 0, "name": []}, "environment": null}], "pageable": {"pageNumber": 0, "pageSize": 20, "sort": {"sorted": false, "empty": true, "unsorted": true}, "offset": 0, "paged": true, "unpaged": false}, "totalElements": 65, "totalPages": 4, "last": false, "size": 20, "number": 0, "sort": {"sorted": false, "empty": true, "unsorted": true}, "numberOfElements": 20, "first": true, "empty": false}, "message": "Services fetched successfully.", "responseStatus": "OK"}