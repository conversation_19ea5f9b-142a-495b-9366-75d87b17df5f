#!/usr/bin/env node

const https = require('https');
const http = require('http');

const API_BASE = 'http://localhost:8080/v2.0/api';
const ORIGIN = 'http://localhost:3000';

console.log('🔍 CORS Debug Tool');
console.log('==================');

async function testCors() {
    console.log('\n1. Testing OPTIONS request (preflight)...');
    await testOptions();
    
    console.log('\n2. Testing simple GET request...');
    await testGet();
    
    console.log('\n3. Testing complex GET request...');
    await testComplexGet();
}

function testOptions() {
    return new Promise((resolve) => {
        const options = {
            hostname: 'localhost',
            port: 8080,
            path: '/v2.0/api/accounts',
            method: 'OPTIONS',
            headers: {
                'Origin': ORIGIN,
                'Access-Control-Request-Method': 'GET',
                'Access-Control-Request-Headers': 'Content-Type,Authorization'
            }
        };

        const req = http.request(options, (res) => {
            console.log(`   Status: ${res.statusCode}`);
            console.log('   CORS Headers:');
            
            Object.keys(res.headers).forEach(key => {
                if (key.startsWith('access-control')) {
                    console.log(`     ${key}: ${res.headers[key]}`);
                }
            });
            
            if (res.statusCode === 200) {
                console.log('   ✅ OPTIONS request successful');
            } else {
                console.log('   ❌ OPTIONS request failed');
            }
            
            resolve();
        });

        req.on('error', (e) => {
            console.log(`   ❌ Error: ${e.message}`);
            resolve();
        });

        req.end();
    });
}

function testGet() {
    return new Promise((resolve) => {
        const options = {
            hostname: 'localhost',
            port: 8080,
            path: '/v2.0/api/timezones',
            method: 'GET',
            headers: {
                'Origin': ORIGIN,
                'Accept': 'application/json'
            }
        };

        const req = http.request(options, (res) => {
            console.log(`   Status: ${res.statusCode}`);
            
            const corsOrigin = res.headers['access-control-allow-origin'];
            if (corsOrigin) {
                console.log(`   ✅ CORS Origin: ${corsOrigin}`);
            } else {
                console.log('   ❌ No CORS Origin header');
            }
            
            resolve();
        });

        req.on('error', (e) => {
            console.log(`   ❌ Error: ${e.message}`);
            resolve();
        });

        req.end();
    });
}

function testComplexGet() {
    return new Promise((resolve) => {
        const options = {
            hostname: 'localhost',
            port: 8080,
            path: '/v2.0/api/accounts',
            method: 'GET',
            headers: {
                'Origin': ORIGIN,
                'Content-Type': 'application/json',
                'Authorization': 'Bearer test-token'
            }
        };

        const req = http.request(options, (res) => {
            console.log(`   Status: ${res.statusCode}`);
            
            const corsOrigin = res.headers['access-control-allow-origin'];
            if (corsOrigin) {
                console.log(`   ✅ CORS Origin: ${corsOrigin}`);
            } else {
                console.log('   ❌ No CORS Origin header');
            }
            
            resolve();
        });

        req.on('error', (e) => {
            console.log(`   ❌ Error: ${e.message}`);
            resolve();
        });

        req.end();
    });
}

// Run tests
testCors().then(() => {
    console.log('\n🎯 Debug complete!');
    console.log('\nIf you see ❌ errors:');
    console.log('1. Check if backend is running on localhost:8080');
    console.log('2. Verify CORS configuration in HealWebMvcConfiguration');
    console.log('3. Check application logs for errors');
});
