package com.heal.controlcenter.businesslogic;

import com.heal.controlcenter.beans.UserDetailsBean;
import com.heal.controlcenter.beans.UserNotificationDetailsBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.NotificationPreferencesDataDao;
import com.heal.controlcenter.dao.mysql.UserDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.util.ClientValidationUtils;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.ServerValidationUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class UserDetailsBL implements BusinessLogic<String, String, List<UserDetailsBean>> {

    @Autowired
    UserDao userDao;
    @Autowired
    NotificationPreferencesDataDao notificationPreferencesDataDao;
    @Autowired
    ClientValidationUtils clientValidationUtils;
    @Autowired
    ServerValidationUtils serverValidationUtils;

    @Override
    public UtilityBean<String> clientValidation(String requestBody, String... requestParams) throws ClientException {
        String authKey = requestParams[0];
        clientValidationUtils.authKeyValidation(authKey);

        return UtilityBean.<String>builder()
                .build();
    }

    @Override
    public String serverValidation(UtilityBean<String> utilityBean) throws ServerException {
        String authKey = utilityBean.getRequestParams().get(Constants.AUTH_KEY);
        serverValidationUtils.authKeyValidation(authKey);
        return null;
    }

    @Override
    public List<UserDetailsBean> process(String bean) throws DataProcessingException {
        try {
            log.info("fetching users detail");
            List<UserDetailsBean> userDetailsBeanList = userDao.getNonSuperUsers();

            if (userDetailsBeanList == null || userDetailsBeanList.isEmpty()) {
                log.info("User list fetched from schema is empty.");
                return Collections.emptyList();
            }

            Map<String, UserNotificationDetailsBean> userNotificationDetailsBeanMap = notificationPreferencesDataDao.getEmailSmsForensicNotificationStatusForUsers();

            return userDetailsBeanList.stream().sorted(Comparator.comparing(UserDetailsBean::getUpdatedOn))
                    .peek(user -> {
                        UserNotificationDetailsBean detailsBean = null;
                        if (userNotificationDetailsBeanMap.containsKey(user.getUserId())) {
                            detailsBean = userNotificationDetailsBeanMap.get(user.getUserId());
                        }
                        String updatedBy = this.getUserName(user.getUpdatedBy());
                        user.setUpdatedBy(updatedBy);
                        user.setEmailNotification(detailsBean == null ? 0 : detailsBean.getEmailEnabled());
                        user.setSmsNotification(detailsBean == null ? 0 : detailsBean.getSmsEnabled());
                        user.setForensicNotification(detailsBean == null ? 0 : detailsBean.getForensicEnabled());
                    }).collect(Collectors.toList());
        } catch (ControlCenterException e) {
            log.error("Error occurred while forming response object", e);
            throw new DataProcessingException(e.getMessage());
        }
    }

    private String getUserName(String userId) {
        try {
            return userDao.getUsernameFromIdentifier(userId);
        } catch (ControlCenterException e) {
            log.error("Error occurred while getting user name for identifier [{}]", userId, e);
            return null;
        }
    }
}
