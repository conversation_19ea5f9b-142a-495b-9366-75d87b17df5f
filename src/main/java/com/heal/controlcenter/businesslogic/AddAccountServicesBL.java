package com.heal.controlcenter.businesslogic;


import com.appnomic.appsone.common.util.StringUtils;
import com.heal.configuration.pojos.*;
import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.dao.mysql.AccountServiceDao;
import com.heal.controlcenter.dao.mysql.ControllerDao;
import com.heal.controlcenter.dao.mysql.MasterDataDao;
import com.heal.controlcenter.dao.redis.AccountRepo;
import com.heal.controlcenter.dao.redis.ApplicationRepo;
import com.heal.controlcenter.dao.redis.MasterDataRepo;
import com.heal.controlcenter.dao.redis.ServiceRepo;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.RulesHelperPojo;
import com.heal.controlcenter.pojo.ServicePojo;
import com.heal.controlcenter.util.*;
import com.heal.controlcenter.aop.LogExecutionAnnotation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AddAccountServicesBL implements BusinessLogic<List<ServicePojo>, UtilityBean<List<ServiceBean>>, List<IdPojo>> {
    private final ServiceRepo serviceRepo;
    private final AccountRepo accountRepo;
    private final MasterDataDao masterDataDao;
    private final ControllerDao controllerDao;
    private final ClientValidationUtils clientValidationUtils;
    private final ServerValidationUtils serverValidationUtils;
    private final AccountServiceDao accountServiceDao;
    private final ControllerBL controllerBL;
    private final TagMappingBL tagMappingBL;
    private final MasterDataRepo masterDataRepo;
    private final ApplicationRepo applicationRepo;

    public AddAccountServicesBL(ServiceRepo serviceRepo, AccountRepo accountRepo, MasterDataDao masterDataDao,
                                ControllerDao controllerDao, ClientValidationUtils clientValidationUtils,
                                ServerValidationUtils serverValidationUtils, AccountServiceDao accountServiceDao,
                                ControllerBL controllerBL, TagMappingBL tagMappingBL, MasterDataRepo masterDataRepo,
                                ApplicationRepo applicationRepo) {
        this.serviceRepo = serviceRepo;
        this.accountRepo = accountRepo;
        this.masterDataDao = masterDataDao;
        this.controllerDao = controllerDao;
        this.clientValidationUtils = clientValidationUtils;
        this.serverValidationUtils = serverValidationUtils;
        this.accountServiceDao = accountServiceDao;
        this.controllerBL = controllerBL;
        this.tagMappingBL = tagMappingBL;
        this.masterDataRepo = masterDataRepo;
        this.applicationRepo = applicationRepo;
    }

    /**
     * Validates the client-side input for adding account services.
     * Checks authentication key, account identifier, and service payload.
     *
     * @param requestBody   List of ServicePojo objects from the request payload.
     * @param requestParams Authorization key and account identifier.
     * @return UtilityBean containing request data and metadata.
     * @throws ClientException if validation fails.
     */
    @LogExecutionAnnotation
    public UtilityBean<List<ServicePojo>> clientValidation(List<ServicePojo> requestBody, String... requestParams) throws ClientException {
        String accountIdentifier = requestParams[0];
        clientValidationUtils.accountIdentifierValidation(accountIdentifier);
        validateServiceRequestPayload(requestBody);
        Map<String, String> error = new HashMap<>();
        for (ServicePojo servicePojo : requestBody) {
            Map<String, String> validationError = servicePojo.validate();
            if (validationError != null && !validationError.isEmpty()) {
                error.putAll(validationError);
            }
        }
        if (!error.isEmpty()) {
            String err = error.toString();
            log.error(err);
            throw new ClientException(err);
        }
        HashMap<String, String> requestParamsMap = CommonUtils.buildRequestParams(null, accountIdentifier);
        log.debug("[clientValidation] Validation successful for accountIdentifier: {}", accountIdentifier);
        return UtilityBean.<List<ServicePojo>>builder()
                .requestParams(requestParamsMap)
                .pojoObject(requestBody)
                .metadata(new HashMap<>())
                .build();
    }

    /**
     * Validates server-side constraints for adding account services.
     * Ensures the account and controller configuration are valid.
     *
     * @param utilityBean UtilityBean containing request parameters and service pojo list.
     * @return UtilityBean enriched with metadata and validated ServiceBeans.
     * @throws ServerException if validation fails or configuration is missing.
     */
    @Override
    @LogExecutionAnnotation
    public UtilityBean<List<ServiceBean>> serverValidation(UtilityBean<List<ServicePojo>> utilityBean) throws ServerException {
        try {
            String userId = (String) utilityBean.getMetadata().get(Constants.USER_ID_KEY);
            String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
            Account account = serverValidationUtils.accountValidation(accountIdentifier);
            int accountId = account.getId();
            log.debug("[serverValidation] Validated account: {} (ID: {})", accountIdentifier, accountId);

            // Fetch required configuration details once
            ViewTypesBean serviceControllerType = masterDataDao.getViewTypesFromMstTypeAndSubTypeName(
                    Constants.CONTROLLER_TYPE_NAME_DEFAULT,
                    Constants.SERVICES_CONTROLLER_TYPE);
            if (serviceControllerType == null) {
                throw new ServerException("Critical configuration error: 'Service' controller type not found.");
            }
            int serviceEntryTypeId = serviceControllerType.getSubTypeId();

            TagDetailsBean controllerTagDetails = masterDataDao.findTagDetailsByName(Constants.CONTROLLER_TAG, String.valueOf(accountId));
            if (controllerTagDetails == null) {
                throw new ServerException("Critical configuration error: 'Controller' tag details not found for this account.");
            }
            int controllerTagId = controllerTagDetails.getId();

            List<ServiceBean> serviceBeanList = new ArrayList<>();
            List<ServicePojo> services = utilityBean.getPojoObject();

            for (ServicePojo serviceEntry : services) {
                String originalIdentifier = StringUtils.isEmpty(serviceEntry.getIdentifier())
                        ? UUID.randomUUID().toString()
                        : serviceEntry.getIdentifier().trim();
                String originalName = serviceEntry.getName().trim();
                String envSuffix = "";
                if (!StringUtils.isEmpty(serviceEntry.getEnvironment())) {
                    envSuffix = "_" + serviceEntry.getEnvironment().trim().toUpperCase();
                }
                String suffixedIdentifier = originalIdentifier + envSuffix;
                String suffixedName = originalName + envSuffix;

                String type = StringUtils.isEmpty(serviceEntry.getType())
                        ? Constants.NON_KUBERNETES
                        : serviceEntry.getType();
                String layer = serviceEntry.getLayer() == null ? null : serviceEntry.getLayer().trim();
                List<Integer> appIds = new ArrayList<>();
                if (!CollectionUtils.isEmpty(serviceEntry.getAppIdentifiers())) {
                    for (String appIdentifier : serviceEntry.getAppIdentifiers()) {
                        ControllerBean appController = controllerDao.getControllerByIdentifierOrName(appIdentifier.trim(), null);
                        if (appController == null) {
                            throw new ServerException("App Identifier '" + appIdentifier + "' does not exist.");
                        }
                        appIds.add(appController.getId());
                    }
                }
                ControllerBean existingController = controllerDao.getControllerByIdentifierOrName(suffixedIdentifier, null);
                if (existingController != null) {
                    Integer applicationId = controllerDao.getControllerApplicationId(existingController.getId(), controllerTagId, accountId);

                    if (applicationId != null && existingController.getStatus() == 0 && existingController.getAccountId() == accountId && appIds.contains(applicationId)) {
                        log.info("Service identifier '{}' exists but is inactive. Reactivating...", suffixedIdentifier);
                    } else if (existingController.getStatus() == 1 && serviceEntry.getLinkedEnvironment() != null) {
                        log.info("Service identifier '{}' exists and a 'linkedEnvironment' is provided. Proceeding as a linking operation.", suffixedIdentifier);
                    } else {
                        throw new ServerException("Service identifier '" + suffixedIdentifier + "' already exists.");
                    }
                } else {
                    // 4. Check for existing service by the suffixed name to prevent duplicates
                    existingController = controllerDao.getControllerByIdentifierOrName(null, suffixedName);
                    if (existingController != null) {
                        Integer applicationId = controllerDao.getControllerApplicationId(existingController.getId(), controllerTagId, accountId);
                        if (applicationId != null && existingController.getControllerTypeId() == serviceEntryTypeId && existingController.getStatus() == 1 &&
                                existingController.getAccountId() == accountId && appIds.contains(applicationId)) {
                            throw new ServerException("Service name '" + suffixedName + "' already exists.");
                        }
                    }
                }
                //  Correctly extract data from the nested 'linkedEnvironment' object
                String linkedIdentifier = null;
                String mappedId = null;
                if (serviceEntry.getLinkedEnvironment() != null) {
                    linkedIdentifier = serviceEntry.getLinkedEnvironment().getAccount();
                    mappedId = serviceEntry.getLinkedEnvironment().getMappedServiceIdentifiers();
                }

                // 6. Build the ServiceBean with the correct data
                serviceBeanList.add(ServiceBean.builder()
                        .name(suffixedName)
                        .identifier(suffixedIdentifier)
                        .layer(layer)
                        .appIds(appIds)
                        .appIdentifier(serviceEntry.getAppIdentifiers().get(0))
                        .accountId(accountId)
                        .accountIdentifier(accountIdentifier)
                        .accountId(accountId)
                        .userId(userId)
                        .type(type)
                        .status(1)
                        .entryPointService(serviceEntry.getIsEntryPointService() == 1)
                        .environment(serviceEntry.getEnvironment())
                        .serviceGroup(serviceEntry.getServiceGroup())
                        .mappedServiceIdentifier(mappedId)
                        .linkedIdentifier(linkedIdentifier)
                        .build());
            }

            Map<String, Object> metadata = utilityBean.getMetadata() != null ? utilityBean.getMetadata() : new HashMap<>();
            metadata.put(Constants.SERVICE_BEAN_LIST, serviceBeanList);
            metadata.put(Constants.USER_ID_KEY, userId);

            return UtilityBean.<List<ServiceBean>>builder()
                    .pojoObject(serviceBeanList)
                    .requestParams(utilityBean.getRequestParams())
                    .metadata(metadata)
                    .build();
        } catch (Exception e) {
            log.error("Error while validating server-side constraints for adding account services. Details: ", e);
            throw new ServerException("Failed to validate server-side constraints for adding account services.");
        }
    }

    /**
     * Processes the creation or update of account services.
     * Handles DB persistence, service linking, and Redis updates.
     *
     * @param utilityBean Validated UtilityBean with service data and metadata.
     * @return List of IdPojo representing the processed services.
     * @throws DataProcessingException if any error occurs during processing.
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogExecutionAnnotation
    public List<IdPojo> process(UtilityBean<List<ServiceBean>> utilityBean) throws DataProcessingException {
        try {
            List<ServiceBean> serviceBeanList = utilityBean.getPojoObject();
            List<IdPojo> idPojoList = new ArrayList<>();
            for (ServiceBean serviceBean : serviceBeanList) {
                log.debug("[process] Adding service: {} (identifier: {})", serviceBean.getName(), serviceBean.getIdentifier());
                IdPojo idPojo = addService(serviceBean);
                idPojoList.add(idPojo);
                handleLinkedServices(serviceBean);
            }
            addServiceToRedis(serviceBeanList, idPojoList);
            log.info("[process] Successfully processed all services.");
            return idPojoList;
        } catch (Exception e) {
            log.error("Error while processing adding account services. Details: ", e);
            throw new DataProcessingException("Failed to process adding account services.");
        }
    }

    private void validateServiceRequestPayload(List<ServicePojo> services) throws ClientException {
        if (CollectionUtils.isEmpty(services)) {
            log.error("The list of services in the request body cannot be empty.");
            throw new ClientException("The list of services in the request body cannot be empty.");
        }

        Set<String> seenNames = new HashSet<>();
        for (ServicePojo service : services) {
            String name = service.getName().trim().toLowerCase();
            if (!seenNames.add(name)) {
                log.error("Duplicate service name '{}' found in the request payload.", service.getName());
                throw new ClientException("Duplicate service name '" + service.getName() + "' found in the request payload.");
            }
        }

        log.info("Service payload passed all client-side business validations.");
    }


    /**
     * Adds a new service or updates an existing service in the system.
     *
     * @param bean The service data to be added or updated.
     * @return IdPojo containing the ID and name of the added or updated service.
     * @throws HealControlCenterException if any error occurs during the operation.
     */
    public IdPojo addService(ServiceBean bean) throws HealControlCenterException, DataProcessingException {
        ViewTypesBean serviceControllerType;
        try {
            serviceControllerType = masterDataDao.getViewTypesFromMstTypeAndSubTypeName(
                    Constants.CONTROLLER_TYPE_NAME_DEFAULT,
                    Constants.SERVICES_CONTROLLER_TYPE);
        } catch (HealControlCenterException e) {
            log.error("A critical error occurred while fetching service controller type details.", e);
            throw new HealControlCenterException("A critical error occurred while fetching service controller type details: " + e.getMessage());
        }

        if (serviceControllerType == null) {
            String err = "Configuration error: '" + Constants.SERVICES_CONTROLLER_TYPE + "' not found in 'view_types' table.";
            log.error(err);
            throw new HealControlCenterException(err);
        }
        ControllerBean controllerBean = controllerBL.addOrUpdateController(bean, serviceControllerType.getSubTypeId());

        if (controllerBean.getId() == -1) {
            String err = "Unable to add or update the service in the controller table.";
            log.error(err);
            throw new HealControlCenterException(err);
        }
        bean.setId(controllerBean.getId());
        if (bean.getServiceGroup() != null && !StringUtils.isEmpty(bean.getServiceGroup().getIdentifier())) {
            log.info("Service group identifier provided. Attempting to link service [id: {}]", bean.getId());
            ServiceGroupBean existingGroup = accountServiceDao.findServiceGroupByIdentifier(
                    bean.getServiceGroup().getIdentifier(), bean.getAccountId());

            if (existingGroup != null) {
                int result = accountServiceDao.createServiceGroupMapping(bean.getId(), existingGroup.getId(), bean.getUserId());
                if (result <= 0) {
                    String err = String.format("Failed to map service [id: %d] to service group [id: %d]", bean.getId(), existingGroup.getId());
                    log.error(err);
                    throw new HealControlCenterException(err);
                }
                log.info("Successfully mapped service [id: {}] to existing service group [id: {}]", bean.getId(), existingGroup.getId());
            } else {
                String errorMessage = "Service group with identifier [" + bean.getServiceGroup().getIdentifier() + "] not found for this account.";
                log.error(errorMessage);
                throw new HealControlCenterException(errorMessage);
            }
        }
        if (bean.isEntryPointService()) {
            TagDetailsBean entryPointTagDetails = accountServiceDao.getTagDetails(Constants.ENTRY_POINT, bean.getAccountId());
            if (entryPointTagDetails == null) {
                String err = "Unable to fetch tag details for Entry-Point.";
                log.error(err);
                throw new HealControlCenterException(err);
            }

            boolean alreadyMapped;
            try {
                alreadyMapped = accountServiceDao.isTagMappingPresent(
                        entryPointTagDetails.getId(),
                        controllerBean.getId(),
                        Constants.CONTROLLER,
                        "Type",
                        "1",
                        bean.getAccountId());
            } catch (Exception e) {
                log.error("Error while checking existing entry-point tag mapping", e);
                alreadyMapped = false;
            }


            if (!alreadyMapped) {
                int id = tagMappingBL.addTagMapping(entryPointTagDetails.getId(), controllerBean.getId(),
                        Constants.CONTROLLER, "Type", "1", bean.getUserId(), bean.getAccountId());

                if (id == -1) {
                    String err = "Unable to save entry point tag mapping details.";
                    log.error(err);
                    throw new HealControlCenterException(err);
                }
            } else {
                log.info("Entry point tag mapping already exists. Skipping insert.");
            }
        }
        return IdPojo.builder()
                .id(controllerBean.getId())
                .name(bean.getName())
                .build();
    }


    /**
     * Builds rules for the given service by fetching rule helper data and view types.
     *
     * @param serviceBean The service for which rules are to be built.
     * @return List of Rule objects for the service.
     */
    public List<Rule> buildRulesForService(ServiceBean serviceBean) {
        log.info("Populating rules for the service {}", serviceBean.getName());

        List<RulesHelperPojo> rulesHelperList = accountServiceDao.getRulesHelperPojo(serviceBean.getAccountId(), serviceBean.getId());

        if (rulesHelperList.isEmpty()) return Collections.emptyList();

        List<ViewTypes> viewTypesList = masterDataRepo.getViewTypes();

        return rulesHelperList.stream().map(rule -> {
            RegexTypeDetail regexDetail = RegexTypeDetail.builder()
                    .id(rule.getTcpId())
                    .initialPattern(rule.getTcpInitialPattern())
                    .endPattern(rule.getTcpLastPattern())
                    .length(rule.getTcpLength())
                    .build();

            List<PairData> pairDataList = accountServiceDao.getDataBeans(rule.getId(), rule.getHttpId());

            RequestTypeDetail requestTypeDetail = RequestTypeDetail.builder()
                    .id(rule.getHttpId())
                    .firstUriSegments(rule.getHttpFirstUriSegments())
                    .lastUriSegments(rule.getHttpLastUriSegments())
                    .completeURI(rule.getHttpCompleteURI() != 0)
                    .payloadTypeId(rule.getHttpPayloadTypeId())
                    .payloadTypeName(
                            viewTypesList.stream()
                                    .filter(vt -> vt.getSubTypeId() == rule.getHttpPayloadTypeId())
                                    .map(ViewTypes::getSubTypeName)
                                    .findFirst()
                                    .orElse(""))
                    .pairData(pairDataList)
                    .build();

            return Rule.builder()
                    .id(rule.getId())
                    .name(rule.getName())
                    .monitoringEnabled(rule.getEnabled() != 0)
                    .discoveryEnabled(rule.getDiscoveryEnabled() == 1)
                    .order(rule.getOrder())
                    .ruleTypeId(rule.getRuleTypeId())
                    .ruleType(
                            viewTypesList.stream()
                                    .filter(vt -> vt.getSubTypeId() == rule.getRuleTypeId())
                                    .map(ViewTypes::getSubTypeName)
                                    .findFirst()
                                    .orElse(""))
                    .isDefault(rule.getIsDefault())
                    .maxTags(rule.getMaxTags())
                    .regexTypeDetails(regexDetail)
                    .requestTypeDetails(requestTypeDetail)
                    .transactionGroups(new ArrayList<>()) // empty for now
                    .build();
        }).collect(Collectors.toList());
    }

    /**
     * Adds application mappings to the given service in Redis based on appIds.
     *
     * @param serviceBean The service to update.
     */
    public void addApplicationsToService(ServiceBean serviceBean) {
        List<BasicEntity> allApplications = accountRepo.getAllApplications(serviceBean.getAccountIdentifier());
        List<BasicEntity> filteredApps = new ArrayList<>();

        // Filter by appIds present in serviceBean
        for (Integer appId : serviceBean.getAppIds()) {
            allApplications.stream()
                    .filter(app -> app.getId() == appId)
                    .findAny()
                    .ifPresent(filteredApps::add);
        }

        // Update service with filtered applications
        serviceRepo.updateApplicationsByServiceIdentifier(
                serviceBean.getAccountIdentifier(),
                serviceBean.getIdentifier(),
                filteredApps
        );
    }

    /**
     * Maps the given service to its applications in Redis.
     *
     * @param serviceBean        The service to map.
     * @param serviceBasicEntity The BasicEntity representing the service.
     */
    public void addServiceMappedToApplications(ServiceBean serviceBean, BasicEntity serviceBasicEntity) {
        // FIX: Use CollectionUtils.isEmpty(), which is null-safe.
        // This prevents the NullPointerException if no linked environment was provided.
        if (StringUtils.isEmpty(serviceBean.getMappedServiceIdentifier())) {
            log.debug("No mapped service identifiers found for service [{}]. Skipping Redis mapping.", serviceBean.getIdentifier());
            return;
        }
        String appIdentifier = serviceBean.getMappedServiceIdentifier();
            List<BasicEntity> mappedServices = applicationRepo.getServicesMappedToApplication(
                    serviceBean.getAccountIdentifier(), appIdentifier
            );

            mappedServices.add(serviceBasicEntity); // Add the current service
            applicationRepo.updateServiceApplication(
                    serviceBean.getAccountIdentifier(), appIdentifier, mappedServices
            );

    }

    /**
     * Adds a new service to the list of services in Redis and returns the BasicEntity.
     *
     * @param serviceRepo      The ServiceRepo instance.
     * @param serviceBean      The service to add.
     * @param serviceDetailMap Map of service names to IdPojo objects.
     * @return The BasicEntity representing the new service.
     */
    public BasicEntity addNewServiceToList(ServiceRepo serviceRepo,
                                           ServiceBean serviceBean,
                                           Map<String, IdPojo> serviceDetailMap) {
        // Fetch existing services from Redis
        List<BasicEntity> allServicesDetails = serviceRepo.getAllServicesDetails(serviceBean.getAccountIdentifier());

        // Build BasicEntity object
        IdPojo idPojo = serviceDetailMap.get(serviceBean.getName());
        if (idPojo == null) {
            throw new IllegalArgumentException("Service name [" + serviceBean.getName() + "] not found in serviceDetailMap.");
        }
        BasicEntity newService = BasicEntity.builder()
                .id(idPojo.getId())
                .name(serviceBean.getName())
                .identifier(serviceBean.getIdentifier())
                .status(serviceBean.getStatus())
                .createdTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                .updatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                .lastModifiedBy(serviceBean.getUserId())
                .build();
        allServicesDetails.add(newService);
        serviceRepo.updateServiceConfiguration(serviceBean.getAccountIdentifier(), allServicesDetails);

        return newService;
    }

    /**
     * Builds a default ServiceConfiguration for the given service.
     *
     * @param serviceBean The service for which configuration is built.
     * @return ServiceConfiguration object.
     */
    public ServiceConfiguration buildServiceConfigurations(ServiceBean serviceBean) {
        String currentTime = DateTimeUtil.getTimeInGMT(System.currentTimeMillis());

        return ServiceConfiguration.builder()
                .lastModifiedBy(serviceBean.getUserId())
                .createdTime(currentTime)
                .updatedTime(currentTime)
                .lowEnable(true)
                .mediumEnable(true)
                .highEnable(true)
                .build();
    }

    public List<Tags> buildTags(ServiceBean serviceBean) {
        List<Tags> tags = new ArrayList<>();

        if (serviceBean.getLayer() != null && !serviceBean.getLayer().trim().isEmpty()) {
            tags.add(Tags.builder()
                    .type(Constants.LAYER_TAG)
                    .value(serviceBean.getLayer().trim())
                    .key(Constants.LAYER_DEFAULT)
                    .build());
        }
        if (serviceBean.getType() != null && !serviceBean.getType().trim().isEmpty()) {
            tags.add(Tags.builder()
                    .type(Constants.SERVICE_TYPE_TAG)
                    .value(serviceBean.getType().trim())
                    .key(Constants.DEFAULT_TAG_VALUE)
                    .build());
        }
        if (serviceBean.isEntryPointService()) {
            tags.add(Tags.builder()
                    .type(Constants.ENTRY_POINT)
                    .value("1")
                    .key(Constants.LAYER_DEFAULT)
                    .build());
        }
        return tags;
    }

   public  void addServiceToRedis(List<ServiceBean> serviceBeanList, List<IdPojo> idPojoList) throws HealControlCenterException {
        Map<String, IdPojo> serviceDetailMap = idPojoList.stream()
                .collect(Collectors.toMap(IdPojo::getName, Function.identity()));

        for (ServiceBean serviceBean : serviceBeanList) {
            ServiceConfiguration config ;
            if (!CollectionUtils.isEmpty(serviceBean.getAppIds())) {
                int applicationId = serviceBean.getAppIds().get(0);
                int serviceId = serviceDetailMap.get(serviceBean.getName()).getId();

                accountServiceDao.copyAnomalySuppressionFromAppToService(applicationId, serviceId);

                List<PersistenceSuppressionConfiguration> suppressionConfigs =
                        accountServiceDao.getAnomalySuppressionForService(serviceId);

                config = buildServiceConfigurations(serviceBean);
                config.setPersistenceSuppressionConfigurations(suppressionConfigs);
            } else {
                log.warn("Skipping service [{}] as no application IDs were provided for anomaly suppression copy.", serviceBean.getName());
                continue;
            }

            List<Tags> tags = buildTags(serviceBean);

            com.heal.configuration.pojos.Service serviceObject = com.heal.configuration.pojos.Service.builder()
                    .id(serviceDetailMap.get(serviceBean.getName()).getId())
                    .status(serviceBean.getStatus())
                    .createdTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                    .updatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                    .name(serviceBean.getName())
                    .type(serviceBean.getType())
                    .lastModifiedBy(serviceBean.getUserId())
                    .serviceConfiguration(config)
                    .tags(tags)
                    .build();

            List<Rule> rules = buildRulesForService(serviceBean);

            serviceRepo.updateServiceRules(
                    serviceBean.getAccountIdentifier(),
                    serviceBean.getIdentifier(),
                    rules
            );

            serviceRepo.updateServiceConfigurationByServiceIdentifier(
                    serviceBean.getAccountIdentifier(),
                    String.valueOf(serviceDetailMap.get(serviceBean.getName()).getId()),
                    serviceObject
            );

            BasicEntity basicEntityObject = addNewServiceToList(serviceRepo, serviceBean, serviceDetailMap);
            addServiceMappedToApplications(serviceBean, basicEntityObject);
            addApplicationsToService(serviceBean);
        }
    }

    /**
     * Handles the creation of a DC/DR service link in the 'service_aliases' table.
     * This method validates that the target service exists and performs a single, complete
     * insertion with both DC and DR identifiers. It is designed to be called once per pair.
     *
     * @param serviceBean The bean containing details of the service being created and its link.
     * @throws HealControlCenterException if validation fails or the database operation fails, ensuring a transaction rollback.
     */
   public void handleLinkedServices(ServiceBean serviceBean) throws HealControlCenterException {
        // 1. Only proceed if this is a linking operation
        if (serviceBean.getLinkedIdentifier() == null || StringUtils.isEmpty(serviceBean.getMappedServiceIdentifier())) {
            log.info("No linked environment specified for service [{}]. Skipping service alias logic.", serviceBean.getIdentifier());
            return;
        }

        // 2. Get necessary data, including the crucial unsuffixed commonName
        String currentEnv = serviceBean.getEnvironment();
        String currentIdentifier = serviceBean.getIdentifier();
        String commonName = serviceBean.getName(); // Assumes 'commonName' is populated in serverValidation
        if (StringUtils.isEmpty(commonName)) {
            throw new HealControlCenterException("Cannot process service link: commonName is missing from the ServiceBean.");
        }
        String updatedTime = DateTimeUtil.getTimeInGMT(System.currentTimeMillis());

        // 3. Validate that the service we are trying to link to actually exists.
        // Assuming a one-to-one mapping, so we get the first identifier.
        String mappedIdentifier = serviceBean.getMappedServiceIdentifier();
        ControllerBean linkedController = controllerDao.getControllerByIdentifierOrName(mappedIdentifier, null);
        if (linkedController == null) {
            String err = String.format("Cannot create link: The specified service to link with identifier [%s] does not exist.", mappedIdentifier);
            log.error(err);
            throw new HealControlCenterException(err);
        }

        // 4. Check if a link for this common name already exists to prevent duplicates.
        ServiceAliases existingAlias = accountServiceDao.getLinkedServiceByCommonName(commonName);
        if (existingAlias != null) {
            log.warn("A service alias for common name [{}] already exists. Skipping insertion to avoid duplicates.", commonName);
            return;
        }

        // 5. Determine both DC and DR identifiers for a single, complete insertion.
        String dcIdentifier;
        String drIdentifier;

        if ("DC".equalsIgnoreCase(currentEnv)) {
            dcIdentifier = currentIdentifier;
            drIdentifier = mappedIdentifier;
        } else if ("DR".equalsIgnoreCase(currentEnv)) {
            drIdentifier = currentIdentifier;
            dcIdentifier = mappedIdentifier;
        } else {
            log.warn("Unknown environment [{}]. Skipping alias creation for common name [{}].", currentEnv, commonName);
            return;
        }

        // 6. Insert the new alias with both identifiers populated.
        log.info("Attempting to insert new service alias for common name [{}]", commonName);
        int result = accountServiceDao.insertLinkedService(
                commonName,
                dcIdentifier,
                drIdentifier,
                serviceBean.getUserId(),
                updatedTime,
                updatedTime,
                serviceBean.getStatus()
        );

        if (result > 0) {
            log.info("Successfully inserted new service_alias for [{}] with DC=[{}] and DR=[{}]", commonName, dcIdentifier, drIdentifier);
        } else {
            // Throw an exception to trigger the @Transactional rollback.
            String err = "Failed to insert service_alias for common name [" + commonName + "]";
            log.error(err);
            throw new HealControlCenterException(err);
        }
    }

}
