//package com.heal.controlcenter.controller;
//
//import com.heal.controlcenter.beans.UtilityBean;
//import com.heal.controlcenter.businesslogic.GetServiceStaticThresholdBL;
//import com.heal.controlcenter.businesslogic.PostServiceStaticThresholdBL;
//import com.heal.controlcenter.exception.ClientException;
//import com.heal.controlcenter.exception.DataProcessingException;
//import com.heal.controlcenter.exception.ServerException;
//import com.heal.controlcenter.pojo.ErrorResponsePojo;
//import com.heal.controlcenter.pojo.GetApplications;
//import com.heal.controlcenter.pojo.ResponsePojo;
//import com.heal.controlcenter.pojo.StaticThresholdRules;
//import com.heal.controlcenter.util.JsonFileParser;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.http.HttpStatus;
//import org.springframework.http.ResponseEntity;
//import org.springframework.web.bind.annotation.*;
//import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.v3.oas.annotations.media.Content;
//import io.swagger.v3.oas.annotations.media.Schema;
//import io.swagger.v3.oas.annotations.responses.ApiResponse;
//
//import java.util.List;
//import java.util.Map;
//
//@Slf4j
//@RestController
//@Configuration
//public class KpiThresholdController {
//
//    @Autowired
//    private GetServiceStaticThresholdBL getServiceStaticThresholdBL;
//
//    @Autowired
//    private PostServiceStaticThresholdBL postServiceStaticThresholdBL;
//
//    @Autowired
//    JsonFileParser headersParser;
//
//    @Operation(
//            summary = "Retrieve KPI Thresholds",
//            responses = {
//                    @ApiResponse(
//                            responseCode = "200", description = "KPI Thresholds fetching successfully",
//                            content = @Content(
//                                    schema = @Schema(
//                                            implementation = GetApplications.class,
//                                            type = "array"
//                                    )
//                            )
//                    ),
//                    @ApiResponse(
//                            responseCode = "400", description = "Error in fetching KPI Thresholds",
//                            content = @Content(schema = @Schema(implementation = ErrorResponsePojo.class))
//                    ),
//                    @ApiResponse(
//                            responseCode = "500", description = "Internal Server Exception encountered while fetching KPI Thresholds",
//                            content = @Content(schema = @Schema(implementation = ErrorResponsePojo.class))
//                    )
//            }
//    )
//    @GetMapping(value = "/accounts/{accountIdentifier}/services/{serviceIdentifier}/threshold-types/{thresholdType}/thresholds")
//    public ResponseEntity<ResponsePojo<List<StaticThresholdRules>>> getKpiThresholdForServiceLevel(@RequestHeader(value = "Authorization") String authorization, @PathVariable(value = "accountIdentifier") String accountIdentifier,
//                                               @PathVariable(value = "serviceIdentifier") String serviceIdentifier,
//                                               @PathVariable(value = "thresholdType") String thresholdType,
//                                               @RequestParam(value = "kpiType") String kpiType)
//            throws ClientException, DataProcessingException, ServerException {
//
//        UtilityBean<Object> utilityBean = getServiceStaticThresholdBL.clientValidation(null, authorization, accountIdentifier, serviceIdentifier, kpiType, thresholdType);
//        UtilityBean<Map<String, Object>> userAccessInfo = getServiceStaticThresholdBL.serverValidation(utilityBean);
//        List<StaticThresholdRules> thresholdRules = getServiceStaticThresholdBL.process(userAccessInfo);
//
//        ResponsePojo<List<StaticThresholdRules>> responsePojo = new ResponsePojo<>("KPI Thresholds fetched successfully",
//                thresholdRules, HttpStatus.OK);
//
//        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
//    }
//
//    @Operation(
//            summary = "Update or Create KPI Thresholds",
//            responses = {
//                    @ApiResponse(
//                            responseCode = "200", description = "KPI Thresholds updated successfully",
//                            content = @Content(
//                                    schema = @Schema(
//                                            implementation = GetApplications.class,
//                                            type = "array"
//                                    )
//                            )
//                    ),
//                    @ApiResponse(
//                            responseCode = "400", description = "Error in updating KPI Thresholds",
//                            content = @Content(schema = @Schema(implementation = ErrorResponsePojo.class))
//                    ),
//                    @ApiResponse(
//                            responseCode = "500", description = "Internal Server Exception encountered while updating KPI Thresholds",
//                            content = @Content(schema = @Schema(implementation = ErrorResponsePojo.class))
//                    )
//            }
//    )
//    @PostMapping(value = "/accounts/{accountIdentifier}/services/{serviceIdentifier}/threshold-types/{thresholdType}/thresholds")
//    public ResponseEntity<ResponsePojo<String>> updateKpiThresholdForServiceLeve(@RequestHeader(value = "Authorization") String authorization, @PathVariable(value = "accountIdentifier") String accountIdentifier,
//                                                                                             @PathVariable(value = "serviceIdentifier") String serviceIdentifier,
//                                                                                             @PathVariable(value = "thresholdType") String thresholdType,
//                                                                                             @RequestParam(value = "kpiType") String kpiType,
//                                                                                             @RequestBody List<StaticThresholdRules> staticThresholdRules)
//            throws ClientException, DataProcessingException, ServerException {
//
//        UtilityBean<List<StaticThresholdRules>> utilityBean = postServiceStaticThresholdBL.clientValidation(staticThresholdRules, authorization, accountIdentifier, serviceIdentifier, kpiType, thresholdType);
//        UtilityBean<List<StaticThresholdRules>> validUtilityBean = postServiceStaticThresholdBL.serverValidation(utilityBean);
//        List<StaticThresholdRules> thresholdRules = postServiceStaticThresholdBL.process(validUtilityBean);
//        String message = "KPI Thresholds Updated Successfully.";
//        if(utilityBean.getPojoObject().size() != thresholdRules.size()) {
//            message = "KPI Thresholds Partially Updated.";
//        }
//        ResponsePojo<String> responseBean = new ResponsePojo<>();
//        responseBean.setData(null);
//        responseBean.setMessage(message);
//        responseBean.setResponseStatus(HttpStatus.OK);
//        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responseBean);
//    }
//}
