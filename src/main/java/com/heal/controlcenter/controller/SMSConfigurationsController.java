package com.heal.controlcenter.controller;

import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.GetSMSConfigurationsBL;
import com.heal.controlcenter.businesslogic.PostSMSConfigurationsBL;
import com.heal.controlcenter.businesslogic.PutSMSConfigurationsBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.ResponsePojo;
import com.heal.controlcenter.pojo.SMSDetailsPojo;
import com.heal.controlcenter.swagger.CommonApiResponses;
import com.heal.controlcenter.util.JsonFileParser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Slf4j
@Configuration
@RestController
public class SMSConfigurationsController {

    @Autowired
    GetSMSConfigurationsBL getSMSConfigurationsBL;
    @Autowired
    PutSMSConfigurationsBL putSMSConfigurationsBL;
    @Autowired
    PostSMSConfigurationsBL postSMSConfigurationsBL;
    @Autowired
    JsonFileParser headersParser;

    @Operation(
            summary = "Retrieves SMS configurations for the specified account.",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "SMS configurations fetched successfully.",
                            content = @Content(schema = @Schema(implementation = SMSDetailsPojo.class))
                    )
            }
    )
    @CommonApiResponses
    @RequestMapping(value = "/accounts/{identifier}/sms-configurations", method = RequestMethod.GET)
    public ResponseEntity<ResponsePojo<SMSDetailsPojo>> getSmsConfigurations(
            @Parameter(
                    name = "Authorization",
                    description = "Bearer token for authentication",
                    required = true,
                    example = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            )
            @RequestHeader("Authorization") String authorization,
            @Parameter(
                    name = "identifier",
                    description = "Unique identifier of the account",
                    required = true,
                    example = "account-001"
            )
            @PathVariable("identifier") String accountIdentifier)
            throws ClientException, ServerException, DataProcessingException {
        log.trace("Method Invoked : getSmsConfigurations");

        UtilityBean<Object> smsDetailsUtilityBean = getSMSConfigurationsBL.clientValidation(null, authorization, accountIdentifier);
        Integer accountId = getSMSConfigurationsBL.serverValidation(smsDetailsUtilityBean);
        SMSDetailsPojo data = getSMSConfigurationsBL.process(accountId);

        ResponsePojo<SMSDetailsPojo> responsePojo = new ResponsePojo<>("SMS configurations fetched successfully.", data, HttpStatus.OK);

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
    }

    @Operation(
            summary = "Updates SMS configurations for the specified account.",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "SMS configurations updated successfully."
                    )
            }
    )
    @CommonApiResponses
    @RequestMapping(value = "/accounts/{identifier}/sms-configurations", method = RequestMethod.PUT)
    public ResponseEntity<ResponsePojo<Object>> putSmsConfigurations(
            @Parameter(
                    name = "Authorization",
                    description = "Bearer token for authentication",
                    required = true,
                    example = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            )
            @RequestHeader("Authorization") String authorization,
            @Parameter(
                    name = "identifier",
                    description = "Unique identifier of the account",
                    required = true,
                    example = "account-001"
            )
            @PathVariable("identifier") String accountIdentifier,
            @Parameter(
                    description = "SMS configuration details to update",
                    required = true
            )
            @Validated @RequestBody SMSDetailsPojo body)
            throws ClientException, ServerException, DataProcessingException {
        log.trace("Method Invoked : putSmsConfigurations");

        UtilityBean<SMSDetailsPojo> smsDetailsUtilityBean = putSMSConfigurationsBL.clientValidation(body, authorization, accountIdentifier);
        putSMSConfigurationsBL.serverValidation(smsDetailsUtilityBean);
        putSMSConfigurationsBL.process(smsDetailsUtilityBean);

        ResponsePojo<Object> responsePojo = new ResponsePojo<>("SMS configurations updated successfully.", null, HttpStatus.OK);

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
    }

    @Operation(
            summary = "Adds SMS configurations for the specified account.",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "SMS configurations added successfully."
                    )
            }
    )
    @CommonApiResponses
    @RequestMapping(value = "/accounts/{identifier}/sms-configurations", method = RequestMethod.POST)
    public ResponseEntity<ResponsePojo<Object>> addSMSConfiguration(
            @Parameter(
                    name = "Authorization",
                    description = "Bearer token for authentication",
                    required = true,
                    example = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            )
            @RequestHeader("Authorization") String authorization,
            @Parameter(
                    name = "identifier",
                    description = "Unique identifier of the account",
                    required = true,
                    example = "account-001"
            )
            @PathVariable("identifier") String accountIdentifier,
            @Parameter(
                    description = "SMS configuration details to add",
                    required = true
            )
            @Validated @RequestBody SMSDetailsPojo body)
            throws ClientException, ServerException, DataProcessingException {
        log.trace("Method Invoked : addSMSConfiguration");

        UtilityBean<SMSDetailsPojo> smsDetailsUtilityBean = postSMSConfigurationsBL.clientValidation(body, authorization, accountIdentifier);
        postSMSConfigurationsBL.serverValidation(smsDetailsUtilityBean);
        postSMSConfigurationsBL.process(smsDetailsUtilityBean);

        ResponsePojo<Object> responsePojo = new ResponsePojo<>("SMS configurations added successfully.", null, HttpStatus.OK);

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
    }
}
