package com.heal.controlcenter.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 */
@Configuration
public class HealWebMvcConfiguration implements WebMvcConfigurer {
    @Value("${header.access.control.allow.origins:*}")
    String frontendUrl;
    @Value("${header.access.control.allow.headers:Content-Type,Authorization,X-Requested-With,Content-Length,Accept,Origin}")
    String headers;
    @Value("${header.access.control.allow.methods:GET,POST,PUT,DELETE,OPTIONS,HEAD,PATCH}")
    String methods;
    @Value("${header.access.control.allow.credentials:false}")
    boolean allowCredentials;

    @Override
    public void addCorsMappings(CorsRegistry corsRegistry) {
        System.out.println("🔧 CORS Configuration Loading...");
        System.out.println("   Origins: " + frontendUrl);
        System.out.println("   Methods: " + methods);
        System.out.println("   Headers: " + headers);
        System.out.println("   Credentials: " + allowCredentials);

        String[] origins = frontendUrl.split(",");
        String[] headerArray = headers.split(",");
        String[] methodArray = methods.split(",");

        corsRegistry.addMapping("/**")
                .allowedOriginPatterns(origins) // Use patterns to support wildcards
                .allowedHeaders(headerArray)
                .allowedMethods(methodArray)
                .allowCredentials(allowCredentials)
                .maxAge(3600); // Cache preflight for 1 hour

        System.out.println("✅ CORS Configuration Applied to /** mapping");
    }
}