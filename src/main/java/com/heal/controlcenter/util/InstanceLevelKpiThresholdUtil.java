package com.heal.controlcenter.util;

import com.heal.configuration.pojos.CompInstKpiEntity;
import com.heal.configuration.pojos.KpiViolationConfig;
import com.heal.configuration.pojos.ViewTypes;
import com.heal.controlcenter.dao.mysql.CompInstanceDao;
import com.heal.controlcenter.dao.mysql.KPIDao;
import com.heal.controlcenter.dao.mysql.ThresholdDao;
import com.heal.controlcenter.dao.mysql.entity.InstKpiAttrPersistenceSuppressionBean;
import com.heal.controlcenter.dao.mysql.entity.InstanceKpiAttributeThresholdBean;
import com.heal.controlcenter.dao.mysql.entity.KpiBean;
import com.heal.controlcenter.dao.redis.InstanceRepo;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.pojo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class InstanceLevelKpiThresholdUtil {

    @Autowired
    KPIDao kpiDao;
    @Autowired
    CompInstanceDao compInstanceDao;
    @Autowired
    CacheWrapper cacheWrapper;
    @Autowired
    DateTimeUtil dateTimeUtil;
    @Autowired
    InstanceRepo instanceRepo;
    @Autowired
    ThresholdDao thresholdDao;
    @Autowired
    CommonUtils commonUtils;

    public void validateInstanceKpiThresholdDetails(InstanceKpiThresholdDetails instanceKpiThresholdDetails, boolean addingThresholds) throws HealControlCenterException {
        log.info("Validating instance level KPI threshold details: {}", instanceKpiThresholdDetails);
        AtomicBoolean invalidInput = new AtomicBoolean(false);
        List<InstanceLevelKpiAttributeThreshold> thresholds = instanceKpiThresholdDetails.getThresholds();

        thresholds.forEach(kpiAttributeThreshold -> {
            if (!kpiAttributeThreshold.validate(addingThresholds)) {
                log.error("Validation of violation configuration failed for KPI attribute [{}]", kpiAttributeThreshold.getAttributeValue());
                invalidInput.set(true);
            }
        });

        if (invalidInput.get()) {
            log.error("Validation of violation configuration(s) failed");
            throw new HealControlCenterException("Validation of violation configuration(s) failed");
        }

        Set<KpiIdVsAttribute> attributeValues = thresholds.stream()
                .map(t -> KpiIdVsAttribute.builder().kpiId(t.getKpiId()).attributeName(t.getAttributeValue()).build())
                .collect(Collectors.toSet());

        if (attributeValues.size() != thresholds.size()) {
            log.error("Duplicate threshold details for KPI attributes(s) provided in the request");
            throw new HealControlCenterException("Duplicate threshold details for KPI attributes(s) provided in the request");
        }
    }

    public Map<Integer, String> verifyInstanceIdKpiAndGroupKpi(InstanceKpiThresholdDetails details, int accountId) throws HealControlCenterException {
        log.info("Verifying instance ID, KPI and group KPI for account ID: {} & details {}", accountId, details);

        Map<Integer, String> compInstIdToIdentifierMap = new HashMap<>();
        List<Integer> instanceIds = details.getInstances();
        List<InstanceLevelKpiAttributeThreshold> inputThresholds = details.getThresholds();

        int kpiId = inputThresholds.get(0).getKpiId();
        int groupKpiId = inputThresholds.get(0).getGroupKpiId();

        KpiBean kpiBean = kpiDao.fetchKpiUsingKpiId(kpiId, accountId);
        if (kpiBean == null) {
            String errMsg = String.format(UIMessages.INVALID_KPI_ID, kpiId);
            log.error(errMsg);
            throw new HealControlCenterException(errMsg);
        }

        if (groupKpiId > 0 && kpiBean.getGroupKpiId() != groupKpiId) {
            log.error("Group Kpi with ID: {} is not mapped to Kpi with ID: {}", groupKpiId, kpiId);
            throw new HealControlCenterException(String.format("Group Kpi with ID %d is not mapped to Kpi with ID %d", groupKpiId, kpiId));
        }

        Map<Integer, String> compIdVsIdentifierMap = compInstanceDao.getCompInstanceIdentifiersFromIds(instanceIds, accountId);

        for (int instanceId : instanceIds) {
            log.info("Validating instance ID: {} for account ID: {}", instanceId, accountId);
            String compInstanceIdentifier = compIdVsIdentifierMap.get(instanceId);
            if (compInstanceIdentifier == null) {
                String errMsg = String.format(UIMessages.INVALID_INSTANCE_ID, instanceId);
                log.error(errMsg);
                throw new HealControlCenterException(errMsg);
            }
            compInstIdToIdentifierMap.put(instanceId, compInstanceIdentifier);
        }

        log.info("Successfully validated instance IDs and fetched identifiers for account ID: {}", accountId);

        return compInstIdToIdentifierMap;
    }

    public List<InstanceKpiAttributeThresholdBean> kpiAndCompInstanceValidation(InstanceKpiThresholdDetails details, Map<Integer, String> compInstIdToIdentifierMap, String userDetails,
                                                                                int accountId, String accountIdentifier, boolean addingThresholds) throws HealControlCenterException {

        log.info("Validating KPI and component instance for account ID: {} & details {}", accountId, details);

        // Initialize required data
        ThresholdValidationContext context = initializeValidationContext(details, accountId, accountIdentifier, userDetails);

        List<InstanceKpiAttributeThresholdBean> attributeThresholdBeans = new ArrayList<>();

        // Process each instance
        for (int instanceId : context.getInstances()) {
            InstanceProcessingData instanceData = prepareInstanceData(instanceId, context);
            attributeThresholdBeans.addAll(processThresholdsForInstance(instanceData, context, compInstIdToIdentifierMap, addingThresholds));
            log.info("Successfully converted instance level KPI attribute thresholds for instance ID: {}", instanceId);
        }

        // Validate for duplicates
        validateForDuplicates(attributeThresholdBeans, addingThresholds);

        return attributeThresholdBeans;
    }

    /**
     * Initialize validation context with severity types and maps
     */
    private ThresholdValidationContext initializeValidationContext(InstanceKpiThresholdDetails details, int accountId, String accountIdentifier,
                                                                   String userDetails) throws HealControlCenterException {
        Map<String, List<ViewTypes>> allViewTypesMapForKPI = cacheWrapper.getAllViewTypesIdMap();

        // Get severity types
        SeverityTypes severityTypes = getSeverityTypes(allViewTypesMapForKPI);

        // Prepare data maps
        List<Integer> instances = details.getInstances();
        Map<Integer, List<InstanceKpiAttributeThresholdBean>> groupKpiMap = compInstanceDao.checkGroupKpiCompInstanceMapping(instances).stream()
                .collect(Collectors.groupingBy(InstanceKpiAttributeThresholdBean::getCompInstanceId));

        Map<Integer, List<InstanceKpiAttributeThresholdBean>> nonGroupKpiMap = compInstanceDao.checkKpiCompInstanceMapping(instances).stream()
                .collect(Collectors.groupingBy(InstanceKpiAttributeThresholdBean::getCompInstanceId));

        Map<Integer, List<InstanceKpiAttributeThresholdBean>> existingThresholdMap = thresholdDao.fetchCompInstanceKpiAttrThresholds(instances).stream()
                .collect(Collectors.groupingBy(InstanceKpiAttributeThresholdBean::getCompInstanceId));

        return ThresholdValidationContext.builder()
                .instances(instances)
                .thresholds(details.getThresholds())
                .accountId(accountId)
                .accountIdentifier(accountIdentifier)
                .userDetails(userDetails)
                .severityTypes(severityTypes)
                .allViewTypesMap(allViewTypesMapForKPI)
                .groupKpiMap(groupKpiMap)
                .nonGroupKpiMap(nonGroupKpiMap)
                .existingThresholdMap(existingThresholdMap)
                .build();
    }

    /**
     * Get all severity types with validation
     */
    private SeverityTypes getSeverityTypes(Map<String, List<ViewTypes>> allViewTypesMapForKPI) throws HealControlCenterException {
        String errorMsg = "Invalid thresholds view types. Subtype: {} unavailable";

        ViewTypes low = commonUtils.getViewTypeByNameAndSubType(allViewTypesMapForKPI,
                Constants.THRESHOLD_SEVERITY_TYPE, Constants.THRESHOLD_SEVERITY_TYPE_LOW);
        if (low == null) {
            log.error(errorMsg, Constants.THRESHOLD_SEVERITY_TYPE_LOW);
            throw new HealControlCenterException("Failure in data validation");
        }

        ViewTypes medium = commonUtils.getViewTypeByNameAndSubType(allViewTypesMapForKPI,
                Constants.THRESHOLD_SEVERITY_TYPE, Constants.THRESHOLD_SEVERITY_TYPE_MEDIUM);
        if (medium == null) {
            log.error(errorMsg, Constants.THRESHOLD_SEVERITY_TYPE_MEDIUM);
            throw new HealControlCenterException("Failure in data validation");
        }

        ViewTypes high = commonUtils.getViewTypeByNameAndSubType(allViewTypesMapForKPI,
                Constants.THRESHOLD_SEVERITY_TYPE, Constants.THRESHOLD_SEVERITY_TYPE_HIGH);
        if (high == null) {
            log.error(errorMsg, Constants.THRESHOLD_SEVERITY_TYPE_HIGH);
            throw new HealControlCenterException("Failure in data validation");
        }

        return new SeverityTypes(low, medium, high);
    }

    /**
     * Prepare instance-specific data for processing
     */
    private InstanceProcessingData prepareInstanceData(int instanceId, ThresholdValidationContext context) {
        List<InstanceKpiAttributeThresholdBean> groupAttributes = context.getGroupKpiMap().getOrDefault(instanceId, new ArrayList<>());
        List<InstanceKpiAttributeThresholdBean> nonGroupAttributes = context.getNonGroupKpiMap().getOrDefault(instanceId, new ArrayList<>());
        List<InstanceKpiAttributeThresholdBean> existingThresholds = context.getExistingThresholdMap().getOrDefault(instanceId, new ArrayList<>());

        Map<KpiIdVsAttributeSeverityId, InstanceKpiAttributeThresholdBean> existingThresholdMap = existingThresholds.stream()
                        .collect(Collectors.toMap(e -> new KpiIdVsAttributeSeverityId(e.getKpiId(), e.getAttributeValue(), e.getThresholdSeverityId()),
                        Function.identity()));

        return InstanceProcessingData.builder()
                .instanceId(instanceId)
                .groupKpis(groupAttributes)
                .nonGroupKpis(nonGroupAttributes)
                .existingThresholds(existingThresholds)
                .existingThresholdMap(existingThresholdMap)
                .build();
    }

    /**
     * Process all thresholds for a single instance
     */
    private List<InstanceKpiAttributeThresholdBean> processThresholdsForInstance(InstanceProcessingData instanceData, ThresholdValidationContext context,
                                                                                 Map<Integer, String> compInstIdToIdentifierMap, boolean addingThresholds)
            throws HealControlCenterException {

        List<InstanceKpiAttributeThresholdBean> beans = new ArrayList<>();

        for (InstanceLevelKpiAttributeThreshold threshold : context.getThresholds()) {
            if (threshold.getLowThreshold() != null) {
                beans.add(createThresholdBean(threshold, threshold.getLowThreshold(), context.getSeverityTypes().getLow(),
                        instanceData, context, compInstIdToIdentifierMap, addingThresholds));
            }

            if (threshold.getMediumThreshold() != null) {
                beans.add(createThresholdBean(threshold, threshold.getMediumThreshold(), context.getSeverityTypes().getMedium(),
                        instanceData, context, compInstIdToIdentifierMap, addingThresholds));
            }

            if (threshold.getHighThreshold() != null) {
                beans.add(createThresholdBean(threshold, threshold.getHighThreshold(), context.getSeverityTypes().getHigh(),
                        instanceData, context, compInstIdToIdentifierMap, addingThresholds));
            }
        }

        return beans;
    }

    /**
     * Create a threshold bean for a specific severity level
     */
    private InstanceKpiAttributeThresholdBean createThresholdBean(InstanceLevelKpiAttributeThreshold threshold, InstanceLevelKpiAttributeThreshold.ThresholdDetails thresholdDetails,
                                                                  ViewTypes severityType, InstanceProcessingData instanceData, ThresholdValidationContext context,
                                                                  Map<Integer, String> compInstIdToIdentifierMap, boolean addingThresholds) throws HealControlCenterException {

        KpiIdVsAttributeSeverityId key = new KpiIdVsAttributeSeverityId(threshold.getKpiId(), threshold.getAttributeValue(), severityType.getSubTypeId());
        InstanceKpiAttributeThresholdBean existingBean = instanceData.getExistingThresholdMap().get(key);

        // Get or validate operation type
        ViewTypes operationTypeView = getOperationTypeView(thresholdDetails, existingBean, context.getAllViewTypesMap());

        // Calculate threshold values
        Map<String, Double> values = calculateThresholdValues(thresholdDetails, existingBean, operationTypeView.getTypeName());

        // Create the bean
        InstanceKpiAttributeThresholdBean bean = buildThresholdBean(threshold, thresholdDetails, values,
                operationTypeView, severityType, instanceData.getInstanceId(), context, compInstIdToIdentifierMap);

        // Validate mappings
        if (bean.getKpiGroupId() > 0) {
            validateGroupKpiMapping(bean, instanceData, context);
        } else {
            validateNonGroupKpiMapping(bean, instanceData, context);
        }

        // Individual validation
        validateThresholdsIndividually(addingThresholds, instanceData.getInstanceId(), context.getInstances().size(), bean, instanceData.getExistingThresholds());

        return bean;
    }

    /**
     * Get or validate operation type view
     */
    private ViewTypes getOperationTypeView(InstanceLevelKpiAttributeThreshold.ThresholdDetails thresholdDetails,
            InstanceKpiAttributeThresholdBean existingBean,
            Map<String, List<ViewTypes>> allViewTypesMap) throws HealControlCenterException {

        ViewTypes operationTypeView = commonUtils.getViewTypeByNameAndSubType(allViewTypesMap,
                Constants.OPERATIONS_TYPE, thresholdDetails.getOperationType());

        if (operationTypeView == null && existingBean != null) {
            operationTypeView = commonUtils.getViewTypeByNameAndSubType(allViewTypesMap,
                    Constants.OPERATIONS_TYPE, existingBean.getOperationName());
        }

        if (operationTypeView == null) {
            log.error("Threshold operationId is invalid");
            throw new HealControlCenterException("Threshold operationId is invalid");
        }

        return operationTypeView;
    }

    /**
     * Calculate min and max threshold values
     */
    private Map<String, Double> calculateThresholdValues(InstanceLevelKpiAttributeThreshold.ThresholdDetails thresholdDetails,
                                                         InstanceKpiAttributeThresholdBean existingBean, String operationName) throws HealControlCenterException {

        Map<String, Double> thresholdMap = new HashMap<>();

        Double minThreshold = (thresholdDetails.getMinThreshold() != null) ?
                thresholdDetails.getMinThreshold() : (existingBean != null ? existingBean.getMinThreshold() : null);
        thresholdMap.put("MIN", minThreshold);

        Double maxThreshold;

        if ("not between".equalsIgnoreCase(operationName)) {
            maxThreshold = (thresholdDetails.getMaxThreshold() != null) ?
                    thresholdDetails.getMaxThreshold() : (existingBean != null ? existingBean.getMaxThreshold() : 0.0);

            thresholdMap.put("MAX", maxThreshold);

            if (minThreshold != null && minThreshold > maxThreshold) {
                log.error("Invalid thresholds. MinThreshold [{}] is greater than maxThreshold [{}]", minThreshold, maxThreshold);
                throw new HealControlCenterException(String.format("Invalid thresholds. MinThreshold [%f] is greater than maxThreshold [%f]", minThreshold, maxThreshold));
            }
        }

        return thresholdMap;
    }

    /**
     * Build the threshold bean with all required fields
     */
    private InstanceKpiAttributeThresholdBean buildThresholdBean(InstanceLevelKpiAttributeThreshold threshold, InstanceLevelKpiAttributeThreshold.ThresholdDetails thresholdDetails,
                                                                 Map<String, Double> values, ViewTypes operationTypeView, ViewTypes severityType, int instanceId,
                                                                 ThresholdValidationContext context, Map<Integer, String> compInstIdToIdentifierMap) {

        return InstanceKpiAttributeThresholdBean.builder()
                .accountId(context.getAccountId())
                .compInstanceId(instanceId)
                .kpiId(threshold.getKpiId())
                .kpiGroupId(threshold.getGroupKpiId())
                .attributeValue(threshold.getAttributeValue())
                .actionForUpdate(thresholdDetails.getAction())
                .status(thresholdDetails.getStatus())
                .operationId(operationTypeView.getSubTypeId())
                .maxThreshold(values.get("MAX"))
                .minThreshold(values.get("MIN"))
                .createdTime(dateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                .updatedTime(dateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                .startTime(dateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                .compInstanceIdentifier(compInstIdToIdentifierMap.get(instanceId))
                .operationName(operationTypeView.getTypeName())
                .userDetailsId(context.getUserDetails())
                .accountIdentifier(context.getAccountIdentifier())
                .thresholdSeverityId(severityType.getSubTypeId())
                .coverageWindow(thresholdDetails.getCoverageWindow())
                .build();
    }

    /**
     * Validate group KPI mapping
     */
    private void validateGroupKpiMapping(InstanceKpiAttributeThresholdBean bean, InstanceProcessingData instanceData,
                                         ThresholdValidationContext context) throws HealControlCenterException {

        log.debug("Attribute thresholds are part of group KPI [{}]", bean.getKpiGroupId());

        boolean kpiMapped = instanceData.getGroupKpis().stream()
                .anyMatch(c -> c.getKpiId() == bean.getKpiId()
                        && c.getKpiGroupId() == bean.getKpiGroupId()
                        && c.getCompInstanceId() == bean.getCompInstanceId());

        if (!kpiMapped) {
            String errorMsg = String.format("Group KPI: %s is not mapped to compInstanceId: %s, accountId: %s",
                    bean.getKpiGroupId(), instanceData.getInstanceId(), context.getAccountId());
            log.error(errorMsg);
            throw new HealControlCenterException(errorMsg);
        }

        if (bean.getIsDiscovery() == 0) {
            boolean attributeMapped = instanceData.getGroupKpis().stream()
                    .anyMatch(c -> c.getAttributeValue().equalsIgnoreCase(bean.getAttributeValue()));

            if (!attributeMapped) {
                String errorMsg = String.format("Group KPI: %s with attribute: %s is not mapped to compInstanceId: %s, accountId: %s",
                        bean.getKpiGroupId(), bean.getAttributeValue(), instanceData.getInstanceId(), context.getAccountId());
                log.error(errorMsg);
                throw new HealControlCenterException(errorMsg);
            }
        }
    }

    /**
     * Validate non-group KPI mapping
     */
    private void validateNonGroupKpiMapping(InstanceKpiAttributeThresholdBean bean,InstanceProcessingData instanceData,
            ThresholdValidationContext context) throws HealControlCenterException {

        boolean kpiMapped = instanceData.getNonGroupKpis().stream()
                .anyMatch(c -> c.getCompInstanceId() == bean.getCompInstanceId()
                        && c.getKpiId() == bean.getKpiId());

        if (!kpiMapped) {
            String errorMsg = String.format("Non-group KPI %s provided is not mapped to compInstanceId %s, accountId %s",
                    bean.getKpiId(), instanceData.getInstanceId(), context.getAccountId());
            log.error(errorMsg);
            throw new HealControlCenterException(errorMsg);
        }
    }

    /**
     * Validate for duplicate threshold configurations
     */
    private void validateForDuplicates(List<InstanceKpiAttributeThresholdBean> attributeThresholdBeans, boolean addingThresholds) throws HealControlCenterException {
        List<InstanceKpiAttributeThresholdBean> beansForDuplicateCheck = attributeThresholdBeans;

        if (!addingThresholds) {
            Map<Boolean, List<InstanceKpiAttributeThresholdBean>> partitionForDuplicateCheck = attributeThresholdBeans.stream()
                    .collect(Collectors.partitioningBy(c -> ActionsEnum.DELETE.equals(c.getActionForUpdate())));
            beansForDuplicateCheck = partitionForDuplicateCheck.get(false);
        }

        Set<InstanceKpiAttributeThresholdBean> tempSet = new HashSet<>(beansForDuplicateCheck);

        if (tempSet.size() != beansForDuplicateCheck.size()) {
            log.error("Duplicate threshold configuration in the input");
            throw new HealControlCenterException("Duplicate threshold configuration in the input");
        }
    }

    private void validateThresholdsIndividually(boolean addingThresholds, int instanceId, int instancesCount, InstanceKpiAttributeThresholdBean bean,
                                                List<InstanceKpiAttributeThresholdBean> existingThresholdBeans) throws HealControlCenterException {

        Map<Integer, List<InstanceKpiAttributeThresholdBean>> thresholdBeanMap = existingThresholdBeans
                .parallelStream().collect(Collectors.groupingBy(InstanceKpiAttributeThresholdBean::getKpiId));

        if (addingThresholds) {
            //Check for attribute thresholds entries are already available
            if (existingThresholdBeans.contains(bean)) {
                log.error("Thresholds for KPI: {} and attribute: {} already exist for instanceId: {}. Details: {}",
                        bean.getKpiId(), bean.getAttributeValue(), instanceId, bean);
                throw new HealControlCenterException(String.format("Thresholds for KPI %d, ThresholdSeverity %d and attribute %s already exist for instanceId %d",
                        bean.getKpiId(), bean.getThresholdSeverityId(), bean.getAttributeValue(), instanceId));
            }
        } else {
            if (instancesCount == 1 && existingThresholdBeans.isEmpty()) {
                log.error("None of the Kpi attribute(s) thresholds exist");
                throw new HealControlCenterException("None of the Kpi attribute(s) thresholds exist");
            }

            if (ActionsEnum.ADD.equals(bean.getActionForUpdate())) {
                if (existingThresholdBeans.contains(bean)) {
                    log.error("Thresholds for KPI: {}, ThresholdSeverity: {} and attribute: {} already exist for instanceId: {}. Details: {}",
                            bean.getKpiId(), bean.getThresholdSeverityId(), bean.getAttributeValue(), instanceId, bean);
                    throw new HealControlCenterException(String.format("Thresholds for KPI %d and attribute %s already exist for instanceId %d",
                            bean.getKpiId(), bean.getAttributeValue(), instanceId));
                }
            } else {
                if (instancesCount == 1 && !existingThresholdBeans.contains(bean)) {
                    log.error("Thresholds for KPI: {}, ThresholdSeverity: {} and attribute: {} unavailable for instanceId: {}. Details: {}",
                            bean.getKpiId(), bean.getThresholdSeverityId(), bean.getAttributeValue(), instanceId, bean);
                    throw new HealControlCenterException(String.format("Thresholds for KPI %d and attribute %s unavailable for instanceId %d",
                            bean.getKpiId(), bean.getAttributeValue(), instanceId));
                }

                if (instancesCount > 1 && ActionsEnum.MODIFY.equals(bean.getActionForUpdate()) && !existingThresholdBeans.contains(bean)) {
                    bean.setActionForUpdate(ActionsEnum.ADD);
                }

                if (ActionsEnum.MODIFY.equals(bean.getActionForUpdate())) {
                    //In case of PUT API, check for duplicates to avoid invalid cassandra time series updates.
                    List<InstanceKpiAttributeThresholdBean> beans = thresholdBeanMap.getOrDefault(bean.getKpiId(), new ArrayList<>());

                    boolean invalidConfiguration = beans.stream()
                            .anyMatch(t -> bean.equals(t)
                                    && t.getOperationId() == bean.getOperationId()
                                    && (bean.getMaxThreshold() != null && t.getMaxThreshold().equals(bean.getMaxThreshold()))
                                    && (bean.getMinThreshold() != null && t.getMinThreshold().equals(bean.getMinThreshold()))
                                    && bean.getStatus() == t.getStatus()
                                    && bean.getThresholdSeverityId() == t.getThresholdSeverityId());

                    if (invalidConfiguration) {
                        log.error("No modification in the input threshold configuration for KPI with ID: {} and attribute: {} compared " +
                                "to existing thresholds", bean.getKpiId(), bean.getAttributeValue());
                        throw new HealControlCenterException(String.format("No modification in the input threshold configuration for KPI with ID %d and attribute " +
                                "%s compared to existing thresholds", bean.getKpiId(), bean.getAttributeValue()));
                    }
                }
            }
        }
    }

    public void addInstanceKpiAttributeLevelThresholdsInRedis(List<InstanceKpiAttributeThresholdBean> thresholdBeans) {
        if (thresholdBeans == null || thresholdBeans.isEmpty()) {
            log.warn("No instance level KPI attribute thresholds to add in redis");
            return;
        }

        log.info("Adding instance level KPI attribute thresholds in redis : {}", thresholdBeans);

        int accountId = thresholdBeans.get(0).getAccountId();

        List<Integer> kpiIds = thresholdBeans.stream().map(InstanceKpiAttributeThresholdBean::getKpiId).distinct().collect(Collectors.toList());
        Map<CompInstanceVsKpi, List<InstanceKpiAttributeThresholdBean>> compInstanceVsKpiMap = thresholdBeans.stream()
                .collect(Collectors.groupingBy(t -> CompInstanceVsKpi.builder().compInstanceId(t.getCompInstanceId()).kpiId(t.getKpiId()).build()));

        List<InstKpiAttrPersistenceSuppressionBean> allExistingConfigBeans = thresholdDao.fetchCompInstanceKpiPerSupValuesByAccountIdAndKpiIds(accountId, kpiIds);
        Map<Pair<Integer, Integer>, List<InstKpiAttrPersistenceSuppressionBean>> existingConfigBeansMap = allExistingConfigBeans.stream()
                .collect(Collectors.groupingBy(threshold -> Pair.of(threshold.getCompInstanceId(), threshold.getKpiId())));

        compInstanceVsKpiMap.forEach((compInstanceVsKpi, groupedThresholdBeans) -> {
            InstanceKpiAttributeThresholdBean thresholdBean = groupedThresholdBeans.get(0);
            List<CompInstKpiEntity> instanceWiseKpis = instanceRepo.getInstanceWiseKpis(thresholdBean.getAccountIdentifier(), thresholdBean.getCompInstanceIdentifier());

            CompInstKpiEntity kpiEntity = instanceWiseKpis.stream()
                    .filter(entity -> entity.getId() == thresholdBean.getKpiId() && thresholdBean.getKpiGroupId() == entity.getGroupId())
                    .findAny().orElse(null);
            if (kpiEntity == null) {
                log.error("The kpi details not found for the kpi: {} and instance: {}", thresholdBean.getKpiId(), thresholdBean.getCompInstanceId());
                return;
            }

            Map<String, List<KpiViolationConfig>> violationConfigMap = new HashMap<>();

            groupedThresholdBeans.forEach(bean -> {
                KpiViolationConfig violationConfig = buildKpiViolationConfig(bean, existingConfigBeansMap);
                violationConfigMap.getOrDefault(bean.getAttributeValue(), new ArrayList<>()).add(violationConfig);
            });

            if (kpiEntity.getKpiViolationConfig().isEmpty()) {
                kpiEntity.setKpiViolationConfig(violationConfigMap);
            } else {
                Map<String, List<KpiViolationConfig>> existingKpiViolationConfig = new HashMap<>(kpiEntity.getKpiViolationConfig());
                existingKpiViolationConfig.putAll(violationConfigMap);
                kpiEntity.setKpiViolationConfig(existingKpiViolationConfig);
            }

            instanceRepo.updateKpiDetailsForKpiId(thresholdBean.getAccountIdentifier(), thresholdBean.getCompInstanceIdentifier(), kpiEntity);
            instanceRepo.updateKpiDetailsForKpiIdentifier(thresholdBean.getAccountIdentifier(), thresholdBean.getCompInstanceIdentifier(), kpiEntity);
            instanceRepo.updateKpiDetails(thresholdBean.getAccountIdentifier(), thresholdBean.getCompInstanceIdentifier(), instanceWiseKpis);
        });
    }

    public KpiViolationConfig buildKpiViolationConfig(InstanceKpiAttributeThresholdBean thresholdBean, Map<Pair<Integer, Integer>, List<InstKpiAttrPersistenceSuppressionBean>> configurationMap) {
        List<InstKpiAttrPersistenceSuppressionBean> fetchCompInstanceKpiConfiguration = configurationMap.getOrDefault(Pair.of(thresholdBean.getCompInstanceId(), thresholdBean.getKpiId()), new ArrayList<>());
        fetchCompInstanceKpiConfiguration = fetchCompInstanceKpiConfiguration.stream()
                .filter(c -> c.getThresholdSeverityId() == thresholdBean.getThresholdSeverityId())
                .toList();

        int Persistence = 0;
        int suppression = 0;
        int excludeMaintenance = 0;
        if (!fetchCompInstanceKpiConfiguration.isEmpty()) {
            Persistence = Optional.ofNullable(fetchCompInstanceKpiConfiguration.get(0).getPersistence()).orElse(0);
            suppression = Optional.ofNullable(fetchCompInstanceKpiConfiguration.get(0).getSuppression()).orElse(0);
            excludeMaintenance = Optional.of(fetchCompInstanceKpiConfiguration.get(0).getIsMaintenanceExcluded()).orElse(0);
        }

        return KpiViolationConfig.builder()
                .attributeValue(thresholdBean.getAttributeValue())
                .operation(thresholdBean.getOperationName())
                .minThreshold(thresholdBean.getMinThreshold())
                .maxThreshold(thresholdBean.getMaxThreshold())
                .startTime(thresholdBean.getStartTime())
                .endTime(thresholdBean.getEndTime())
                .status(thresholdBean.getStatus())
                .generateAnomaly(thresholdBean.getStatus())
                .severity(thresholdBean.getSeverity())
                .persistence(Persistence)
                .suppression(suppression)
                .excludeMaintenance(excludeMaintenance)
                .compInstanceId(thresholdBean.getCompInstanceId())
                .kpiId(thresholdBean.getKpiId())
                .definedBy("USER")
                .thresholdSeverityId(thresholdBean.getThresholdSeverityId())
                .build();
    }

    public void deleteThresholdsForInstanceKpiInRedis(List<InstanceKpiAttributeThresholdBean> inputThresholdBeans) {
        if (inputThresholdBeans == null || inputThresholdBeans.isEmpty()) {
            log.warn("No instance level KPI attribute thresholds to delete in redis");
            return;
        }

        log.info("Deleting thresholds for instance kpi in redis: {}", inputThresholdBeans.stream()
                .map(InstanceKpiAttributeThresholdBean::getAttributeValue)
                .collect(Collectors.joining(", ")));

        inputThresholdBeans.forEach(thresholdBean -> {
            List<CompInstKpiEntity> instanceWiseKpis = instanceRepo.getInstanceWiseKpis(thresholdBean.getAccountIdentifier(), thresholdBean.getCompInstanceIdentifier());

            CompInstKpiEntity kpiEntity = instanceWiseKpis.stream()
                    .filter(entity -> entity.getId() == thresholdBean.getKpiId() && thresholdBean.getKpiGroupId() == entity.getGroupId())
                    .findAny().orElse(null);

            if (kpiEntity == null) {
                log.error("The kpi details unavailable for the kpi: {} and instance: {}", thresholdBean.getKpiId(), thresholdBean.getCompInstanceId());
                return;
            }

            kpiEntity.getKpiViolationConfig().remove(thresholdBean.getAttributeValue());

            instanceRepo.updateKpiDetailsForKpiId(thresholdBean.getAccountIdentifier(), thresholdBean.getCompInstanceIdentifier(), kpiEntity);
            instanceRepo.updateKpiDetailsForKpiIdentifier(thresholdBean.getAccountIdentifier(), thresholdBean.getCompInstanceIdentifier(), kpiEntity);
            instanceRepo.updateKpiDetails(thresholdBean.getAccountIdentifier(), thresholdBean.getCompInstanceIdentifier(), instanceWiseKpis);
        });
    }
}
