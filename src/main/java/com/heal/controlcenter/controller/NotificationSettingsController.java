package com.heal.controlcenter.controller;

import com.heal.controlcenter.beans.NotificationSettingsBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.GetNotificationSettingsBL;
import com.heal.controlcenter.businesslogic.PutNotificationSettingsBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.*;
import com.heal.controlcenter.swagger.CommonApiResponses;
import com.heal.controlcenter.util.JsonFileParser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@Configuration
@RestController
public class NotificationSettingsController {

    @Autowired
    GetNotificationSettingsBL getNotificationSettingsBL;
    @Autowired
    PutNotificationSettingsBL putNotificationSettingsBL;
    @Autowired
    JsonFileParser headersParser;

    @Operation(
            summary = "Retrieves notification settings for the specified account.",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Notification settings fetched successfully.",
                            content = @Content(schema = @Schema(implementation = NotificationSettings.class))
                    )
            }
    )
    @CommonApiResponses
    @RequestMapping(value = "/accounts/{identifier}/notification-settings", method = RequestMethod.GET)
    public ResponseEntity<ResponsePojo<List<NotificationSettings>>> getNotificationSettings(
            @Parameter(
                    name = "Authorization",
                    description = "Bearer token for authentication",
                    required = true,
                    example = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            )
            @RequestHeader("Authorization") String authorization,
            @Parameter(
                    name = "identifier",
                    description = "Unique identifier of the account",
                    required = true,
                    example = "account-001"
            )
            @PathVariable("identifier") String accountIdentifier)
            throws ClientException, ServerException, DataProcessingException {
        log.trace("Method Invoked : getNotificationSettings");

        UtilityBean<Object> utilityBean = getNotificationSettingsBL.clientValidation(null, authorization, accountIdentifier);
        UserAccountPojo user = getNotificationSettingsBL.serverValidation(utilityBean);
        List<NotificationSettings> data = getNotificationSettingsBL.process(user);

        ResponsePojo<List<NotificationSettings>> responsePojo = new ResponsePojo<>("Notification settings fetched successfully.", data, HttpStatus.OK);

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
    }

    @Operation(
            summary = "Updates notification settings for the specified account.",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Notification settings updated successfully."
                    )
            }
    )
    @CommonApiResponses
    @RequestMapping(value = "/accounts/{identifier}/notification-settings", method = RequestMethod.PUT)
    public ResponseEntity<ResponsePojo<Object>> putNotificationSettings(
            @Parameter(
                    name = "Authorization",
                    description = "Bearer token for authentication",
                    required = true,
                    example = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            )
            @RequestHeader("Authorization") String authorization,
            @Parameter(
                    name = "identifier",
                    description = "Unique identifier of the account",
                    required = true,
                    example = "account-001"
            )
            @PathVariable("identifier") String accountIdentifier,
            @Parameter(
                    description = "List of notification settings to update",
                    required = true
            )
            @Validated @RequestBody List<NotificationSettingsPojo> body)
            throws ClientException, ServerException, DataProcessingException {
        log.trace("Method Invoked : putNotificationSettings");

        UtilityBean<List<NotificationSettingsPojo>> utilityBean = putNotificationSettingsBL.clientValidation(body, authorization, accountIdentifier);
        putNotificationSettingsBL.serverValidation(utilityBean);
        putNotificationSettingsBL.process(utilityBean);

        ResponsePojo<Object> responsePojo = new ResponsePojo<>("Notification settings updated successfully.", null, HttpStatus.OK);

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
    }
}
