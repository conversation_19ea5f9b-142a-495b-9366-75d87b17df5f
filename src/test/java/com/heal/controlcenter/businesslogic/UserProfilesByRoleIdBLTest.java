package com.heal.controlcenter.businesslogic;

import com.heal.controlcenter.beans.IdBean;
import com.heal.controlcenter.beans.UserProfileBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.UserDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.util.CommonUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR> Suman - 16-11-2021
 */
@ExtendWith(MockitoExtension.class)
@PrepareForTest({UserProfilesByRoleIdBL.class})
class UserProfilesByRoleIdBLTest {

    @Mock
    CommonUtils commonUtils;
    @Autowired
    @InjectMocks
    UserProfilesByRoleIdBL userProfilesByRoleIdBL;
    @Mock
    UserDao rolesAndProfilesDao;
    List<IdBean> mockidBeanList;
    IdBean idBean;
    List<UserProfileBean> mocklistOfUserPofiles;
    UserProfileBean userProfileBean;

    @BeforeEach
    void setUp() {
        mockidBeanList = new ArrayList<>();
        mocklistOfUserPofiles = new ArrayList<>();
        userProfileBean = new UserProfileBean();
        idBean = new IdBean();
        idBean.setIdentifier("identifier1");
        idBean.setName("name1");
        idBean.setId(1);
        userProfileBean.setUserProfileName("userprofilename1");
        userProfileBean.setRole("name1");
        userProfileBean.setUserProfileId(2);
        userProfileBean.setAccessibleFeatures(new HashSet<String>(){{
            add("accessible1");
        }});
        mockidBeanList.add(idBean);
        mocklistOfUserPofiles.add(userProfileBean);
    }

    @AfterEach
    void tearDown() {
        mockidBeanList = null;
        idBean = null;
    }

    @Test
    void clientValidation_InvalidToken() throws ControlCenterException {
        String expectedMessage = "ClientException : Invalid Authorization token";
        when(commonUtils.getUserId("tyr8-uijjf8sooijfjfkjkjskjjkje")).thenReturn(any());
        ClientException exception = assertThrows(ClientException.class, () ->
                userProfilesByRoleIdBL.clientValidation(null, "tyr8-uijjf8sooijfjfkjkjskjjkje" ));
        assertEquals(expectedMessage, exception.getMessage());
    }

    @Test
    void clientValidation() throws ControlCenterException, ClientException {
        String userId = "7640123a-fbde-4fe5-9812-581cd1e3a9c1";
        when(commonUtils.getUserId("tyr8-uijjf8sooijfjfkjkjskjjkje")).thenReturn(userId);
        UtilityBean<String> auth = userProfilesByRoleIdBL.clientValidation(null, "tyr8-uijjf8sooijfjfkjkjskjjkje" );
        assertEquals(auth.getPojoObject(), userId);
    }

    @Test
    void clientValidation_WhenRequestIsNull() {
        String expectedMessage = "ClientException : Invalid Authorization token";
        ClientException requestException = assertThrows(ClientException.class, () ->
                userProfilesByRoleIdBL.clientValidation(null,"tyr8-uijjf8sooijfjfkjkjskjjkje"));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidation_WhenUserIdFailed() throws ControlCenterException {
        String message = "Error while fetching userId from the Authorization token";
        given(commonUtils.getUserId(anyString())).willAnswer(exc -> {
            throw new ControlCenterException(message);
        });
        Throwable processing = assertThrows(ClientException.class, () -> userProfilesByRoleIdBL.clientValidation(null,"tyr8-uijjf8sooijfjfkjkjskjjkje"));
        assertTrue(processing.getMessage().contains(message));
    }

    @Test
    void process() throws DataProcessingException, ControlCenterException {
        when(rolesAndProfilesDao.getRolesById(anyLong())).thenReturn(mockidBeanList);
        when(rolesAndProfilesDao.getUserProfiles()).thenReturn(mocklistOfUserPofiles);
        List<IdBean> actualResult = userProfilesByRoleIdBL.process(2L);
        assertEquals(1, actualResult.size());
        assertEquals(2, actualResult.get(0).getId());
        assertEquals("name1", actualResult.get(0).getName());
        verify(rolesAndProfilesDao, times(1)).getUserProfiles();
        verify(rolesAndProfilesDao, times(1)).getRolesById(anyLong());
    }

    @Test
    void process_when_role_empty_byid() throws ControlCenterException {
        when(rolesAndProfilesDao.getRolesById(anyLong())).thenReturn(new ArrayList<>());
        Throwable processing = assertThrows(DataProcessingException.class, () -> userProfilesByRoleIdBL.process(2L));
        assertTrue(processing.getMessage().contains("Role Id provided is unavailable"));
    }

    @Test
    void process_when_user_profile_empty() throws DataProcessingException, ControlCenterException {
        when(rolesAndProfilesDao.getRolesById(anyLong())).thenReturn(mockidBeanList);
        when(rolesAndProfilesDao.getUserProfiles()).thenReturn(new ArrayList<>());
        List<IdBean> actualResult = userProfilesByRoleIdBL.process(2L);
        assertEquals(0, actualResult.size());
    }
}