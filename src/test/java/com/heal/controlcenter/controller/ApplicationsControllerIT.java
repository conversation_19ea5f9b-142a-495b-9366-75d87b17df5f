package com.heal.controlcenter.controller;

import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.util.JsonFileParser;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.web.servlet.MockMvc;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;


@WebMvcTest(ApplicationsController.class)
@RunWith(MockitoJUnitRunner.class)
class ApplicationsControllerIT {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    JsonFileParser headersParser;

    UtilityBean<String> utilityBean = null;
    String[] params = new String[2];
    @BeforeEach
    void setUp() {
        params[0] = "check2";
        params[1] = "d681ef13-d-4917-jkhg-6c79b-1";
    }

    @AfterEach
    void tearDown() {
    }

    @Test
    void getServerValidations_InvalidIdentifier() throws Exception {
        MockHttpServletResponse mockHttpServletResponse = mockMvc.perform(get("/accounts/d681ef13-d-4917-jkhg-6c79b-1/applications")
                .header("authorization", "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.jURqssSqfLX48GplwhWdLGAtXbw6t3f37SNlByUUONem_KNbquy5hTydJY-KfX5wpfcKPJNPuLMtWn6tHS2LhyTV9PMpWCGmg99zWTEard0rWLtBoy5jphGvo15w_0XoOXSW6EaSN1m63IXH-4dogi3PNy3DdUKUFNGyw-YrQ5i3G9SQcdgvY_SwjJGM6_MDA6lFtsPijDYcyXJgxSdCYgbc79nJf6eOo1jIzbtXxyLEM06gW-SS31uXuBv3hIkn9Pu-udhjEyE2NnciSG9P-Au6Jpsy0m11BIvtwlJ5cE55ivb4jJh_WPCPTK_lqzt8bljL33Px6SQUKPzf9VZUyA"))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.responseStatus", Matchers.equalTo("BAD_REQUEST")))
                .andExpect(jsonPath("$.data", Matchers.hasKey("status")))
                .andExpect(jsonPath("$.data", Matchers.hasKey("type")))
                .andExpect(jsonPath("$.data", Matchers.hasKey("path")))
                .andExpect(jsonPath("$.data", Matchers.hasKey("error")))
                .andExpect(jsonPath("$.message", Matchers.equalTo("ServerException : Invalid Account Identifier")))
                .andReturn()
                .getResponse();
        assertEquals(mockHttpServletResponse.getStatus(), HttpStatus.BAD_REQUEST.value());
    }

    @Test
    void addServerValidations_InvalidIdentifier() throws Exception {
        String body = "{\"name\": \"it_test_app_iidentifier\"," +
                "\"services\" : [\"guihklj.o \",\"yhiohoio\"]," +
                "\"timezone\" : \"india\"}";

        MockHttpServletResponse mockHttpServletResponse = mockMvc.perform(post("/accounts/d681ef13-d-4917-jkhg-6c79b-1/applications")
                .content(body)
                .contentType(MediaType.APPLICATION_JSON)
                .header("authorization", "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bw251XjUAYSLOsADwqkhBcAmOkyy9OTHTHk-GQY-sv0sls6BS0we5bhpc9MBtLfnK03Syc8rB4IRU4T6vrN4z4zib9HIv7eIQfpyZlcz3I6zwwLgeZWLqXnCFAC71aCTeGdkAxDcylphuCGpLPcq3O3kWp_1-y_2BD3hAGo4JEWKlc5CcNilH_1fUQNtA8DEh0ZWGKRMMm1kzUX6Rp1GuqWThZbYznEW3Yyfl9Asl3BAc0sSMUcjhy0MghcAd9Qz_Q9xcwRcaC0LshDwAvRhGWN6xVWCQgMx6zSzGCGGQ4XVFxrwvz5yRI8PrlAm-dbTD9hGfGLI4rLpgSIKtzRknA"))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.responseStatus", Matchers.equalTo("BAD_REQUEST")))
                .andExpect(jsonPath("$.data", Matchers.hasKey("status")))
                .andExpect(jsonPath("$.data", Matchers.hasKey("type")))
                .andExpect(jsonPath("$.data", Matchers.hasKey("path")))
                .andExpect(jsonPath("$.data", Matchers.hasKey("error")))
                .andExpect(jsonPath("$.message", Matchers.equalTo("ServerException : Invalid Account Identifier")))
                .andReturn()
                .getResponse();
        assertEquals(mockHttpServletResponse.getStatus(), HttpStatus.BAD_REQUEST.value());
    }

    @Test
    void addServerValidations_InvalidName() throws Exception {
        String body = "{\"name\": \"NetBanking-DR\"}";

        MockHttpServletResponse mockHttpServletResponse = mockMvc.perform(post("/accounts/qa-d681ef13-d690-4917-jkhg-6c79b-1/applications")
                .content(body)
                .contentType(MediaType.APPLICATION_JSON)
                .header("authorization", "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.jVJYbITcnDNhrA0ZP8eR-rtYYc8l1g8sn1A4Xj4lVhi6ia8G27q1lCkav54pGty-myGCPyDyM1TV5mZukas5t_QXXvQ2sM2zByN0xZvo53v0W4b2_Q01NKbRNt9kxMy0TESPstXvpt8pm9K53YjlKyp4RVd4y6_tSeiDexwbRfumlKBDzgEgAHhE5z6JXOuZDotcVhYCn8mshJT1Hq-CUj6r22zPvZUgVL_i46K9RL9NWx7qEs3lP93s3zOk_xjmcf0XHTN2Vmc7ToJj1FwR65l2HY6yvh2EjUYUUzBaRZV6TaWJUYwFk0RgTgxOLezwh4thSFTI1JvI6sExLe4GUA"))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.responseStatus", Matchers.equalTo("BAD_REQUEST")))
                .andExpect(jsonPath("$.data", Matchers.hasKey("status")))
                .andExpect(jsonPath("$.data", Matchers.hasKey("type")))
                .andExpect(jsonPath("$.data", Matchers.hasKey("path")))
                .andExpect(jsonPath("$.data", Matchers.hasKey("error")))
                .andExpect(jsonPath("$.message", Matchers.anyOf(Matchers.is("ServerException : Application with name already exists"), Matchers.is("ServerException : Unable to validate application name"))))
                .andReturn()
                .getResponse();
        assertEquals(mockHttpServletResponse.getStatus(), HttpStatus.BAD_REQUEST.value());
    }

    @Test
    void addServerValidations_InvalidAppIdentifier() throws Exception {
        String body = "{\"name\": \"it_test_app_iname\"," +
                "\"identifier\" : \"netbanking_1_DR\"}";

        MockHttpServletResponse mockHttpServletResponse = mockMvc.perform(post("/accounts/qa-d681ef13-d690-4917-jkhg-6c79b-1/applications")
                .content(body)
                .contentType(MediaType.APPLICATION_JSON)
                .header("authorization", "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.jVJYbITcnDNhrA0ZP8eR-rtYYc8l1g8sn1A4Xj4lVhi6ia8G27q1lCkav54pGty-myGCPyDyM1TV5mZukas5t_QXXvQ2sM2zByN0xZvo53v0W4b2_Q01NKbRNt9kxMy0TESPstXvpt8pm9K53YjlKyp4RVd4y6_tSeiDexwbRfumlKBDzgEgAHhE5z6JXOuZDotcVhYCn8mshJT1Hq-CUj6r22zPvZUgVL_i46K9RL9NWx7qEs3lP93s3zOk_xjmcf0XHTN2Vmc7ToJj1FwR65l2HY6yvh2EjUYUUzBaRZV6TaWJUYwFk0RgTgxOLezwh4thSFTI1JvI6sExLe4GUA"))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.responseStatus", Matchers.equalTo("BAD_REQUEST")))
                .andExpect(jsonPath("$.data", Matchers.hasKey("status")))
                .andExpect(jsonPath("$.data", Matchers.hasKey("type")))
                .andExpect(jsonPath("$.data", Matchers.hasKey("path")))
                .andExpect(jsonPath("$.data", Matchers.hasKey("error")))
                .andExpect(jsonPath("$.message", Matchers.anyOf(Matchers.is("ServerException : Application with identifier already exists"), Matchers.is("ServerException : Unable to validate application identifier"))))
                .andReturn()
                .getResponse();
        assertEquals(mockHttpServletResponse.getStatus(), HttpStatus.BAD_REQUEST.value());
    }

    @Test
    void addServerValidations_InvalidTimzone() throws Exception {
        String body = "{\"name\": \"it_test_app_timezone\"," +
                "\"services\" : [\"guihklj.o \",\"yhiohoio\"]," +
                "\"timezone\" : \"india\"}";
        MockHttpServletResponse mockHttpServletResponse = mockMvc.perform(post("/accounts/qa-d681ef13-d690-4917-jkhg-6c79b-1/applications")
                .content(body)
                .contentType(MediaType.APPLICATION_JSON)
                .header("authorization", "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bw251XjUAYSLOsADwqkhBcAmOkyy9OTHTHk-GQY-sv0sls6BS0we5bhpc9MBtLfnK03Syc8rB4IRU4T6vrN4z4zib9HIv7eIQfpyZlcz3I6zwwLgeZWLqXnCFAC71aCTeGdkAxDcylphuCGpLPcq3O3kWp_1-y_2BD3hAGo4JEWKlc5CcNilH_1fUQNtA8DEh0ZWGKRMMm1kzUX6Rp1GuqWThZbYznEW3Yyfl9Asl3BAc0sSMUcjhy0MghcAd9Qz_Q9xcwRcaC0LshDwAvRhGWN6xVWCQgMx6zSzGCGGQ4XVFxrwvz5yRI8PrlAm-dbTD9hGfGLI4rLpgSIKtzRknA"))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.responseStatus", Matchers.equalTo("BAD_REQUEST")))
                .andExpect(jsonPath("$.data", Matchers.hasKey("status")))
                .andExpect(jsonPath("$.data", Matchers.hasKey("type")))
                .andExpect(jsonPath("$.data", Matchers.hasKey("path")))
                .andExpect(jsonPath("$.data", Matchers.hasKey("error")))
                .andExpect(jsonPath("$.message", Matchers.equalTo("ServerException : Error while fetching timezone details")))
                .andReturn()
                .getResponse();
        assertEquals(mockHttpServletResponse.getStatus(), HttpStatus.BAD_REQUEST.value());
    }

    @Test
    void deleteServerValidations_InvalidIdentifier() throws Exception {
        String[] appIdentifiers = new String[2];
        appIdentifiers[0] = "abc";
        MockHttpServletResponse mockHttpServletResponse = mockMvc.perform(delete("/accounts/d681ef13-d-4917-jkhg-6c79b-1/applications")
                .queryParam("appIdentifiers",appIdentifiers)
                .header("authorization", "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.jURqssSqfLX48GplwhWdLGAtXbw6t3f37SNlByUUONem_KNbquy5hTydJY-KfX5wpfcKPJNPuLMtWn6tHS2LhyTV9PMpWCGmg99zWTEard0rWLtBoy5jphGvo15w_0XoOXSW6EaSN1m63IXH-4dogi3PNy3DdUKUFNGyw-YrQ5i3G9SQcdgvY_SwjJGM6_MDA6lFtsPijDYcyXJgxSdCYgbc79nJf6eOo1jIzbtXxyLEM06gW-SS31uXuBv3hIkn9Pu-udhjEyE2NnciSG9P-Au6Jpsy0m11BIvtwlJ5cE55ivb4jJh_WPCPTK_lqzt8bljL33Px6SQUKPzf9VZUyA"))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.responseStatus", Matchers.equalTo("BAD_REQUEST")))
                .andExpect(jsonPath("$.data", Matchers.hasKey("status")))
                .andExpect(jsonPath("$.data", Matchers.hasKey("type")))
                .andExpect(jsonPath("$.data", Matchers.hasKey("path")))
                .andExpect(jsonPath("$.data", Matchers.hasKey("error")))
                .andExpect(jsonPath("$.message", Matchers.equalTo("ServerException : Invalid Account Identifier")))
                .andReturn()
                .getResponse();
        assertEquals(mockHttpServletResponse.getStatus(), HttpStatus.BAD_REQUEST.value());
    }

    @Test
    void deleteServerValidations_MappedService() throws Exception {
        String[] appIdentifiers = new String[1];
        appIdentifiers[0] = "netbanking_1_DR";
        MockHttpServletResponse mockHttpServletResponse = mockMvc.perform(delete("/accounts/qa-d681ef13-d690-4917-jkhg-6c79b-1/applications")
                .queryParam("appIdentifiers",appIdentifiers)
                .header("authorization", "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.jURqssSqfLX48GplwhWdLGAtXbw6t3f37SNlByUUONem_KNbquy5hTydJY-KfX5wpfcKPJNPuLMtWn6tHS2LhyTV9PMpWCGmg99zWTEard0rWLtBoy5jphGvo15w_0XoOXSW6EaSN1m63IXH-4dogi3PNy3DdUKUFNGyw-YrQ5i3G9SQcdgvY_SwjJGM6_MDA6lFtsPijDYcyXJgxSdCYgbc79nJf6eOo1jIzbtXxyLEM06gW-SS31uXuBv3hIkn9Pu-udhjEyE2NnciSG9P-Au6Jpsy0m11BIvtwlJ5cE55ivb4jJh_WPCPTK_lqzt8bljL33Px6SQUKPzf9VZUyA"))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.responseStatus", Matchers.equalTo("BAD_REQUEST")))
                .andExpect(jsonPath("$.data", Matchers.hasKey("status")))
                .andExpect(jsonPath("$.data", Matchers.hasKey("type")))
                .andExpect(jsonPath("$.data", Matchers.hasKey("path")))
                .andExpect(jsonPath("$.data", Matchers.hasKey("error")))
                .andExpect(jsonPath("$.message", Matchers.equalTo("ServerException : Some services are still mapped to Application with Identifier '[" + appIdentifiers[0] +"]'. Please remove the mapped services first.")))
                .andReturn()
                .getResponse();
        assertEquals(mockHttpServletResponse.getStatus(), HttpStatus.BAD_REQUEST.value());
    }


}
