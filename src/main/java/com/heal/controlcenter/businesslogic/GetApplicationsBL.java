package com.heal.controlcenter.businesslogic;

import com.heal.configuration.pojos.Account;
import com.heal.controlcenter.beans.ControllerBean;
import com.heal.controlcenter.beans.TimezoneBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.beans.ViewApplicationServiceMappingBean;
import com.heal.controlcenter.dao.mysql.ControllerDao;
import com.heal.controlcenter.dao.mysql.TimeZoneDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.ApplicationPojo;
import com.heal.controlcenter.pojo.ClusterComponentDetails;
import com.heal.controlcenter.pojo.ServiceClusterDetails;
import com.heal.controlcenter.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class GetApplicationsBL implements BusinessLogic<String, UtilityBean<List<ControllerBean>>, List<ApplicationPojo>> {

    @Autowired
    UserValidationUtil userValidationUtil;
    @Autowired
    ControllerDao controllerDao;
    @Autowired
    DateTimeUtil dateTimeUtil;
    @Autowired
    TimeZoneDao timeZoneDao;
    @Autowired
    ClientValidationUtils clientValidationUtils;
    @Autowired
    ServerValidationUtils serverValidationUtils;

    private boolean clusterDataRequired;

    @Override
    public UtilityBean<String> clientValidation(String requestBody, String... requestParams) throws ClientException {

        String authKey = requestParams[0];
        clientValidationUtils.authKeyValidation(authKey);

        String accountIdentifier = requestParams[1];
        clientValidationUtils.accountIdentifierValidation(accountIdentifier);

        clusterDataRequired = Boolean.parseBoolean(requestParams[2]);

        HashMap<String, String> requestParamsMap = CommonUtils.buildRequestParams(authKey, accountIdentifier);

        return UtilityBean.<String>builder()
                .requestParams(requestParamsMap)
                .build();
    }

    @Override
    public UtilityBean<List<ControllerBean>> serverValidation(UtilityBean<String> utilityBean) throws ServerException {

        String authKey = utilityBean.getRequestParams().get(Constants.AUTH_KEY);
        String userId = serverValidationUtils.authKeyValidation(authKey);

        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
        Account account = serverValidationUtils.accountValidation(accountIdentifier);

        List<ControllerBean> accessibleApplications = userValidationUtil.getAccessibleApplicationsForUser(userId, account.getIdentifier());

        if (accessibleApplications == null || accessibleApplications.isEmpty()) {
            log.error("Applications unavailable for account [{}]", account.getIdentifier());
            throw new ServerException("Application unavailable for account " + account.getIdentifier());
        }

        return UtilityBean.<List<ControllerBean>>builder()
                .pojoObject(accessibleApplications)
                .requestParams(utilityBean.getRequestParams())
                .metadata(Map.of(Constants.USER_ID_KEY, userId))
                .build();
    }

    @Override
    public List<ApplicationPojo> process(UtilityBean<List<ControllerBean>> utilityBean) throws DataProcessingException {
        List<ControllerBean> accessibleApplications = utilityBean.getPojoObject();
        String userId = utilityBean.getMetadata().get(Constants.USER_ID_KEY).toString();

        if (accessibleApplications == null || accessibleApplications.isEmpty()) {
            log.error("No applications found for user [{}]", userId);
            return List.of();
        }

        List<ViewApplicationServiceMappingBean> mappedServices = controllerDao.getServicesMappedToApplication(accessibleApplications.get(0).getAccountId(), accessibleApplications.get(0).getIdentifier());

        return accessibleApplications.parallelStream()
                .map(c -> {
                    TimezoneBean timezoneBean;
                    try {
                        timezoneBean = timeZoneDao.getApplicationTimezoneDetails(c.getId());
                    } catch (Exception e) {
                        log.error("Timezone not found for application ID {}. Skipping timezone fields.", c.getId());
                        timezoneBean = null;
                    }
                    if (timezoneBean == null) {
                        log.warn("Timezone details not found for application [{}]. Skipping timezone fields.", c.getIdentifier());
                        return null;
                    }
                    return ApplicationPojo.builder()
                            .id(c.getId())
                            .identifier(c.getIdentifier())
                            .name(c.getName())
                            .environment(c.getEnvironment())
                            .lastModifiedBy(c.getLastModifiedBy())
                            .lastModifiedOn(dateTimeUtil.getGMTToEpochTime(c.getUpdatedTime()))
                            .services(getMappedServices(mappedServices))
                            .timezoneMilli(timezoneBean.getOffset())
                            .timeZoneString(timezoneBean.getTimeZoneId())
                            .build();

                }).filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private List<ServiceClusterDetails> getMappedServices(List<ViewApplicationServiceMappingBean> mappedServices) {
        if (!clusterDataRequired) {
            return mappedServices.parallelStream().map(t -> ServiceClusterDetails.builder()
                            .id(t.getServiceId())
                            .name(t.getServiceName())
                            .identifier(t.getServiceIdentifier())
                            .build())
                    .collect(Collectors.toList());
        }

        return mappedServices.parallelStream().map(service -> {
            List<ClusterComponentDetails> hostClusterComponentDetails;
            List<ClusterComponentDetails> componentClusterComponentDetails;
            try {
                hostClusterComponentDetails = controllerDao.getHostClusterComponentDetailsForService(service.getServiceIdentifier());
                componentClusterComponentDetails = controllerDao.getComponentClusterComponentDetailsForService(service.getServiceIdentifier());
            } catch (Exception e) {
                log.error("Error in fetching cluster information for service [{}]", service.getServiceIdentifier(), e);
                return null;
            }
            return ServiceClusterDetails.builder()
                    .id(service.getServiceId())
                    .name(service.getServiceName())
                    .identifier(service.getServiceIdentifier())
                    .hostCluster(hostClusterComponentDetails)
                    .componentCluster(componentClusterComponentDetails)
                    .build();
        }).collect(Collectors.toList());
    }
}

