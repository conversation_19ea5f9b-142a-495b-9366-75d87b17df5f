//package com.heal.controlcenter.businesslogic;
//
//import com.heal.configuration.entities.UserAccessDetails;
//import com.heal.configuration.pojos.Account;
//import com.heal.configuration.pojos.KpiCategoryDetails;
//import com.heal.configuration.pojos.Service;
//import com.heal.controlcenter.beans.*;
//import com.heal.controlcenter.dao.mysql.AccountsDao;
//import com.heal.controlcenter.dao.mysql.ControllerDao;
//import com.heal.controlcenter.dao.mysql.KPIDao;
//import com.heal.controlcenter.dao.mysql.ServiceDao;
//import com.heal.controlcenter.dao.mysql.entity.ComputedKpiBean;
//import com.heal.controlcenter.dao.mysql.entity.KpiDetailsBean;
//import com.heal.controlcenter.dao.mysql.entity.ServiceKpiThreshold;
//import com.heal.controlcenter.dao.opensearch.ServiceOpensearchRepo;
//import com.heal.controlcenter.exception.ClientException;
//import com.heal.controlcenter.exception.ControlCenterException;
//import com.heal.controlcenter.exception.DataProcessingException;
//import com.heal.controlcenter.exception.ServerException;
//import com.heal.controlcenter.pojo.StaticThresholdRules;
//import com.heal.controlcenter.service.CommonDataService;
//import com.heal.controlcenter.util.*;
//import org.apache.commons.lang3.tuple.Pair;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.MockitoAnnotations;
//import org.mockito.Spy;
//
//import java.sql.Timestamp;
//import java.util.Arrays;
//import java.util.Collections;
//import java.util.HashMap;
//import java.util.List;
//
//import static org.junit.jupiter.api.Assertions.*;
//import static org.mockito.Mockito.*;
//
//class GetServiceStaticThresholdBLTest {
//
//    @InjectMocks
//    private GetServiceStaticThresholdBL getServiceStaticThresholdBL;
//
//    @Mock
//    private CacheWrapper cacheWrapper;
//
//    @Mock
//    private CommonUtils commonUtils;
//
//    @Mock
//    private CommonDataService commonDataService;
//
//    @Mock
//    private KPIDao kpiDao;
//
//    @Mock
//    private ServiceDao serviceDao;
//
//    @Mock
//    private ServiceOpensearchRepo serviceOpensearchRepo;
//
//    @Mock
//    private ServerValidationUtils serverValidationUtils;
//
//    @BeforeEach
//    void setUp() throws ControlCenterException {
//        MockitoAnnotations.openMocks(this);
//
//        // Mock cacheWrapper methods
//        HashMap<Integer, ViewTypesBean> viewTypesIdMap = new HashMap<>();
//        ViewTypesBean lowThresholdViewType = new ViewTypesBean();
//        lowThresholdViewType.setTypeId(1);
//        lowThresholdViewType.setTypeName(Constants.THRESHOLD_SEVERITY_TYPE);
//        lowThresholdViewType.setSubTypeName(Constants.THRESHOLD_SEVERITY_TYPE_LOW);
//        lowThresholdViewType.setSubTypeId(1);
//
//        ViewTypesBean mediumThresholdViewType = new ViewTypesBean();
//        mediumThresholdViewType.setTypeId(1);
//        mediumThresholdViewType.setTypeName(Constants.THRESHOLD_SEVERITY_TYPE);
//        mediumThresholdViewType.setSubTypeName(Constants.THRESHOLD_SEVERITY_TYPE_MEDIUM);
//        mediumThresholdViewType.setSubTypeId(2);
//
//        ViewTypesBean highThresholdViewType = new ViewTypesBean();
//        highThresholdViewType.setTypeId(1);
//        highThresholdViewType.setTypeName(Constants.THRESHOLD_SEVERITY_TYPE);
//        highThresholdViewType.setSubTypeName(Constants.THRESHOLD_SEVERITY_TYPE_HIGH);
//        highThresholdViewType.setSubTypeId(3);
//
//        ViewTypesBean kpiTypeView = new ViewTypesBean();
//        kpiTypeView.setTypeId(2);
//        kpiTypeView.setTypeName(Constants.KPI_TYPE);
//        kpiTypeView.setSubTypeName("validKpiType");
//        kpiTypeView.setSubTypeId(4);
//
//        ViewTypesBean lesserOperationType = new ViewTypesBean();
//        lesserOperationType.setTypeId(3);
//        lesserOperationType.setTypeName(Constants.OPERATIONS_TYPE);
//        lesserOperationType.setSubTypeName(Constants.OPERATIONS_TYPE_LESSER_THAN);
//        lesserOperationType.setSubTypeId(5);
//
//        ViewTypesBean greaterOperationType = new ViewTypesBean();
//        greaterOperationType.setTypeId(3);
//        greaterOperationType.setTypeName(Constants.OPERATIONS_TYPE);
//        greaterOperationType.setSubTypeName(Constants.OPERATIONS_TYPE_GREATER_THAN);
//        greaterOperationType.setSubTypeId(6);
//
//        ViewTypesBean notBetweenOperationType = new ViewTypesBean();
//        notBetweenOperationType.setTypeId(3);
//        notBetweenOperationType.setTypeName(Constants.OPERATIONS_TYPE);
//        notBetweenOperationType.setSubTypeName(Constants.OPERATIONS_TYPE_NOT_BETWEEN);
//        notBetweenOperationType.setSubTypeId(7);
//
//        ViewTypesBean coreKpiTypeView = new ViewTypesBean();
//        coreKpiTypeView.setTypeId(2);
//        coreKpiTypeView.setTypeName(Constants.KPI_TYPE);
//        coreKpiTypeView.setSubTypeName("Core");
//        coreKpiTypeView.setSubTypeId(8);
//
//        viewTypesIdMap.put(1, lowThresholdViewType);
//        viewTypesIdMap.put(2, mediumThresholdViewType);
//        viewTypesIdMap.put(3, highThresholdViewType);
//        viewTypesIdMap.put(4, kpiTypeView);
//        viewTypesIdMap.put(5, lesserOperationType);
//        viewTypesIdMap.put(6, greaterOperationType);
//        viewTypesIdMap.put(7, notBetweenOperationType);
//        viewTypesIdMap.put(8, coreKpiTypeView);
//        when(cacheWrapper.getAllViewTypesIdMap()).thenReturn(viewTypesIdMap);
//
//        HashMap<Pair<String, String>, ViewTypesBean> viewTypesPairMap = new HashMap<>();
//        viewTypesPairMap.put(Pair.of(Constants.THRESHOLD_SEVERITY_TYPE, Constants.THRESHOLD_SEVERITY_TYPE_LOW), lowThresholdViewType);
//        viewTypesPairMap.put(Pair.of(Constants.THRESHOLD_SEVERITY_TYPE, Constants.THRESHOLD_SEVERITY_TYPE_MEDIUM), mediumThresholdViewType);
//        viewTypesPairMap.put(Pair.of(Constants.THRESHOLD_SEVERITY_TYPE, Constants.THRESHOLD_SEVERITY_TYPE_HIGH), highThresholdViewType);
//        viewTypesPairMap.put(Pair.of(Constants.OPERATIONS_TYPE, Constants.OPERATIONS_TYPE_LESSER_THAN), lesserOperationType);
//        viewTypesPairMap.put(Pair.of(Constants.OPERATIONS_TYPE, Constants.OPERATIONS_TYPE_GREATER_THAN), greaterOperationType);
//        viewTypesPairMap.put(Pair.of(Constants.OPERATIONS_TYPE, Constants.OPERATIONS_TYPE_NOT_BETWEEN), notBetweenOperationType);
//        viewTypesPairMap.put(Pair.of(Constants.KPI_TYPE, "validKpiType"), kpiTypeView);
//        viewTypesPairMap.put(Pair.of(Constants.KPI_TYPE, Constants.CORE_KPI_TYPE), coreKpiTypeView);
//
//        when(cacheWrapper.getAllViewTypesPairMap()).thenReturn(viewTypesPairMap);
//        when(commonUtils.getUserId(anyString())).thenReturn("testUserId");
//    }
//
//    @Test
//    void testClientValidation_ValidInputs() throws ClientException {
//        String authKey = "validAuthKey";
//        String accountIdentifier = "validAccount";
//        String serviceIdentifier = "validService";
//        String kpiType = "validKpiType";
//        String thresholdType = "static";
//        String userId = "testUserId";
//
//        String[] requestParams = {authKey, accountIdentifier, serviceIdentifier, kpiType, thresholdType};
//
//        UtilityBean<Object> result = getServiceStaticThresholdBL.clientValidation(null, requestParams);
//
//        assertNotNull(result);
//        assertEquals(accountIdentifier, result.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER));
//        assertEquals(serviceIdentifier, result.getRequestParams().get(Constants.SERVICE_IDENTIFIER));
//        assertEquals(kpiType, result.getRequestParams().get(Constants.KPI_TYPE));
//        assertEquals(thresholdType, result.getRequestParams().get(Constants.THRESHOLD_TYPE));
//    }
//
//    @Test
//    void testClientValidation_InvalidThresholdType() {
//        String[] requestParams = {"validAuthKey", "validAccount", "validService", "validKpiType", ""};
//
//        ClientException exception = assertThrows(ClientException.class, () ->
//                getServiceStaticThresholdBL.clientValidation(null, requestParams));
//
//        assertEquals("ClientException : " +  UIMessages.THRESHOLD_TYPE_INVALID, exception.getMessage());
//    }
//
////    Server Validation Test Cases
//    @Test
//    void testServerValidation_ValidInputs() throws ServerException, ControlCenterException {
//        UtilityBean<Object> utilityBean = UtilityBean.builder()
//                .requestParams(new HashMap<String, String>() {{
//                    put("KPI", "validKpiType");
//                    put("accountIdentifier", "validAccount");
//                    put("serviceIdentifier", "validService");
//                }})
//                .userId("testUserId")
//                .build();
//
//        Account accountBean = new Account();
//        accountBean.setIdentifier("validAccount");
//        accountBean.setName("Valid Account");
//        accountBean.setId(1);
//        accountBean.setLastModifiedBy("testUserId");
//        Service controllerBean = new Service();
//        controllerBean.setId(1);
//        com.heal.configuration.entities.UserAccessDetails userAccessDetails = new UserAccessDetails();
//
//        when(serverValidationUtils.accountValidation("validAccount")).thenReturn(accountBean);
//        when(serverValidationUtils.serviceValidation(anyString(), anyString(), any())).thenReturn(controllerBean);
//        when(serverValidationUtils.userAccessDetailsValidation("testUserId", "validAccount")).thenReturn(userAccessDetails);
//
//        UtilityBean<Service> result = getServiceStaticThresholdBL.serverValidation(utilityBean);
//
//        assertNotNull(result);
//        assertEquals(controllerBean, result.getPojoObject());
//    }
//
//    @Test
//    void testServerValidation_InvalidKpiType() throws ServerException {
//        UtilityBean<Object> utilityBean = UtilityBean.builder()
//                .requestParams(new HashMap<String, String>() {{
//                    put("KPI", "invalidKpiType");
//                    put("accountIdentifier", "validAccount");
//                    put("serviceIdentifier", "validService");
//                }})
//                .userId("testUserId")
//                .build();
//
//        Account accountBean = new Account();
//        accountBean.setIdentifier("validAccount");
//        Service controllerBean = new Service();
//        controllerBean.setId(1);
//        com.heal.configuration.entities.UserAccessDetails userAccessDetails = new UserAccessDetails();
//
//        when(serverValidationUtils.accountValidation("validAccount")).thenReturn(accountBean);
//        when(serverValidationUtils.serviceValidation(anyString(), anyString(), any())).thenReturn(controllerBean);
//        when(serverValidationUtils.userAccessDetailsValidation("testUserId", "validAccount")).thenReturn(userAccessDetails);
//
//        ServerException exception = assertThrows(ServerException.class, () ->
//                getServiceStaticThresholdBL.serverValidation(utilityBean));
//
//        assertEquals("ServerException : " + UIMessages.INVALID_KPI_TYPE, exception.getMessage());
//    }
//
//    // Process Test
//    @Test
//    void testProcess_ValidInputs() throws ControlCenterException, DataProcessingException {
//        Service controllerBean = new Service();
//        controllerBean.setId(1);
//        controllerBean.setName("validService");
//        controllerBean.setIdentifier("validAccount");
//        controllerBean.setAccountId(1);
//
//        UtilityBean<Service> utilityBean = UtilityBean.<Service>builder()
//                .requestParams(new HashMap<String, String>() {{
//                    put("KPI", "validKpiType");
//                    put("accountIdentifier", "validAccount");
//                    put("serviceIdentifier", "validService");
//                }})
//                .userId("testUserId")
//                .pojoObject(controllerBean)
//                .build();
//
//        AccountBean accountBean = new AccountBean();
//        accountBean.setIdentifier("validAccount");
//        CompInstClusterDetailsBean compInstClusterDetailsBean = new CompInstClusterDetailsBean();
//        compInstClusterDetailsBean.setCompId(1);
//        compInstClusterDetailsBean.setCompVersionId(1);
//        compInstClusterDetailsBean.setInstanceId(1);
//        compInstClusterDetailsBean.setCommonVersionId(1);
//
//        CompInstClusterDetailsBean compInstClusterDetailsBean2 = new CompInstClusterDetailsBean();
//        compInstClusterDetailsBean2.setCompId(2);
//        compInstClusterDetailsBean2.setCompVersionId(2);
//        compInstClusterDetailsBean2.setInstanceId(2);
//        compInstClusterDetailsBean2.setCommonVersionId(2);
//
//        ComputedKpiBean computedKpiBean = new ComputedKpiBean();
//        computedKpiBean.setComputedKpiId(1);
//        computedKpiBean.setFormula("validFormula");
//        computedKpiBean.setDisplayFormula("validDisplayFormula");
//
//        KpiDetailsBean kpiDetailsBean = new KpiDetailsBean();
//        kpiDetailsBean.setId(1);
//        kpiDetailsBean.setName("validKpiType");
//        kpiDetailsBean.setTypeId(4);
//
//        KpiDetailsBean kpiDetailsBean1 = new KpiDetailsBean();
//        kpiDetailsBean1.setId(2);
//        kpiDetailsBean1.setName("validKpiType1");
//        kpiDetailsBean1.setTypeId(4);
//
//        KpiDetailsBean kpiDetailsBean5 = new KpiDetailsBean();
//        kpiDetailsBean5.setId(5);
//        kpiDetailsBean5.setName("validKpiType5");
//        kpiDetailsBean5.setTypeId(4);
//
//        KpiCategoryDetails kpiCategoryDetails = new KpiCategoryDetails();
//        kpiCategoryDetails.setId(1);
//        kpiCategoryDetails.setName("validKpiType");
//
//        KpiCategoryDetails kpiCategoryDetails2 = new KpiCategoryDetails();
//        kpiCategoryDetails2.setId(2);
//        kpiCategoryDetails2.setName("validKpiType2");
//
//        KpiCategoryDetails kpiCategoryDetails3 = new KpiCategoryDetails();
//        kpiCategoryDetails3.setId(3);
//        kpiCategoryDetails3.setName("validKpiType3");
//
//        KpiCategoryDetails kpiCategoryDetails4 = new KpiCategoryDetails();
//        kpiCategoryDetails3.setId(5);
//        kpiCategoryDetails3.setName("validKpiType5");
//
//        ServiceKpiThreshold serviceKpiThreshold = new ServiceKpiThreshold();
//        serviceKpiThreshold.setKpiId(1);
//        serviceKpiThreshold.setOperationTypeId(5);
//        serviceKpiThreshold.setThresholdSeverityId(1);
//        serviceKpiThreshold.setMinThreshold(1.0);
//        serviceKpiThreshold.setApplicableTo("instances");
//        serviceKpiThreshold.setDefinedBy(Constants.THRESHOLD_DEFINED_BY_USER);
//        serviceKpiThreshold.setStatus(1);
//        serviceKpiThreshold.setStartTime(new Timestamp(System.currentTimeMillis()));
//
//        ServiceKpiThreshold serviceKpiThreshold2 = new ServiceKpiThreshold();
//        serviceKpiThreshold2.setKpiId(7);
//        serviceKpiThreshold2.setOperationTypeId(7);
//        serviceKpiThreshold2.setThresholdSeverityId(3);
//        serviceKpiThreshold2.setMinThreshold(1.0);
//        serviceKpiThreshold2.setDefinedBy(Constants.THRESHOLD_DEFINED_BY_SYSTEM);
//        serviceKpiThreshold2.setStatus(1);
//        serviceKpiThreshold2.setStartTime(new Timestamp(System.currentTimeMillis()));
//        serviceKpiThreshold2.setApplicableTo("instances");
//
//        ServiceKpiThreshold serviceKpiThreshold3 = new ServiceKpiThreshold();
//        serviceKpiThreshold3.setKpiId(2);
//        serviceKpiThreshold3.setOperationTypeId(7);
//        serviceKpiThreshold3.setThresholdSeverityId(3);
//        serviceKpiThreshold3.setMinThreshold(1.0);
//        serviceKpiThreshold3.setDefinedBy(Constants.THRESHOLD_DEFINED_BY_SYSTEM);
//        serviceKpiThreshold3.setStatus(1);
//        serviceKpiThreshold3.setStartTime(new Timestamp(System.currentTimeMillis()));
//        serviceKpiThreshold3.setApplicableTo("instances");
//
//        ServiceKpiThreshold serviceKpiThreshold4 = new ServiceKpiThreshold();
//        serviceKpiThreshold4.setKpiId(2);
//        serviceKpiThreshold4.setOperationTypeId(7);
//        serviceKpiThreshold4.setThresholdSeverityId(1);
//        serviceKpiThreshold4.setMinThreshold(1.0);
//        serviceKpiThreshold4.setDefinedBy(Constants.THRESHOLD_DEFINED_BY_SYSTEM);
//        serviceKpiThreshold4.setStatus(1);
//        serviceKpiThreshold4.setStartTime(new Timestamp(System.currentTimeMillis()));
//        serviceKpiThreshold4.setApplicableTo("clusters");
//
//        when(commonDataService.getComponentClusterList(1,1)).thenReturn(Arrays.asList(compInstClusterDetailsBean));
//        when(serviceDao.getAllKpiThresholds(1,1)).thenReturn(Arrays.asList(serviceKpiThreshold,serviceKpiThreshold3, serviceKpiThreshold4,serviceKpiThreshold2));
//        when(kpiDao.getComputedExpression(1)).thenReturn(Arrays.asList(computedKpiBean));
//        when(kpiDao.getKpiList(1,1)).thenReturn(Arrays.asList(kpiDetailsBean,kpiDetailsBean1,kpiDetailsBean5));
//        when(kpiDao.getKpiList(2,2)).thenReturn(Arrays.asList());
//        when(kpiDao.getAllKpiCategoryDetails()).thenReturn(Arrays.asList(kpiCategoryDetails,kpiCategoryDetails2,kpiCategoryDetails3,kpiCategoryDetails4));
//        doNothing().when(serviceOpensearchRepo).createThreshold(anyString(), anyString(), any(), any(), anyString());
//        when(serviceOpensearchRepo.getServiceLevelThresholdDetails(anyString(), anyString(), anyString(), anyString()))
//                .thenReturn(Collections.emptyList());
//
//        List<StaticThresholdRules> result = getServiceStaticThresholdBL.process(utilityBean);
//
//        assertEquals(4, result.size());
//    }
//
//}
