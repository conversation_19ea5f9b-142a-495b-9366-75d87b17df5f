package com.heal.controlcenter.util;

public class LogMessages {

    public static final String TAG_MAPPING_ALREADY_EXISTS = "Data is already available in tag_mapping table for tagId- %s ,objId - %s ,objectName - %s ,tagKey:- %s ,tagValue- %s ,accountId :-%s";
    public static final String TAG_MAPPING_ADD_SUCCESS = "Tag mapping data is added successfully tagId: %s accountId: %s";
    public static final String TAG_MAPPING_ADD_FAILURE = "Failed to add the Tag mapping data tagId: %s accountId: %s";
    public static final String USER_ACCESS_DETAILS_UNAVAILABLE = "Access details unavailable for user:%s, Account: %s. Returning empty page.";
    public static final String USER_HAS_NO_ACCESSIBLE_SERVICES = "User %s has no accessible services in account %s. Returning empty page.";
    public static final String NO_SERVICES_FOUND = "No services found for account: %s with the given criteria.";
    public static final String ERROR_PARSING_DATES_FOR_SERVICE = "Error parsing dates for service %s: %s";
    public static final String NO_SERVICE_GROUPS_FOUND = "No service groups found for account: %s with the given criteria.";
    public static final String ERROR_PARSING_DATES_FOR_SERVICE_GROUP = "Error parsing dates for service group %s: %s";
    public static final String CONTROLLER_ADD_UPDATE_SUCCESS = "Controller %s with ID %s %s successfully.";
    public static final String PAGEABLE_NULL_WARNING = "Pageable is null in process. Defaulting to first page with 20 items";
    public static final String SEARCH_TERM_NULL_WARNING = "Search term is null in process. Defaulting to empty string";
    public static final String HEAL_HEALTH_ACCOUNT_NOT_FOUND = "heal_health account not found in DB";
    public static final String REMOVE_DUPLICATES_COMPLETED = "removeDuplicates completed in {} ms (preExisting: {}, staging: {})";
    public static final String FETCHING_PRE_EXISTING_DATA = "Fetching preExistingData for accountId: {}";
    public static final String TIME_TAKEN_FOR_INSTANCE_BEANS = "Time taken for instanceBeans:{} ms";
    public static final String TIME_TAKEN_FOR_HOST_INSTANCE_BEANS = "Time taken for hostInstanceBeans:{} ms";
    public static final String TIME_TAKEN_FOR_HOST_COMPONENT_INSTANCE_MAP = "Time taken for hostComponentInstanceMap:{} ms";
    public static final String TIME_TAKEN_FOR_HOST_CLUSTER_MAP = "Time taken for hostClusterMap:{} ms";
    public static final String TIME_TAKEN_FOR_CLUSTER_SERVICES_BEAN_MAP = "Time taken for clusterServicesBeanMap:{} ms";
    public static final String TIME_TAKEN_FOR_SERVICE_APPLICATIONS_BEAN_MAP = "Time taken for serviceApplicationsBeanMap:{} ms";
    public static final String TIME_TAKEN_FOR_AGENT_BEAN_MAP = "Time taken for agentBeanMap:{} ms";
    public static final String TIME_TAKEN_FOR_INST_COMP_ATTRIBUTES_MAP = "Time taken for instCompAttributesMap:{} ms";
    public static final String TIME_TAKEN_FOR_GET_HOST_LIST = "Time taken for getHostList:{} ms";
    public static final String PRE_EXISTING_DATA_COMPLETED = "preExistingData for accountId {} completed in {} ms";
    public static final String FETCHING_AUTO_DISCOVERY_PART_DATA = "Fetching autoDiscoveryPart data";
    public static final String AUTO_DISCOVERY_PART_COMPLETED = "autoDiscoveryPart completed in {} ms";
    public static final String STARTING_PROCESS_METHOD = "Starting process method for account: {}, user: {}, searchTerm: '{}', pageable: {}";
    public static final String RETURNING_HOSTS_COUNT = "Returning {} hosts out of {} total for account: {}";
    public static final String PROCESS_COMPLETED_FOR_ACCOUNT = "process completed for account {} in {} ms";
    public static final String CLIENT_VALIDATION_COMPLETED = "clientValidation(requestParams: {}) completed in {} ms";
    public static final String SERVER_VALIDATION_COMPLETED = "serverValidation(authKey: {}, userId: {}) completed in {} ms";
    public static final String SEARCH_TERM_EMPTY_OR_NULL = "Search term is empty or null. Defaulting to empty string.";
    public static final String CLIENT_VALIDATION_COMPLETED_IN_MS = "clientValidation completed in {} ms";
    public static final String FAILED_TO_PROCESS_USER_ACCESS = "Failed to process user access for userId={}";
    public static final String SERVER_VALIDATION_COMPLETED_IN_MS = "serverValidation completed in {} ms";
    public static final String PROCESSING_ACCOUNTS_WITH_PAGEABLE_AND_SEARCH_TERM = "Processing accounts with Pageable: {} and SearchTerm: '{}'";
    public static final String PROCESSING_ACCOUNTS_FOR_USER_ID = "Processing accounts for userId={}, search='{}', page={}";
    public static final String FOUND_ACCESSIBLE_ACCOUNTS = "Found {} accessible accounts for userId={} with search='{}'";
    public static final String ATTEMPTING_TO_FETCH_TENANT_ID = "[process] Attempting to fetch tenant ID mapped to account ID: {}";
    public static final String MAPPED_TENANT_ID = "[process] Mapped tenant ID for account ID [{}] is: {}";
    public static final String FETCHING_TENANT_DETAILS = "[process] Fetching tenant details for tenant ID: {}";
    public static final String SUCCESSFULLY_RETRIEVED_TENANT_DETAILS = "[process] Successfully retrieved tenant details for tenant ID: {}";
    public static final String NO_TENANT_DETAILS_FOUND = "[process] No tenant details found for tenant ID: {}";
    public static final String NO_TENANT_MAPPING_FOUND = "[process] No tenant mapping found for account ID: {}. Proceeding with tenantDetails as null.";
    public static final String SKIPPING_ACCOUNT_DUE_TO_PROCESSING_ERROR = "Skipping account ID {} due to processing error";
    public static final String APPLYING_IN_MEMORY_SORTING = "Applying in-memory sorting as per pageable sort: {}";
    public static final String SORTING_COMPLETE = "Sorting complete. First account after sort: {}";
    public static final String EXCEPTION_OCCURRED_IN_PROCESSING_ACCOUNTS = "Exception occurred in processing accounts";
    public static final String PROCESS_COMPLETED_IN_MS = "process() completed in {} ms";
}