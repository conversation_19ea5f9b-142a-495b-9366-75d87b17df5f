package com.heal.controlcenter.dao.redis;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.configuration.entities.UserAccessDetails;
import com.heal.controlcenter.util.RedisUtilities;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
@Slf4j
public class UserRepo {

    @Autowired
    ObjectMapper objectMapper;
    @Autowired
    RedisUtilities redisUtilities;

    public List<UserAccessDetails> getUserAccessDetails(String userIdentifier) {
        String key = "/users/" + userIdentifier + "/accessDetails";
        String hashKey = "USERS_" + userIdentifier + "_ACCESSDETAILS";
        try {
            String userAccessDetails = redisUtilities.getKey(key, hashKey);
            if (userAccessDetails == null || userAccessDetails.trim().isEmpty()) {
                log.warn("Null/Empty value found for key {} in redis.", key);
                return null;
            }

            return objectMapper.readValue(userAccessDetails, new TypeReference<List<UserAccessDetails>>() {
            });

        } catch (Exception e) {
            log.error("Exception encountered while getting user details from key:{}, hashKey:{}", key, hashKey, e);
            return null;
        }
    }
}
