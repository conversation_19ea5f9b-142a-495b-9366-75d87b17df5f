package com.heal.controlcenter.beans;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.heal.controlcenter.dao.mysql.MasterDataDao;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.util.DateTimeUtil;
import lombok.*;
import org.springframework.beans.factory.annotation.Autowired;

import static com.heal.controlcenter.util.Constants.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CommandDetailArgumentBean {

    private int id;
    private int commandId;
    @NonNull
    private String argumentKey;
    @NonNull
    private String argumentValue;
    @NonNull
    private String defaultValue;
    private int argumentTypeId;
    private int argumentValueTypeId;
    private String lastModifiedBy;
    private String createdTime;
    private String updatedTime;
    private boolean placeHolder = false;

    @JsonIgnore
    @Autowired
    DateTimeUtil dateTimeUtil;
    @JsonIgnore
    @Autowired
    MasterDataDao masterDataDao;

    public CommandDetailArgumentBean getInstance(int commandId, String userId, String key, String value) throws DataProcessingException {
        int argValueTypeId;
        int argTypeId;
        try {
            argValueTypeId = masterDataDao.getViewTypesFromMstTypeAndSubTypeName(
                    MST_TYPE_ATTRIBUTE_TYPE, MST_SUB_TYPE_TEXT_BOX).getSubTypeId();
            argTypeId = masterDataDao.getViewTypesFromMstTypeAndSubTypeName(
                    MST_TYPE_SCRIPT_PARAM_TYPE, MST_SUB_TYPE_COMMAND_LINE).getSubTypeId();
        } catch (ControlCenterException e) {
            throw new DataProcessingException(e.getMessage());
        }

        return CommandDetailArgumentBean.builder()
                .commandId(commandId)
                .argumentKey(key)
                .argumentValue(value)
                .defaultValue(value)
                .lastModifiedBy(userId)
                .createdTime(String.valueOf(dateTimeUtil.getCurrentTimestampInGMT()))
                .updatedTime(String.valueOf(dateTimeUtil.getCurrentTimestampInGMT()))
                .argumentTypeId(argTypeId)
                .argumentValueTypeId(argValueTypeId)
                .placeHolder(false)
                .build();
    }

    public CommandDetailArgumentBean getInstance(int commandId, String userId, String key, String value,String defaultValue,
                                                        int argTypeId,int argValueTypeId){

        return CommandDetailArgumentBean.builder()
                .commandId(commandId)
                .argumentKey(key)
                .argumentValue(value)
                .defaultValue(defaultValue)
                .lastModifiedBy(userId)
                .createdTime(String.valueOf(dateTimeUtil.getCurrentTimestampInGMT()))
                .updatedTime(String.valueOf(dateTimeUtil.getCurrentTimestampInGMT()))
                .argumentTypeId(argTypeId)
                .argumentValueTypeId(argValueTypeId)
                .placeHolder(false)
                .build();
    }
}
