package com.heal.controlcenter.controller;

import com.heal.controlcenter.aop.AopCustomAnnotation;
import com.heal.controlcenter.beans.AccountServiceValidationBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.pojo.BasicUserDetails;
import com.heal.controlcenter.pojo.ResponsePojo;
import com.heal.controlcenter.pojo.ServiceGroupListPage;
import com.heal.controlcenter.swagger.CommonApiResponses;
import com.heal.controlcenter.businesslogic.GetAccountServiceGroupsBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.JsonFileParser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@Configuration
@RestController
public class ServiceGroupController {

    @Autowired
    GetAccountServiceGroupsBL getAccountServiceGroupsBL;
    @Autowired
    JsonFileParser headersParser;

    @Operation(
            summary = "Retrieve Service Groups for Account",
            responses = {
                    @ApiResponse(
                            responseCode = "200", description = "Service groups retrieved successfully",
                            content = @Content(
                                    schema = @Schema(
                                            implementation = Page.class
                                    )
                            )
                    )
            }
    )
    @CommonApiResponses
    @AopCustomAnnotation
    @GetMapping(value = "/accounts/{accountIdentifier}/service-groups")
    public ResponseEntity<ResponsePojo<Page<ServiceGroupListPage>>> getAccountServiceGroups(
            @Parameter(
                    name = "accountIdentifier",
                    description = "Unique identifier of the account",
                    required = true,
                    example = "account-001"
            )
            @PathVariable("accountIdentifier") String accountIdentifier,
            @Parameter(
                    description = "Pagination and sorting information. " +
                            "Use `page` (0-indexed), `size` (items per page), and `sort` (property[,asc|desc]). " +
                            "Example: `?page=0&size=10&sort=name,asc`",
                    required = false
            )
            Pageable pageable,
            @Parameter(
                    description = "Search term to filter service groups by name or identifier.",
                    required = false,
                    example = "TestGroup")
            @RequestParam(required = false) String searchTerm, BasicUserDetails userDetails)
            throws ClientException, DataProcessingException, ServerException {

        UtilityBean<Object> utilityBean = getAccountServiceGroupsBL.clientValidation(null, accountIdentifier, searchTerm);
        utilityBean.setPageable(pageable);
        utilityBean.getMetadata().put(Constants.USER_ID_KEY, userDetails.getUserIdentifier());
        UtilityBean<AccountServiceValidationBean> validatedBean = getAccountServiceGroupsBL.serverValidation(utilityBean);
        Page<ServiceGroupListPage> serviceGroups = getAccountServiceGroupsBL.process(validatedBean);

        ResponsePojo<Page<ServiceGroupListPage>> responsePojo = new ResponsePojo<>("success",
                serviceGroups, HttpStatus.OK);

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
    }
}
