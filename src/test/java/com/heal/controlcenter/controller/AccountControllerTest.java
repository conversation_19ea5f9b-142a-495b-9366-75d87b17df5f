package com.heal.controlcenter.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.controlcenter.beans.AccessDetailsBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.AddAccountBL;
import com.heal.controlcenter.businesslogic.GetAccountsBL;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.Account;
import com.heal.controlcenter.pojo.Tags;
import com.heal.controlcenter.pojo.ThresholdSeverity;
import com.heal.controlcenter.util.JsonFileParser;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.web.PageableHandlerMethodArgumentResolver;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@ExtendWith(MockitoExtension.class)
class AccountControllerTest {

    @InjectMocks
    private AccountController accountController;

    @Mock
    private GetAccountsBL getAccountsBL;
    @Mock
    private AddAccountBL addAccountBL;
    @Mock
    private JsonFileParser jsonFileParser;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;
    private Account account;
    private static final String MOCK_AUTH_TOKEN = "Bearer mockToken";

    @BeforeEach
    void setup() {
        PageableHandlerMethodArgumentResolver pageableResolver = new PageableHandlerMethodArgumentResolver();

        mockMvc = MockMvcBuilders
                .standaloneSetup(accountController)
                .setCustomArgumentResolvers(pageableResolver)
                .build();

        objectMapper = new ObjectMapper();

        account = Account.builder()
                .identifier("acc-identifier")
                .accountName("Test Account")
                .status(1)
                .closingWindow(10)
                .maxDataBreaks(5)
                .thresholdSeverity(ThresholdSeverity.builder()
                        .low(true)
                        .warning(false)
                        .critical(true)
                        .build())
                .tags(List.of(new Tags("Timezone", "Asia/Kolkata")))
                .build();
    }


    @Test
    void getAccounts_WhenSuccess() throws Exception {
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", MOCK_AUTH_TOKEN);

        PageRequest pageable = PageRequest.of(0, 10);
        List<Account> accountList = List.of(account);
        Page<Account> mockPage = new PageImpl<>(accountList, pageable, 1);

        UtilityBean<String> mockUtilityBean = UtilityBean.<String>builder()
                .requestParams(Map.of("Authorization", MOCK_AUTH_TOKEN, "searchTerm", "Test"))
                .pageable(pageable)
                .build();

        AccessDetailsBean accessDetails = new AccessDetailsBean();

        when(jsonFileParser.loadHeaderConfiguration()).thenReturn(headers);
        when(getAccountsBL.clientValidation(null, MOCK_AUTH_TOKEN, "Test")).thenReturn(mockUtilityBean);
        when(getAccountsBL.serverValidation(mockUtilityBean)).thenReturn(
                UtilityBean.<AccessDetailsBean>builder().pojoObject(accessDetails).build());
        when(getAccountsBL.process(any())).thenReturn(mockPage);

        mockMvc.perform(get("/accounts")
                        .header("Authorization", MOCK_AUTH_TOKEN)
                        .param("searchTerm", "Test")
                        .param("page", "0")
                        .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(header().string("Authorization", MOCK_AUTH_TOKEN))
                .andExpect(jsonPath("$.message").value("Accounts fetched successfully."))
                .andExpect(jsonPath("$.responseStatus").value("OK"))
                .andExpect(jsonPath("$.data.content").isArray())
                .andExpect(jsonPath("$.data.content.length()").value(1))
                .andExpect(jsonPath("$.data.content[0].identifier").value("acc-identifier"));
    }

    @Test
    void getAccounts_WhenMissingAuthorization() throws Exception {
        mockMvc.perform(get("/accounts"))
                .andExpect(status().isBadRequest());
    }

    @Test
    void postAccount_WhenValidInput() throws Exception {
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", MOCK_AUTH_TOKEN);

        UtilityBean<Account> utilityBean = UtilityBean.<Account>builder()
                .pojoObject(account)
                .requestParams(Map.of("Authorization", MOCK_AUTH_TOKEN, "accountIdentifier", account.getIdentifier()))
                .metadata(Map.of("userId", "mock-user"))
                .build();

        when(jsonFileParser.loadHeaderConfiguration()).thenReturn(headers);
        when(addAccountBL.clientValidation(any(Account.class), anyString(), anyString())).thenReturn(utilityBean);
        when(addAccountBL.serverValidation(any())).thenReturn(utilityBean);
        when(addAccountBL.process(any())).thenReturn(account);

        mockMvc.perform(post("/accounts")
                        .header("Authorization", MOCK_AUTH_TOKEN)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(account)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.message").value("Account created successfully ."))
                .andExpect(jsonPath("$.responseStatus").value("OK"));
    }

    @Test
    void postAccount_WhenMissingAuthorization() throws Exception {
        mockMvc.perform(post("/accounts")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(account)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void postAccount_WhenServerExceptionThrown() throws Exception {
        UtilityBean<Account> utilityBean = UtilityBean.<Account>builder()
                .pojoObject(account)
                .requestParams(Map.of("Authorization", MOCK_AUTH_TOKEN))
                .build();

        when(addAccountBL.clientValidation(any(Account.class), anyString(), anyString()))
                .thenReturn(utilityBean);
        when(addAccountBL.serverValidation(any()))
                .thenThrow(new ServerException("Server error"));

        mockMvc.perform(post("/accounts")
                        .header("Authorization", MOCK_AUTH_TOKEN)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(account)))
                .andExpect(status().isInternalServerError());
    }
}
