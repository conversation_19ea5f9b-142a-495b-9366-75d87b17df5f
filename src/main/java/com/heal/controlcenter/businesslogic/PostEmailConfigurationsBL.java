package com.heal.controlcenter.businesslogic;

import com.heal.controlcenter.beans.AccountBean;
import com.heal.controlcenter.beans.SMTPDetailsBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.beans.ViewTypesBean;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.dao.mysql.MasterDataDao;
import com.heal.controlcenter.dao.mysql.NotificationsDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.SMTPDetailsPojo;
import com.heal.controlcenter.util.*;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.crypto.DataLengthException;
import org.bouncycastle.crypto.InvalidCipherTextException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class PostEmailConfigurationsBL implements BusinessLogic<SMTPDetailsPojo, UtilityBean<SMTPDetailsPojo>, Object> {

    @Autowired
    CommonUtils commonUtils;
    @Autowired
    AccountsDao accountsDao;
    @Autowired
    NotificationsDao notificationsDao;
    @Autowired
    MasterDataDao masterDataDao;
    @Autowired
    AECSBouncyCastleUtil aecsBouncyCastleUtil;
    @Autowired
    DateTimeUtil dateTimeUtil;
    @Autowired
    ClientValidationUtils clientValidationUtils;
    @Autowired
    ServerValidationUtils serverValidationUtils;

    private ViewTypesBean securityType;

    /**
     * Validates client-side input for SMTP configuration, including authKey and accountIdentifier.
     * Also invokes the SMTPDetailsPojo's own validation logic for mandatory field checks.
     * Returns a UtilityBean containing the validated request and SMTP details.
     * Throws ClientException if validation fails.
     */
    @Override
    public UtilityBean<SMTPDetailsPojo> clientValidation(SMTPDetailsPojo smtpDetails, String... requestParams) throws ClientException {

        String authKey = requestParams[0];
        clientValidationUtils.authKeyValidation(authKey);

        String accountIdentifier = requestParams[1];
        clientValidationUtils.accountIdentifierValidation(accountIdentifier);

        Map<String, String> error = smtpDetails.validate();
        if (error != null && !error.isEmpty()) {
            String err = error.toString();
            log.error(err);
            throw new ClientException(err);
        }

        HashMap<String, String> requestParamsMap = CommonUtils.buildRequestParams(authKey, accountIdentifier);

        return UtilityBean.<SMTPDetailsPojo>builder()
                .requestParams(requestParamsMap)
                .pojoObject(smtpDetails)
                .build();
    }

    /**
     * Performs server-side validation such as user authorization and account verification.
     * Decrypts and re-encrypts SMTP password to ensure valid encryption format.
     * Validates the security type for the SMTP protocol using master data lookup.
     * Throws ServerException if validation fails at any step.
     */
    @Override
    public UtilityBean<SMTPDetailsPojo> serverValidation(UtilityBean<SMTPDetailsPojo> utilityBean) throws ServerException {
        String authKey = utilityBean.getRequestParams().get(Constants.AUTH_KEY);
        String userId = serverValidationUtils.authKeyValidation(authKey);

        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
        AccountBean account = accountsDao.getAccountByIdentifier(accountIdentifier);
        if (account == null) {
            String message = String.format(UIMessages.ACCOUNT_IDENTIFIER_UNAVAILABLE,accountIdentifier);
            log.error(message);
            throw new ServerException(message);
        }

        utilityBean.setMetadata(Map.of(Constants.ACCOUNT, account));

        SMTPDetailsBean smtpDetailsBeanExists = notificationsDao.getSMTPDetails(account.getId());
        if (smtpDetailsBeanExists != null) {
            log.error("SMTP settings are already available for accountId [{}].", account.getId());
            throw new ServerException("SMTP settings already present.");
        }

        SMTPDetailsPojo smtpDetails = utilityBean.getPojoObject();
        String plainTxt = "";

        try {
            securityType = masterDataDao.getViewTypesFromMstTypeAndSubTypeName(Constants.SMTP_PROTOCOLS, smtpDetails.getSecurity());
        } catch (HealControlCenterException e) {
            throw new ServerException(e.getMessage());
        }

        if (securityType == null) {
            String err = "Security type details is unavailable for security type: ".concat(smtpDetails.getSecurity());
            log.error(err);
            throw new ServerException(err);
        }

        if (smtpDetails.getPassword() == null || smtpDetails.getPassword().trim().isEmpty()) {
            smtpDetails.setPassword("");
        } else {
            try {
                plainTxt = aecsBouncyCastleUtil.decrypt(smtpDetails.getPassword());
                if (plainTxt.isEmpty()) {
                    String err = "Error occurred, Password is not encrypted properly.";
                    log.error(err);
                    throw new ServerException(err);
                }
            } catch (InvalidCipherTextException | DataLengthException e) {
                log.error("Exception encountered while decrypting the password. Details: ", e);
                throw new ServerException("Error while decrypting the password");
            }
        }

        smtpDetails.setPassword(commonUtils.encryptInBCEC(plainTxt));

        return UtilityBean.<SMTPDetailsPojo>builder()
                .metadata(Map.of(Constants.USER_ID, userId))
                .build();
    }

    /**
     * Persists validated SMTP configuration details into the database.
     * Constructs SMTPDetailsBean with encrypted password and timestamps.
     * Rolls back transaction if any error occurs during insertion.
     * Throws DataProcessingException on failure to persist data.
     */
    @Override
    @Transactional(rollbackFor = {Exception.class, RuntimeException.class})
    public Object process(UtilityBean<SMTPDetailsPojo> smtpDetailsPojo) throws DataProcessingException {
        Date time = dateTimeUtil.getCurrentTimestampInGMT();
        DateFormat dateFormat = new SimpleDateFormat(Constants.DATE_TIME);
        String createdTime = dateFormat.format(time);
        String userId = (String) smtpDetailsPojo.getMetadata().get(Constants.USER_ID);
        AccountBean accountBean = (AccountBean) smtpDetailsPojo.getMetadata().get(Constants.ACCOUNT);

        SMTPDetailsPojo smtpDetails = smtpDetailsPojo.getPojoObject();

        SMTPDetailsBean smtpDetailsBean = SMTPDetailsBean.builder()
                .accountId(accountBean.getId())
                .address(smtpDetails.getAddress())
                .port(smtpDetails.getPort())
                .lastModifiedBy(userId)
                .username(smtpDetails.getUsername())
                .password(smtpDetails.getPassword())
                .securityId(securityType.getSubTypeId())
                .createdTime(createdTime)
                .updatedTime(createdTime)
                .status(1)
                .fromRecipient(smtpDetails.getFromRecipient())
                .build();

        try {
            notificationsDao.addSMTPDetails(smtpDetailsBean);
        } catch (HealControlCenterException e) {
            log.error("Error occurred while adding SMTP details to account. Details: ", e);
            throw new DataProcessingException(e.getMessage());
        }

        return null;
    }
}
