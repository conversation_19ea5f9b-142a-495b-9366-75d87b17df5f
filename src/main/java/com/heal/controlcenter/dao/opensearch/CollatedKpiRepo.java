package com.heal.controlcenter.dao.opensearch;

import com.appnomic.appsone.opeasearchquery.helper.OpenSearchQueryHelper;
import com.appnomic.appsone.opeasearchquery.queryopts.QueryOptions;
import com.appnomic.appsone.opeasearchquery.results.NameValuePair;
import com.appnomic.appsone.opeasearchquery.results.TabularResults;
import com.heal.configuration.util.DateHelper;
import com.heal.controlcenter.config.OpenSearchConfig;
import com.heal.controlcenter.util.Constants;
import lombok.extern.slf4j.Slf4j;
import org.opensearch.client.opensearch.OpenSearchClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.*;

@Repository
@Slf4j
public class CollatedKpiRepo {

    @Autowired
    private OpenSearchConfig openSearchConfig;

    public Map<String, Set<String>> getGroupKpiAttributesWithDataCollected(String accountIdentifier, Set<String> instanceIdentifiers,
                                                                          Long range, Integer kpiId) {
        String indexPrefix = Constants.Opensearch.INDEX_PREFIX_HEAL_COLLATED_KPI + "_" + accountIdentifier.toLowerCase();
        try {
            OpenSearchClient openSearchClient = openSearchConfig.getOpenSearchClient(accountIdentifier, Constants.Opensearch.INDEX_PREFIX_HEAL_SERVICE_KPI_THRESHOLDS);
            if (openSearchClient == null) {
                log.warn("OpenSearchClient not available for account [{}]. Unable to fetch collated KPI attributes.", accountIdentifier);
                return Collections.emptyMap();
            }

            if (instanceIdentifiers == null || instanceIdentifiers.isEmpty() || kpiId == null) {
                return Collections.emptyMap();
            }

            long toDate = System.currentTimeMillis();
            long fromDate = toDate - range;

            List<String> indexNames = new ArrayList<>();
            DateHelper.getWeeksAsString(fromDate, toDate).forEach(date ->
                    indexNames.add(indexPrefix + "_" + date));

            List<NameValuePair> matchAnyFieldsInstanceId = instanceIdentifiers.stream()
                    .map(instanceId -> new NameValuePair("compInstanceIdentifier", instanceId)).toList();
            List<NameValuePair> matchAnyFieldsKpiId = Collections.singletonList(new NameValuePair("kpiId", String.valueOf(kpiId)));

            List<String> columns = new ArrayList<>();
            columns.add("compInstanceIdentifier");
            columns.add("groupAttribute");

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .epochFieldName("timeInGMT")
                    .epochFromDate(fromDate)
                    .epochToDate(toDate)
                    .isTimeSeriesData(false)
                    .matchAnyOfFields(Optional.of(List.of(matchAnyFieldsInstanceId, matchAnyFieldsKpiId)))
                    .fetchAllRecords(true)
                    .groupByColumns(Optional.of(columns))
                    .build();

            TabularResults results = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, openSearchClient);
            if (results == null || results.getRowResults() == null || results.getRowResults().isEmpty()) {
                return Collections.emptyMap();
            }

            Map<String, Set<String>> resultMap = new HashMap<>();
            for (TabularResults.ResultRow row : results.getRowResults()) {
                List<TabularResults.ResultRow.ResultRowColumn> rowValues = row.getListOfRows();
                String instanceId = null;
                String groupAttribute = null;
                for (TabularResults.ResultRow.ResultRowColumn resultRowColumn : rowValues) {
                    if ("compInstanceIdentifier".equals(resultRowColumn.getColumnName())) {
                        instanceId = resultRowColumn.getColumnValue();
                    } else if ("groupAttribute".equals(resultRowColumn.getColumnName())) {
                        groupAttribute = resultRowColumn.getColumnValue();
                    }
                }
                if (instanceId != null && groupAttribute != null) {
                    resultMap.computeIfAbsent(instanceId, k -> new HashSet<>()).add(groupAttribute);
                }
            }
            return resultMap;
        } catch (Exception e) {
            log.error("Unable to get core kpi group attributes from [{}]. Details: accountIdentifier [{}], instanceIds [{}], kpiIds [{}].",
                    indexPrefix, accountIdentifier, instanceIdentifiers, kpiId, e);
        }
        return Collections.emptyMap();
    }
}


