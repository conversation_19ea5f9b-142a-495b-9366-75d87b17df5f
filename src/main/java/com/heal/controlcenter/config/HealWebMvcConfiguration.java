package com.heal.controlcenter.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 */
@Configuration
public class HealWebMvcConfiguration implements WebMvcConfigurer {
    @Value("${header.access.control.allow.origins:https://localhost:5173}")
    String frontendUrl;
    @Value("${header.access.control.allow.headers:Content-Type,Authorization,X-Requested-With,Content-Length,Accept,Origin}")
    String headers;
    @Value("${header.access.control.allow.methods:GET,POST,DELETE,PUT,OPTIONS}")
    String methods;

    @Override
    public void addCorsMappings(CorsRegistry corsRegistry) {
        corsRegistry.addMapping("/**")
                .allowedOrigins(frontendUrl)
                .allowedHeaders(headers)
                .allowedMethods(methods);
    }
}