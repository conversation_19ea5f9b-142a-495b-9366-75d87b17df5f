package com.heal.controlcenter.swagger;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Schema definitions for error responses used in Swagger documentation.
 * These schemas represent the actual structure returned by the ExceptionHandler.
 */
public class ErrorResponseSchemas {

    @Schema(name = "ErrorResponse", description = "Standard error response format")
    public static class ErrorResponse {
        @Schema(description = "Error message", example = "Invalid account identifier format")
        public String message;
        
        @Schema(description = "Error details containing timestamp, status, and path information")
        public ErrorData data;
        
        @Schema(description = "HTTP status", example = "BAD_REQUEST")
        public String responseStatus;
    }

    @Schema(name = "UnauthorizedResponse", description = "Unauthorized error response")
    public static class UnauthorizedResponse {
        @Schema(description = "Error message", example = "Invalid or expired authentication token")
        public String message;
        
        @Schema(description = "Error details containing timestamp, status, and path information")
        public ErrorData data;
        
        @Schema(description = "HTTP status", example = "UNAUTHORIZED")
        public String responseStatus;
    }

    @Schema(name = "ServerErrorResponse", description = "Server error response")
    public static class ServerErrorResponse {
        @Schema(description = "Error message", example = "Internal server error occurred")
        public String message;
        
        @Schema(description = "Error details containing timestamp, status, and path information")
        public ErrorData data;
        
        @Schema(description = "HTTP status", example = "INTERNAL_SERVER_ERROR")
        public String responseStatus;
    }

    @Schema(name = "ErrorData", description = "Detailed error information")
    public static class ErrorData {
        @Schema(description = "Timestamp when the error occurred", example = "2024-01-15T10:30:00Z")
        public String timestamp;
        
        @Schema(description = "HTTP status code", example = "400")
        public int status;
        
        @Schema(description = "List of error messages", example = "[\"Invalid account identifier format\"]")
        public java.util.List<String> error;
        
        @Schema(description = "Exception type", example = "ClientException")
        public String type;
        
        @Schema(description = "Request path where error occurred", example = "uri=/accounts/invalid-id/applications")
        public String path;
        
        @Schema(description = "Error message", example = "Invalid account identifier format")
        public String message;
    }
}
