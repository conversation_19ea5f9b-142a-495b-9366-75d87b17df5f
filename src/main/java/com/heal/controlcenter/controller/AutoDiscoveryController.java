package com.heal.controlcenter.controller;

import com.appnomic.appsone.common.beans.discovery.Component;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.GetAutoDiscoveryIgnoredEntitiesBL;
import com.heal.controlcenter.businesslogic.GetAutoDiscoveryKnownComponentsBL;
import com.heal.controlcenter.businesslogic.PostAutoDiscoveryMapToServiceBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.AutoDiscoveryIgnoredEntitiesPojo;
import com.heal.controlcenter.pojo.AutoDiscoveryMapEntityPojo;
import com.heal.controlcenter.pojo.ResponsePojo;
import com.heal.controlcenter.swagger.CommonApiResponses;
import com.heal.controlcenter.swagger.ErrorResponseSchemas;
import com.heal.controlcenter.util.JsonFileParser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

@Slf4j
@Configuration
@RestController
public class AutoDiscoveryController {

    @Autowired
    PostAutoDiscoveryMapToServiceBL postAutoDiscoveryMapToServiceBL;
    @Autowired
    GetAutoDiscoveryIgnoredEntitiesBL getAutoDiscoveryIgnoredEntitiesBL;
    @Autowired
    GetAutoDiscoveryKnownComponentsBL getAutoDiscoveryKnownComponentsBL;
    @Autowired
    JsonFileParser headersParser;

    @Operation(
            summary = "Retrieves auto-discovery ignored entities for the specified account.",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Ignored entities fetched successfully.",
                            content = @Content(schema = @Schema(implementation = AutoDiscoveryIgnoredEntitiesPojo.class))
                    )
            }
    )
    @CommonApiResponses
    @RequestMapping(value = "/accounts/{identifier}/ignored-entities", method = RequestMethod.GET)
    public ResponseEntity<ResponsePojo<List<AutoDiscoveryIgnoredEntitiesPojo>>> getIgnoredEntities(
            @Parameter(
                    name = "Authorization",
                    description = "Bearer token for authentication",
                    required = true,
                    example = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            )
            @RequestHeader("Authorization") String authorization,
            @Parameter(
                    name = "identifier",
                    description = "Unique identifier of the account",
                    required = true,
                    example = "account-001"
            )
            @PathVariable("identifier") String accountIdentifier)
            throws ClientException, ServerException, DataProcessingException {
        log.trace("Method Invoked : getIgnoredEntities");
        Object bean = new Object();

        UtilityBean<Object> utilityBean = getAutoDiscoveryIgnoredEntitiesBL.clientValidation(null, authorization, accountIdentifier);
        getAutoDiscoveryIgnoredEntitiesBL.serverValidation(utilityBean);
        List<AutoDiscoveryIgnoredEntitiesPojo> data = getAutoDiscoveryIgnoredEntitiesBL.process(bean);

        ResponsePojo<List<AutoDiscoveryIgnoredEntitiesPojo>> responseBean = new ResponsePojo<>("Ignored entities fetched successfully.", data, HttpStatus.OK);

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responseBean);
    }

    @Operation(
            summary = "Maps auto-discovery entities to services for the specified account.",
            description = "Maps discovered entities to services or unmaps them. Returns different status codes based on the operation result.",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Mapping successful or service(s) unmapped successfully."
                    ),
                    @ApiResponse(
                            responseCode = "207",
                            description = "Multi-status - Some invalid entities found during mapping.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ErrorResponseSchemas.ErrorResponse.class)
                            )
                    )
            }
    )
    @CommonApiResponses
    @RequestMapping(value = "/accounts/{identifier}/map-to-service", method = RequestMethod.POST)
    public ResponseEntity<ResponsePojo<String>> mapEntity(
            @Parameter(
                    name = "Authorization",
                    description = "Bearer token for authentication",
                    required = true,
                    example = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            )
            @RequestHeader("Authorization") String authorization,
            @Parameter(
                    name = "identifier",
                    description = "Unique identifier of the account",
                    required = true,
                    example = "account-001"
            )
            @PathVariable("identifier") String accountIdentifier,
            @Parameter(
                    description = "Entity mapping details for auto-discovery service mapping",
                    required = true
            )
            @Validated @RequestBody AutoDiscoveryMapEntityPojo body)
            throws ClientException, ServerException, DataProcessingException {
        log.trace("Method Invoked : mapEntity");

        UtilityBean<AutoDiscoveryMapEntityPojo> utilityBean = postAutoDiscoveryMapToServiceBL.clientValidation(body, authorization, accountIdentifier);
        AutoDiscoveryMapEntityPojo mapEntityBean = postAutoDiscoveryMapToServiceBL.serverValidation(utilityBean);
        List<String> invalidJsonObjects = postAutoDiscoveryMapToServiceBL.process(mapEntityBean);

        ResponsePojo<String> responseBean = new ResponsePojo<>();
        if (invalidJsonObjects.contains("UNMAPPING")) {
            invalidJsonObjects.remove("UNMAPPING");
            if (invalidJsonObjects.size() == 0) {
                responseBean.setData(null);
                responseBean.setMessage("Service(s) unmapped successfully.");
                responseBean.setResponseStatus(HttpStatus.OK);

            } else if (invalidJsonObjects.size() < Arrays.asList(mapEntityBean.getServiceMappingIdentifiers()).size()) {
                responseBean.setData("Invalid Json Objects: " + invalidJsonObjects);
                responseBean.setMessage("Some invalid entities found.");
                responseBean.setResponseStatus(HttpStatus.MULTI_STATUS);

            } else if (invalidJsonObjects.size() == Arrays.asList(mapEntityBean.getServiceMappingIdentifiers()).size()) {
                responseBean.setData("Invalid Json Objects: " + invalidJsonObjects);
                responseBean.setMessage("Invalid entities found.");
                responseBean.setResponseStatus(HttpStatus.BAD_REQUEST);
            }
        } else if (invalidJsonObjects.contains("WRONG-EDIT")) {
            invalidJsonObjects.remove("WRONG-EDIT");
            responseBean.setData("Invalid Json Objects: " + invalidJsonObjects);
            responseBean.setMessage("Cannot edit component instance mappings here!");
            responseBean.setResponseStatus(HttpStatus.BAD_REQUEST);
        } else if (invalidJsonObjects.contains("PASS")) {
            invalidJsonObjects.remove("PASS");
            if (invalidJsonObjects.size() == 0) {
                responseBean.setData(null);
                responseBean.setMessage("Mapping successful and related connections are discovered.");
                responseBean.setResponseStatus(HttpStatus.OK);
            }
        }
        if (invalidJsonObjects.stream().noneMatch(i -> i.equals("UNMAPPING") || i.equals("WRONG-EDIT"))) {
            if (invalidJsonObjects.size() > 0 && invalidJsonObjects.size() < Arrays.asList(mapEntityBean.getServiceMappingIdentifiers()).size()) {
                responseBean.setData("Invalid Json Objects: " + invalidJsonObjects);
                responseBean.setMessage("Some invalid entities found.");
                responseBean.setResponseStatus(HttpStatus.MULTI_STATUS);
            } else if (invalidJsonObjects.size() == Arrays.asList(mapEntityBean.getServiceMappingIdentifiers()).size()) {
                responseBean.setData("Invalid Json Objects: " + invalidJsonObjects);
                responseBean.setMessage("Invalid entities found.");
                responseBean.setResponseStatus(HttpStatus.BAD_REQUEST);
            }
        }

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responseBean);
    }

    @Operation(
            summary = "Retrieves auto-discovery known component information.",
            description = "Returns information about all known components for auto-discovery. This is a public endpoint that does not require authentication.",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Components info fetched successfully.",
                            content = @Content(schema = @Schema(implementation = Component.class))
                    )
            }
    )
    @CommonApiResponses
    @RequestMapping(value = "/auto-discovery-components", method = RequestMethod.GET)
    public ResponseEntity<ResponsePojo<List<Component>>> getComponentAttributeInfo()
            throws DataProcessingException {
        log.trace("Method Invoked : getComponentAttributeInfo");

        List<Component> data = getAutoDiscoveryKnownComponentsBL.process(new Object());

        ResponsePojo<List<Component>> responsePojo = new ResponsePojo<>("Components info fetched successfully.", data, HttpStatus.OK);

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
    }
}
