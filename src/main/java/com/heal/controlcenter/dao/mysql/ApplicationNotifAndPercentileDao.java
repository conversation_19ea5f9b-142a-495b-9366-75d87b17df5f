package com.heal.controlcenter.dao.mysql;


import com.heal.controlcenter.beans.ApplicationPercentilesBean;
import com.heal.controlcenter.beans.DefaultNotificationPreferences;
import com.heal.controlcenter.exception.HealControlCenterException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;

@Slf4j
@Repository
public class ApplicationNotifAndPercentileDao {

    @Autowired
    JdbcTemplate jdbcTemplate;

    public int[] addDefaultNotificationPreferences(List<DefaultNotificationPreferences> defaultNotificationPreferences) throws HealControlCenterException {
        String query = "INSERT INTO application_notification_mapping (application_id,notification_type_id,signal_type_id,signal_severity_id,account_id,created_time, updated_time, user_details_id) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
        try {
            log.debug("adding default preferences.");
            return jdbcTemplate.batchUpdate(query, new BatchPreparedStatementSetter() {

                public void setValues(PreparedStatement ps, int i) throws SQLException {
                    ps.setInt(1, defaultNotificationPreferences.get(i).getApplicationId());
                    ps.setInt(2, defaultNotificationPreferences.get(i).getNotificationTypeId());
                    ps.setInt(3, defaultNotificationPreferences.get(i).getSignalTypeId());
                    ps.setInt(4, defaultNotificationPreferences.get(i).getSignalSeverityId());
                    ps.setInt(5, defaultNotificationPreferences.get(i).getAccountId());
                    ps.setString(6, defaultNotificationPreferences.get(i).getCreatedTime());
                    ps.setString(7, defaultNotificationPreferences.get(i).getUpdatedTime());
                    ps.setString(8, defaultNotificationPreferences.get(i).getUserDetailsId());

                }

                public int getBatchSize() {
                    return defaultNotificationPreferences.size();
                }
            });
        } catch (Exception ex) {
            log.error("Error in adding default notification preferences. Details: ", ex);
            throw new HealControlCenterException("Error in adding default notification preferences.");
        }
    }

    public int[] addApplicationPercentiles(List<ApplicationPercentilesBean> applicationPercentilesBeans) throws HealControlCenterException {
        String query = "INSERT INTO `application_percentiles` (" +
                "application_id, display_name, created_time, updated_time, " +
                "user_details_id, account_id, mst_kpi_details_id) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?)";

        try {
            log.debug("Adding application percentiles");

            return jdbcTemplate.batchUpdate(query, new BatchPreparedStatementSetter() {

                public void setValues(PreparedStatement ps, int i) throws SQLException {
                    ApplicationPercentilesBean bean = applicationPercentilesBeans.get(i);
                    ps.setInt(1, bean.getApplicationId());
                    ps.setString(2, bean.getDisplayName());
                    ps.setString(3, bean.getCreatedTime());
                    ps.setString(4, bean.getUpdatedTime());
                    ps.setString(5, bean.getUserDetailsId());
                    ps.setInt(6, bean.getAccountId());
                    ps.setInt(7, bean.getPercentileValue());
                }

                public int getBatchSize() {
                    return applicationPercentilesBeans.size();
                }

            });
        } catch (Exception ex) {
            log.error("Error in adding application percentiles. Details: ", ex);
            throw new HealControlCenterException("Error in adding application percentiles");
        }
    }
}
