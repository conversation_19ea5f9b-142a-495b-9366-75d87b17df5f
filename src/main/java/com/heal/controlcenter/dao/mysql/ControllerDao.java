package com.heal.controlcenter.dao.mysql;

import com.heal.controlcenter.beans.CompInstClusterDetailsBean;
import com.heal.controlcenter.beans.ControllerBean;
import com.heal.controlcenter.beans.ViewApplicationServiceMappingBean;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.pojo.ClusterComponentDetails;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Repository
@Slf4j
public class ControllerDao {

    @Autowired
    JdbcTemplate jdbcTemplate;

    public List<ControllerBean> getServicesList(Integer accountId) throws ControlCenterException {
        String query = "select id, name, identifier, account_id accountId, user_details_id lastModifiedBy, created_time createdTime, " +
                "updated_time updatedTime, controller_type_id controllerTypeId, status from controller " +
                "where account_id = ? and controller_type_id = 192";
        try {
            log.debug("getting services list.");
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(ControllerBean.class), accountId);
        } catch (EmptyResultDataAccessException e) {
            log.info("Services not mapped to account [{}]", accountId);
            return Collections.emptyList();
        } catch (Exception e) {
            log.error("Exception encountered while fetching services for accountId [{}]. Details: ", accountId, e);
            throw new ControlCenterException("Error occurred while getting services list");
        }
    }

    public List<ControllerBean> getServicesList(int accountId, String searchTerm, List<Integer> serviceIds, int offset, int limit) throws ControlCenterException {
        String sql = "SELECT c.id, c.name, c.identifier, c.account_id AS accountId, c.user_details_id AS lastModifiedBy, " +
                "c.created_time AS createdTime, c.updated_time AS updatedTime, c.controller_type_id AS controllerTypeId, c.status " +
                "FROM controller c WHERE c.account_id = ? AND c.controller_type_id = 192 AND c.id IN (%s)";

        List<Object> params = new ArrayList<>();
        params.add(accountId);

        if (searchTerm != null && !searchTerm.trim().isEmpty()) {
            sql += " AND (LOWER(c.name) LIKE ? OR LOWER(c.identifier) LIKE ?)";
            String likeTerm = "%" + searchTerm.toLowerCase().trim() + "%";
            params.add(likeTerm);
            params.add(likeTerm);
        }

        String serviceIdParams = String.join(",", Collections.nCopies(serviceIds.size(), "?"));
        sql = String.format(sql, serviceIdParams);
        params.addAll(serviceIds);

        sql += " ORDER BY c.updated_time DESC LIMIT ? OFFSET ?";
        params.add(limit);
        params.add(offset);

        try {
            return jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(ControllerBean.class), params.toArray());
        } catch (Exception e) {
            log.error("Error while fetching services for accountId: {}. Details: ", accountId, e);
            throw new ControlCenterException("Error fetching services list");
        }
    }

    public int getServicesListCount(int accountId, String searchTerm, List<Integer> serviceIds) throws ControlCenterException {
        String sql = "SELECT COUNT(*) FROM controller c WHERE c.account_id = ? AND c.controller_type_id = 192 AND c.id IN (%s)";

        List<Object> params = new ArrayList<>();
        params.add(accountId);

        if (searchTerm != null && !searchTerm.trim().isEmpty()) {
            sql += " AND (LOWER(c.name) LIKE ? OR LOWER(c.identifier) LIKE ?)";
            String likeTerm = "%" + searchTerm.toLowerCase().trim() + "%";
            params.add(likeTerm);
            params.add(likeTerm);
        }

        String serviceIdParams = String.join(",", Collections.nCopies(serviceIds.size(), "?"));
        sql = String.format(sql, serviceIdParams);
        params.addAll(serviceIds);

        try {
            Integer count = jdbcTemplate.queryForObject(sql, Integer.class, params.toArray());
            return count != null ? count : 0;
        } catch (Exception e) {
            log.error("Error while counting services for accountId: {}. Details: ", accountId, e);
            throw new ControlCenterException("Error counting services");
        }
    }

    public List<ControllerBean> getControllerList(int accountId) throws ControlCenterException {
        String query = "select id id, name name, controller_type_id controllerTypeId, identifier identifier, plugin_supr_interval pluginSuppressionInterval, plugin_whitelist_status pluginWhitelisted, " +
                "status status, user_details_id lastModifiedBy, created_time createdTime, updated_time updatedTime, account_id accountId " +
                "from controller where account_id = ? and status = 1";
        try {
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(ControllerBean.class), accountId);
        } catch (Exception ex) {
            log.error("Error in fetching controllers", ex);
            throw new ControlCenterException("Error in fetching controllers");
        }
    }

    public ControllerBean getServiceById(int serviceId, int accountId) {
        String query = "SELECT id, name name, controller_type_id controllerTypeId, identifier identifier, plugin_supr_interval pluginSuppressionInterval, " +
                "plugin_whitelist_status pluginWhitelisted, status status, user_details_id lastModifiedBy, created_time createdTime, updated_time updatedTime, " +
                "account_id accountId FROM controller WHERE account_id = ? AND id = ? AND status = 1 and controller_type_id=192";
        try {
            return jdbcTemplate.queryForObject(query, new BeanPropertyRowMapper<>(ControllerBean.class), accountId, serviceId);
        } catch (Exception ex) {
            log.error("Error occurred while fetching controller. Details: ", ex);
        }
        return null;
    }

    public List<String> getLayers(String servicesLayerType) {
        String query = "SELECT distinct mst.name FROM mst_type mt JOIN mst_sub_type mst on(mt.id = mst_type_id) WHERE mt.type = ?";
        try {
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(String.class), servicesLayerType);
        } catch (Exception e) {
            log.error("Error while fetching layers for the service [{}]. Details: ", servicesLayerType, e);
        }
        return new ArrayList<>();
    }

    public List<ControllerBean> getApplicationsList(int accountId) {
        String query = "select id, name, controller_type_id controllerTypeId, identifier, " +
                "status, user_details_id userDetailsId, created_time createdTime, updated_time updatedTime ,account_id accountId, environment " +
                "from controller where account_id = ? and status = 1 and controller_type_id = 191";
        try {
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(ControllerBean.class), accountId);
        } catch (EmptyResultDataAccessException e) {
            log.info("No applications mapped to accountId [{}]", accountId);
            return Collections.emptyList();
        } catch (Exception ex) {
            log.error("Error in fetching applications for accountId [{}]", accountId, ex);
            return Collections.emptyList();
        }
    }

    public List<ViewApplicationServiceMappingBean> getServicesMappedToApplication(int accountId, String appId) {
        String query = "select application_id applicationId, application_name applicationName, application_identifier applicationIdentifier, " +
                "service_id serviceId, service_name serviceName,service_identifier serviceIdentifier from view_application_service_mapping " +
                "where account_id = ? and application_identifier = ?";
        try {
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(ViewApplicationServiceMappingBean.class), accountId, appId);
        } catch (EmptyResultDataAccessException e) {
            log.info("No services mapped to applicationId [{}] and accountId [{}]", appId, accountId);
            return Collections.emptyList();
        } catch (Exception ex) {
            log.error("No services mapped to applicationId [{}] and accountId [{}]. Details: ", appId, accountId, ex);
            return Collections.emptyList();
        }
    }

    public List<ClusterComponentDetails> getHostClusterComponentDetailsForService(String serviceIdentifier) throws ControlCenterException {
        String query = "select vc.id,vc.name,vc.identifier,vci.mst_component_id componentId," +
                " vci.component_name componentName, vci.mst_component_type_id componentTypeId, vci.component_type_name componentTypeName," +
                " vci.mst_component_version_id componentVersionId, vci.component_version_name componentVersionName, vci.common_version_id commonVersionId, " +
                " vci.common_version_name commonVersionName from" +
                " view_cluster_services vc, view_component_instance vci where vc.id = vci.id and vc.mst_component_type_id = 1" +
                " and vc.service_identifier= ? ";
        try {
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(ClusterComponentDetails.class), serviceIdentifier);
        } catch (EmptyResultDataAccessException e) {
            log.info("No host clusters/instances mapped to serviceIdentifier [{}]", serviceIdentifier);
            return Collections.emptyList();
        } catch (Exception ex) {
            log.error("Error in getting host cluster component details for service. Details: ", ex);
            throw new ControlCenterException("Error in getting host cluster component details for service");
        }
    }

    public List<ClusterComponentDetails> getComponentClusterComponentDetailsForService(String serviceIdentifier) throws ControlCenterException {
        String query = "select vc.id,vc.name,vc.identifier,vci.mst_component_id componentId," +
                " vci.component_name componentName, vci.mst_component_type_id componentTypeId, vci.component_type_name componentTypeName," +
                " vci.mst_component_version_id componentVersionId, vci.component_version_name componentVersionName,vci.common_version_id commonVersionId, " +
                " vci.common_version_name commonVersionName from" +
                " view_cluster_services vc, view_component_instance vci where vc.id = vci.id and vc.mst_component_type_id != 1" +
                " and vc.service_identifier= ?";
        try {
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(ClusterComponentDetails.class), serviceIdentifier);
        } catch (EmptyResultDataAccessException e) {
            log.info("No component clusters/instances mapped to serviceIdentifier [{}]", serviceIdentifier);
            return Collections.emptyList();
        } catch (Exception ex) {
            log.error("Error in getting component cluster component details for service. Details: ", ex);
            throw new ControlCenterException("Error in getting component cluster component details for service");
        }
    }

    public List<CompInstClusterDetailsBean> getCompInstanceDetails(int accountId) {
        String query = "select id instanceId,common_version_id commonVersionId, common_version_name commonVersionName,mst_component_id compId,component_name componentName, " +
                "mst_component_type_id mstComponentTypeId,component_type_name componentTypeName, " +
                "mst_component_version_id compVersionId,component_version_name componentVersionName,name instanceName,host_id hostId,status, " +
                "host_name hostName,is_cluster isCluster,identifier, " +
                "host_address hostAddress, supervisor_id supervisorId from view_component_instance where account_id = ? and status = 1";
        try {
            log.debug("getting component instance details.");
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(CompInstClusterDetailsBean.class), accountId);
        } catch (Exception e) {
            log.error("Error occurred while fetching component instance list from 'view_component_instance' table for accountId [{}]. Details: ", accountId, e);
        }
        return Collections.emptyList();
    }

    public ControllerBean getControllerByIdentifierOrName(String identifier, String name) {
        String query = "SELECT id, name, identifier, account_id as accountId, user_details_id as userDetailsId, " +
                "created_time as createdTime, updated_time as updatedTime, controller_type_id as controllerTypeId, status " +
                "FROM controller WHERE identifier = ? OR name = ?";
        try {
            return jdbcTemplate.queryForObject(
                    query,
                    new BeanPropertyRowMapper<>(ControllerBean.class),
                    identifier,
                    name
            );
        } catch (EmptyResultDataAccessException e) {
            log.info("No controller found with identifier [{}] or name [{}]", identifier, name);
            return null;
        } catch (Exception e) {
            log.error("Error while fetching controller by identifier [{}] or name [{}]. Details: ", identifier, name, e);
            return null;
        }
    }

    public Integer getControllerApplicationId(int controllerId, int tagId, int accountId) {
        String query = "SELECT tm.object_id " +
                "FROM tag_mapping tm " +
                "JOIN controller c ON tm.tag_key = c.id AND tm.account_id = c.account_id " +
                "WHERE tm.account_id = ? AND c.id = ? AND tm.tag_id = ? AND tm.object_ref_table = 'controller'";
        try {
            return jdbcTemplate.queryForObject(query, Integer.class, accountId, controllerId, tagId);
        } catch (EmptyResultDataAccessException e) {
            log.info("No application mapping found for controllerId [{}], tagId [{}], accountId [{}]", controllerId, tagId, accountId);
            return null;
        } catch (Exception e) {
            log.error("Error while fetching applicationId for controllerId [{}], tagId [{}], accountId [{}]. Details: ", controllerId, tagId, accountId, e);
            return null;
        }
    }

}
