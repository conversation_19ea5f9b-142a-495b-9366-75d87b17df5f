package com.heal.controlcenter.businesslogic;

import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.UserDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.util.ClientValidationUtils;
import com.heal.controlcenter.util.CommonUtils;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.ServerValidationUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.powermock.core.classloader.annotations.PrepareForTest;



import static org.junit.jupiter.api.Assertions.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;


@ExtendWith(MockitoExtension.class)
@PrepareForTest({UserRoleBL.class})
public class UserRoleBLTest {

    @Mock
    CommonUtils commonUtils;

    @Mock
    ClientValidationUtils clientValidationUtils;

    @Mock
    ServerValidationUtils serverValidationUtils;

    @InjectMocks
    UserRoleBL userRoleBL;

    @Mock
    UserDao rolesAndProfilesDao;


    @BeforeEach
    void setUp() {
    }

    @AfterEach
    void tearDown() {
    }

    @Test
    void clientValidation_InvalidToken() throws ControlCenterException, ClientException {
        String expectedMessage = "ClientException : Invalid Authorization token";
        String authKey = "tyr8-uijjf8sooijfjfkjkjskjjkje";

        // Mock the clientValidationUtils.authKeyValidation to throw exception
        doThrow(new ClientException("Invalid Authorization token"))
            .when(clientValidationUtils).authKeyValidation(authKey);

        // Execute and verify
        ClientException exception = assertThrows(ClientException.class, () ->
                userRoleBL.clientValidation(null, authKey));
        assertEquals(expectedMessage, exception.getMessage());
    }

    @Test
    void clientValidation() throws ControlCenterException, ClientException {
        String authKey = "tyr8-uijjf8sooijfjfkjkjskjjkje";

        // Mock successful validation - no exception thrown
        // clientValidationUtils.authKeyValidation() returns void, so no need to mock return value

        UtilityBean<String> result = userRoleBL.clientValidation(null, authKey);

        // Verify the result structure
        assertNotNull(result);
        assertNotNull(result.getRequestParams());
        assertEquals(authKey, result.getRequestParams().get(Constants.AUTH_KEY));
        // pojoObject is not set in clientValidation, so it should be null
        assertNull(result.getPojoObject());
    }


    @Test
    void clientValidation_WhenAuthKeyValidationFailed() throws ControlCenterException, ClientException {
        String authKey = "invalid-auth-key";
        String message = "Invalid Authorization token";

        // Mock clientValidationUtils.authKeyValidation to throw ClientException
        doThrow(new ClientException(message))
            .when(clientValidationUtils).authKeyValidation(authKey);

        // Execute and verify
        ClientException exception = assertThrows(ClientException.class, () ->
            userRoleBL.clientValidation(null, authKey));
        assertEquals("ClientException : " + message, exception.getMessage());
    }

    @Test
    void process_when_user_profile_invalid() {
        Throwable processing = assertThrows(DataProcessingException.class, () -> userRoleBL.process("bean"));
        assertTrue(processing.getMessage().contains("DataProcessingException : User roles information unavailable"));
    }
}
