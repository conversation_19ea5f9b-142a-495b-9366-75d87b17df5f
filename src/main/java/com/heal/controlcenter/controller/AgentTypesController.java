package com.heal.controlcenter.controller;

import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.GetAgentTypesBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.AgentTypePojo;
import com.heal.controlcenter.pojo.ResponsePojo;
import com.heal.controlcenter.swagger.CommonApiResponses;
import com.heal.controlcenter.util.JsonFileParser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@Configuration
@RestController
public class AgentTypesController {

    @Autowired
    GetAgentTypesBL getAgentTypesBL;
    @Autowired
    JsonFileParser headersParser;

    @Operation(
            summary = "Retrieves agent types for the specified account and service.",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Agent types fetched successfully.",
                            content = @Content(schema = @Schema(implementation = AgentTypePojo.class))
                    )
            }
    )
    @CommonApiResponses
    @RequestMapping(value = "/accounts/{identifier}/services/{serviceId}/agent-types", method = RequestMethod.GET)
    public ResponseEntity<ResponsePojo<List<AgentTypePojo>>> getAgentType(
            @Parameter(
                    name = "Authorization",
                    description = "Bearer token for authentication",
                    required = true,
                    example = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            )
            @RequestHeader("Authorization") String authorization,
            @Parameter(
                    name = "identifier",
                    description = "Unique identifier of the account",
                    required = true,
                    example = "account-001"
            )
            @PathVariable("identifier") String accountIdentifier,
            @Parameter(
                    name = "serviceId",
                    description = "Unique identifier of the service",
                    required = true,
                    example = "service-123"
            )
            @PathVariable("serviceId") String serviceId)
            throws ClientException, ServerException, DataProcessingException {
        log.trace("Method Invoked : getAgentType");

        UtilityBean<Integer> agentTypeUtilityBean = getAgentTypesBL.clientValidation(null, authorization, accountIdentifier, serviceId);
        UtilityBean<Integer> bean = getAgentTypesBL.serverValidation(agentTypeUtilityBean);
        List<AgentTypePojo> data = getAgentTypesBL.process(bean);

        ResponsePojo<List<AgentTypePojo>> responsePojo = new ResponsePojo<>("Agent type fetched successfully.", data, HttpStatus.OK);

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
    }
}
