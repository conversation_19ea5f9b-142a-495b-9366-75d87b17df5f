package com.heal.controlcenter.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.media.Schema;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class OpenApiConfig {

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("Heal Control Center API")
                        .version("1.0")
                        .description("Heal Control Center REST API Documentation"))
                .components(new Components()
                        .addSchemas("ErrorResponse", createErrorResponseSchema())
                        .addSchemas("UnauthorizedResponse", createUnauthorizedResponseSchema())
                        .addSchemas("ServerErrorResponse", createServerErrorResponseSchema()));
    }

    private Schema<?> createErrorResponseSchema() {
        return new Schema<>()
                .type("object")
                .addProperty("message", new Schema<>().type("string").example("Invalid account identifier format"))
                .addProperty("data", new Schema<>().nullable(true).example(null))
                .addProperty("status", new Schema<>().type("string").example("BAD_REQUEST"));
    }

    private Schema<?> createUnauthorizedResponseSchema() {
        return new Schema<>()
                .type("object")
                .addProperty("message", new Schema<>().type("string").example("Invalid or expired authentication token"))
                .addProperty("data", new Schema<>().nullable(true).example(null))
                .addProperty("status", new Schema<>().type("string").example("UNAUTHORIZED"));
    }

    private Schema<?> createServerErrorResponseSchema() {
        return new Schema<>()
                .type("object")
                .addProperty("message", new Schema<>().type("string").example("Internal server error occurred"))
                .addProperty("data", new Schema<>().nullable(true).example(null))
                .addProperty("status", new Schema<>().type("string").example("INTERNAL_SERVER_ERROR"));
    }
}