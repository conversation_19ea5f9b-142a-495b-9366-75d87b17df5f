package com.heal.controlcenter.businesslogic;

import com.heal.configuration.pojos.AnomalyConfiguration;
import com.heal.configuration.pojos.EscalationSettings;
import com.heal.controlcenter.beans.AccountAnomalyConfigurationBean;
import com.heal.controlcenter.beans.AccountBean;
import com.heal.controlcenter.beans.TimezoneBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.dao.mysql.MasterDataDao;
import com.heal.controlcenter.dao.mysql.NotificationsDao;
import com.heal.controlcenter.dao.mysql.TagsDao;
import com.heal.controlcenter.dao.redis.AccountRepo;
import com.heal.controlcenter.dao.redis.NotificationRepo;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.Account;
import com.heal.controlcenter.pojo.TagMappingDetails;
import com.heal.controlcenter.pojo.Tags;
import com.heal.controlcenter.pojo.ThresholdSeverity;
import com.heal.controlcenter.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.security.KeyPair;
import java.util.*;

@Slf4j
@Service
public class AddAccountBL implements BusinessLogic<Account, UtilityBean<Account>, Account> {

    @Autowired
    AccountsDao accountsDao;
    @Autowired
    MasterDataDao masterDataDao;
    @Autowired
    DateTimeUtil dateTimeUtil;
    @Autowired
    TagsDao tagsDao;
    @Autowired
    AccountRepo accountRepo;
    @Autowired
    NotificationRepo notificationRepo;
    @Autowired
    GetNotificationSettingsBL getNotificationSettingsBL;
    @Autowired
    ClientValidationUtils clientValidationUtils;
    @Autowired
    ServerValidationUtils serverValidationUtils;
    @Autowired
    NotificationsDao notificationsDao;

    /**
     * Performs client-side validation for the incoming account creation request.
     *
     * <p>This method validates:
     * <ul>
     *     <li>The authorization key format and presence.</li>
     *     <li>The uniqueness and validity of the account identifier.</li>
     *     <li>The request payload structure and required fields via {@code validate()}.</li>
     * </ul>
     *
     * <p>If any validation fails, a {@link ClientException} is thrown.
     *
     * @param requestBody   The incoming {@link Account} object from the request payload.
     * @param requestParams Varargs containing the authorization token and account identifier.
     * @return A {@link UtilityBean} containing the original account data and request metadata.
     * @throws ClientException If any validation fails due to incorrect input or missing data.
     */
    @Override
    public UtilityBean<Account> clientValidation(Account requestBody, String... requestParams) throws ClientException {
        String authKey = requestParams[0];
        clientValidationUtils.authKeyValidation(authKey);

        String accountIdentifier = requestParams[1];
        clientValidationUtils.accountIdentifierValidation(accountIdentifier);

        Map<String, String> error = requestBody.validate();
        if (!error.isEmpty()) {
            String err = error.toString();
            log.error(err);
            throw new ClientException(err);
        }

        HashMap<String, String> requestParamsMap = CommonUtils.buildRequestParams(authKey, accountIdentifier);

        return UtilityBean.<Account>builder()
                .requestParams(requestParamsMap)
                .pojoObject(requestBody)
                .build();
    }

    /**
     * Performs server-side validation for the account creation request.
     *
     * <p>This method:
     * <ul>
     *     <li>Validates the authorization key and extracts the user ID.</li>
     *     <li>Checks if the account identifier already exists in the system.</li>
     * </ul>
     *
     * <p>If validation passes, the method enriches the {@link UtilityBean} with metadata like the user ID.
     * Otherwise, it throws a {@link ServerException}.
     *
     * @param utilityBean A {@link UtilityBean} containing request parameters and the account object.
     * @return An enriched {@link UtilityBean} with extracted metadata (e.g., userId).
     * @throws ServerException If the account identifier is already in use or user validation fails.
     */
    @Override
    public UtilityBean<Account> serverValidation(UtilityBean<Account> utilityBean) throws ServerException {
        String authKey = utilityBean.getRequestParams().get(Constants.AUTH_KEY);

        String userId = serverValidationUtils.authKeyValidation(authKey);

        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (accountsDao.existsByIdentifier(accountIdentifier.trim())) {
            String msg = "Account identifier already in use, kindly use another identifier.";
            log.error("{} -> {}", msg, accountIdentifier);
            throw new ServerException(msg);
        }

        return UtilityBean.<Account>builder()
                .pojoObject(utilityBean.getPojoObject())
                .requestParams(utilityBean.getRequestParams())
                .metadata(Map.of(Constants.USER_ID_KEY, userId))
                .build();
    }

    /**
     * Executes the core business logic for creating a new account in the Control Center system.
     *
     * <p>This method performs the following operations in sequence:
     * <ul>
     *     <li>Validates that no other account with the same name exists.</li>
     *     <li>Generates cryptographic key pairs (private and public keys) for the account.</li>
     *     <li>Constructs and persists the account to the database with audit metadata.</li>
     *     <li>Inserts associated anomaly configuration values (e.g., severity levels, thresholds).</li>
     *     <li>Fetches and validates the timezone tag via {@code TagsDao} and {@code MasterDataDao}.</li>
     *     <li>Persists the timezone tag mapping in the tag_mapping table.</li>
     *     <li>Builds a {@code com.heal.configuration.pojos.Account} object and stores it in Redis.</li>
     *     <li>Updates the in-memory account list within {@code AccountRepo} with the newly created account.</li>
     *     <li>Inserts default notification settings into the database for the new account.</li>
     *     <li>Fetches escalation settings from the database and saves them to Redis via {@code NotificationRepo}.</li>
     * </ul>
     *
     * <p>If any failure occurs during these operations—whether due to validation, database, or Redis interaction—
     * a {@link DataProcessingException} or {@link ControlCenterException} is thrown as appropriate.
     *
     * @param utilityBean A validated {@link UtilityBean} containing metadata, request parameters, and the account object.
     * @return A new, empty {@link Account} object as a placeholder (the actual created account is stored in Redis and DB).
     * @throws DataProcessingException If any unrecoverable error occurs during processing.
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public Account process(UtilityBean<Account> utilityBean) throws DataProcessingException {
        String userId = (String) utilityBean.getMetadata().get(Constants.USER_ID_KEY);
        Account accountDetails = utilityBean.getPojoObject();
        try {
            int id = accountsDao.getAccountByName(accountDetails.getAccountName());
            if (id > 0) {
                String msg = String.format("Account name [%s] already exists.", utilityBean.getPojoObject().getAccountName());
                log.error(msg);
                throw new ControlCenterException(msg);
            }

            KeyPair keyPair = KeyGenerator.generateKeys();
            String privateKey = KeyGenerator.getPrivateKey(null, keyPair);
            String publicKey = KeyGenerator.getPublicKey(null, keyPair);

            String createdTime = dateTimeUtil.getCurrentTimestampInGMT().toString();
            if (createdTime == null) {
                log.error("Error while fetching currentTimestamp in GMT");
                throw new ControlCenterException("Error while fetching currentTime in GMT");
            }

            //Insert account
            AccountBean accountBean = new AccountBean();
            accountBean.setName(accountDetails.getAccountName());
            accountBean.setLastModifiedBy(userId);
            accountBean.setIdentifier(accountDetails.getIdentifier());
            accountBean.setPublicKey(publicKey);
            accountBean.setPrivateKey(privateKey);
            accountBean.setCreatedTime(createdTime);
            accountBean.setUpdatedTime(createdTime);
            accountBean.setStatus(utilityBean.getPojoObject().getStatus());

            id = accountsDao.addAccount(accountBean);

            AccountAnomalyConfigurationBean accountAnomalyConfigurationBean = new AccountAnomalyConfigurationBean();
            accountAnomalyConfigurationBean.setAccountId(id);
            accountAnomalyConfigurationBean.setUserDetailsId(userId);
            accountAnomalyConfigurationBean.setCreatedTime(createdTime);
            accountAnomalyConfigurationBean.setUpdatedTime(createdTime);
            accountAnomalyConfigurationBean.setLowEnable(utilityBean.getPojoObject().getThresholdSeverity().isLow());
            accountAnomalyConfigurationBean.setMediumEnable(utilityBean.getPojoObject().getThresholdSeverity().isWarning());
            accountAnomalyConfigurationBean.setHighEnable(utilityBean.getPojoObject().getThresholdSeverity().isCritical());
            accountAnomalyConfigurationBean.setClosingWindow(utilityBean.getPojoObject().getClosingWindow());
            accountAnomalyConfigurationBean.setMaxDataBreaks(utilityBean.getPojoObject().getMaxDataBreaks());

            accountsDao.insertAccountAnomalyConfigurations(id, accountAnomalyConfigurationBean, userId);

            TimezoneBean timezone;
            List<Tags> tagList = accountDetails.getTags();

            String timeZoneId = tagList.get(0).getIdentifier();
            try {
                timezone = masterDataDao.getTimeZoneWithId(timeZoneId);
            } catch (ControlCenterException e) {
                log.error("Invalid timezone [{}]", timeZoneId);
                throw new ControlCenterException(e.getMessage());
            }

            //Insert tag_mapping
            try {
                TagMappingDetails timeZoneTag = TagMappingDetails.builder()
                        .tagId(tagsDao.getTagDetailsByName(Constants.TIME_ZONE_TAG).getId())
                        .tagKey(String.valueOf(timezone.getId()))
                        .tagValue(String.valueOf(timezone.getOffset()))
                        .objectId(id)
                        .objectRefTable(Constants.ACCOUNT)
                        .accountId(id)
                        .userDetailsId(userId)
                        .createdTime(createdTime)
                        .updatedTime(createdTime)
                        .build();
                int mappingId = tagsDao.addTagMappingDetails(timeZoneTag);
                if (mappingId <= 0) {
                    log.error("Timezone mapping to application [{}] failed", id);
                    throw new DataProcessingException("Timezone mapping to application failed");
                }
            } catch (ControlCenterException e) {
                throw new DataProcessingException(e.getMessage());
            }

            Optional<Tags> timeZoneTag = utilityBean.getPojoObject().getTags().stream().filter(tag -> tag.getName().equalsIgnoreCase(Constants.TIME_ZONE_TAG))
                    .findAny();

            com.heal.configuration.pojos.Tags tags = new com.heal.configuration.pojos.Tags();
            String tagName = tagList.get(0).getName();//Ex:Timezone
            tags.setKey(timezone.getTimeZoneId());
            tags.setValue(String.valueOf(timezone.getOffset()));
            tags.setType(tagName);

            //Account Repo saving to Redis
            ThresholdSeverity sourceSeverity = utilityBean.getPojoObject().getThresholdSeverity();
            AnomalyConfiguration redisSeverity = new AnomalyConfiguration();
            redisSeverity.setLowEnable(sourceSeverity.isLow());
            redisSeverity.setMediumEnable(sourceSeverity.isWarning());
            redisSeverity.setHighEnable(sourceSeverity.isCritical());
            redisSeverity.setClosingWindow(utilityBean.getPojoObject().getClosingWindow());
            redisSeverity.setMaxDataBreaks(utilityBean.getPojoObject().getMaxDataBreaks());
            redisSeverity.setLastModifiedBy(userId);
            redisSeverity.setCreatedTime(createdTime);
            redisSeverity.setUpdatedTime(createdTime);
            redisSeverity.setAccountId(id);

            com.heal.configuration.pojos.Account newAccount = com.heal.configuration.pojos.Account.builder()
                    .accountId(id)
                    .id(id)
                    .name(accountBean.getName())
                    .createdTime(String.valueOf(accountBean.getCreatedTime()))
                    .updatedTime(String.valueOf(accountBean.getUpdatedTime()))
                    .status(accountBean.getStatus())
                    .privateKey(accountBean.getPrivateKey())
                    .publicKey(accountBean.getPublicKey())
                    .identifier(accountBean.getIdentifier())
                    .tags(List.of(tags))
                    .timezone(timeZoneTag.map(Tags::getIdentifier).orElse(null))
                    .lastModifiedBy(userId)
                    .anomalyConfiguration(redisSeverity)
                    .build();

            accountRepo.updateAccount(utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER), newAccount);

            List<com.heal.configuration.pojos.Account> existingAccountList = accountRepo.getAccounts();
            if (existingAccountList.isEmpty()) {
                existingAccountList = new ArrayList<>();
            }
            existingAccountList.add(newAccount);
            accountRepo.updateAccounts(existingAccountList);

            //Insert notification_settings
            getNotificationSettingsBL.addDefaultNotificationSettings(id, userId);

            //Notification Repo saving to Redis
            List<EscalationSettings> escalationSetting = notificationsDao.getEscalationSettingsForAccount(id);
            notificationRepo.updateNotificationSettingsInRedis(utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER), escalationSetting);

            return new Account();

        } catch (Exception e) {
            log.error("Error while creating account: {}", accountDetails.getAccountName(), e);
            throw new DataProcessingException(e.getMessage());
        }
    }
}
