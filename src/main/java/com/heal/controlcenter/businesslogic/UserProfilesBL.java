package com.heal.controlcenter.businesslogic;

import com.heal.controlcenter.beans.UserProfileBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.UserDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.util.ClientValidationUtils;
import com.heal.controlcenter.util.CommonUtils;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.ServerValidationUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Slf4j
@Service
public class UserProfilesBL implements BusinessLogic<String, String, List<UserProfileBean>>{

    @Autowired
    CommonUtils commonUtils;
    @Autowired
    UserDao userRolesAndProfilesDao;
    @Autowired
    ClientValidationUtils clientValidationUtils;
    @Autowired
    ServerValidationUtils serverValidationUtils;


    @Override
    public UtilityBean<String> clientValidation(String requestBody, String... requestParams) throws ClientException {
        String authKey = requestParams[0];
        clientValidationUtils.authKeyValidation(authKey);

        HashMap<String, String> requestParamsMap = new HashMap<>();
        requestParamsMap.put(Constants.AUTH_KEY, authKey);

        return UtilityBean.<String>builder()
                .requestParams(requestParamsMap)
                .build();
    }

    @Override
    public String serverValidation(UtilityBean<String> utilityBean) throws ServerException {
        String authKey = utilityBean.getRequestParams().get(Constants.AUTH_KEY);
        serverValidationUtils.authKeyValidation(authKey);
        return null;
    }

    @Override
    public List<UserProfileBean> process(String bean) throws DataProcessingException {
        try {
            List<UserProfileBean> listofUserProfiles = userRolesAndProfilesDao.getUserProfiles();
            for (UserProfileBean userProfile: listofUserProfiles) {
                Set<String> setOfUserProfileMapping = new HashSet<>(userRolesAndProfilesDao.getAccessProfileMapping(userProfile.getUserProfileId()));
                userProfile.setAccessibleFeatures(setOfUserProfileMapping);
            }
            return listofUserProfiles;
        } catch (ControlCenterException e) {
            throw new DataProcessingException(e.getMessage());
        }
    }
}
