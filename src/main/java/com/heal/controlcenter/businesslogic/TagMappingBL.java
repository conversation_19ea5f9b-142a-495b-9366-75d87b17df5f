package com.heal.controlcenter.businesslogic;


import com.heal.controlcenter.beans.TagMappingBean;
import com.heal.controlcenter.dao.mysql.AccountServiceDao;
import groovy.util.logging.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.heal.controlcenter.util.DateTimeUtil;

@Slf4j
@Service
public class TagMappingBL {

    @Autowired
    private AccountServiceDao accountServiceDao;

    private static final Logger log = LoggerFactory.getLogger(TagMappingBL.class);

    public int addTagMapping(int tagId, int objId, String objectRefTable, String tagKey,
                             String tagValue, String userId, int accountId) {

        Integer tagMappingId = accountServiceDao.getTagMappingId(
                tagId, objId, objectRefTable, tagKey, tagValue, accountId);

        if (tagMappingId != null && tagMappingId != 0) {
            log.error("Data is already available in tag_mapping table for tagId- {} ,objId - {} ,objectName - {} ,tagKey:- {},tagValue- {} ,accountId :-{}",
                    tagId, objId, objectRefTable, tagKey, tagValue, accountId);
            return -1;
        }

        TagMappingBean bean = new TagMappingBean();
        bean.setTagId(tagId);
        bean.setObjectId(objId);
        bean.setObjectRefTable(objectRefTable);
        bean.setTagKey(tagKey);
        bean.setTagValue(tagValue);
        bean.setUserDetailsId(userId);
        bean.setAccountId(accountId);

        String date = DateTimeUtil.getTimeInGMT(System.currentTimeMillis());
        bean.setCreatedTime(date);
        bean.setUpdatedTime(date);

        int insertedId = accountServiceDao.addTagMappingDetails(bean);
        if (insertedId != -1) {
            log.info("Tag mapping data is added successfully tagId: {} accountId: {}", tagId, accountId);
        } else {
            log.error("Failed to add the Tag mapping data tagId: {} accountId: {}", tagId, accountId);
        }

        return insertedId;
    }
}
