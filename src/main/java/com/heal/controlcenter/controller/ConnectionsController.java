package com.heal.controlcenter.controller;

import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.GetConnectionsBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.ErrorResponsePojo;
import com.heal.controlcenter.pojo.GetConnectionPojo;
import com.heal.controlcenter.pojo.ResponsePojo;
import com.heal.controlcenter.util.JsonFileParser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@Configuration
@RestController
public class ConnectionsController {

    @Autowired
    GetConnectionsBL getConnectionsBL;
    @Autowired
    JsonFileParser headersParser;

    @Operation(
            summary = "Retrieves connections for the specified account.",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Connections fetched successfully.",
                            content = @Content(schema = @Schema(implementation = GetConnectionPojo.class))
                    ),
                    @ApiResponse(
                            responseCode = "500",
                            description = "Exception encountered while fetching connections.",
                            content = @Content(schema = @Schema(implementation = ErrorResponsePojo.class))
                    ),
                    @ApiResponse(
                            responseCode = "400",
                            description = "Error occurred while fetching connections.",
                            content = @Content(schema = @Schema(implementation = ErrorResponsePojo.class))
                    ),
                    @ApiResponse(
                            responseCode = "401",
                            description = "Unauthorized - Invalid or missing authentication token.",
                            content = @Content(schema = @Schema(implementation = ErrorResponsePojo.class))
                    )
            }
    )
    @RequestMapping(value = "/accounts/{identifier}/connections", method = RequestMethod.GET)
    public ResponseEntity<ResponsePojo<List<GetConnectionPojo>>> getConnections(
            @Parameter(
                    name = "Authorization",
                    description = "Bearer token for authentication",
                    required = true,
                    example = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            )
            @RequestHeader("Authorization") String authorization,
            @Parameter(
                    name = "identifier",
                    description = "Unique identifier of the account",
                    required = true,
                    example = "account-001"
            )
            @PathVariable("identifier") String accountIdentifier)
            throws ClientException, ServerException, DataProcessingException {
        log.trace("Method Invoked : getConnections");

        UtilityBean<Object> utilityBean = getConnectionsBL.clientValidation(null, authorization, accountIdentifier);
        Integer accountId = getConnectionsBL.serverValidation(utilityBean);
        List<GetConnectionPojo> data = getConnectionsBL.process(accountId);

        ResponsePojo<List<GetConnectionPojo>> responsePojo = new ResponsePojo<>("Connections fetched successfully.", data, HttpStatus.OK);

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
    }
}
