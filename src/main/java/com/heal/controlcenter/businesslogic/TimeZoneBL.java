package com.heal.controlcenter.businesslogic;

import com.heal.controlcenter.beans.TimezoneBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.TimeZoneDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.util.ClientValidationUtils;
import com.heal.controlcenter.util.CommonUtils;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.ServerValidationUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;

@Service
@Slf4j
public class TimeZoneBL implements BusinessLogic<String, String, List<TimezoneBean>> {
    @Autowired
    TimeZoneDao timeZoneDao;
    @Autowired
    CommonUtils commonUtils;
    @Autowired
    ClientValidationUtils clientValidationUtils;
    @Autowired
    ServerValidationUtils serverValidationUtils;

    @Override
    public UtilityBean<String> clientValidation(String requestBody, String... requestParams) throws ClientException {
        String authKey = requestParams[0];
        clientValidationUtils.authKeyValidation(authKey);

        HashMap<String, String> requestParamsMap = CommonUtils.buildRequestParams(authKey,null);

        return UtilityBean.<String>builder()
                .requestParams(requestParamsMap)
                .build();
    }

    @Override
    public String serverValidation(UtilityBean<String> utilityBean) throws ServerException {
        String authKey = utilityBean.getRequestParams().get(Constants.AUTH_KEY);
        serverValidationUtils.authKeyValidation(authKey);
        return null;
    }

    @Override
    public List<TimezoneBean> process(String bean) throws DataProcessingException {
        try {
            return timeZoneDao.getTimeZones();
        } catch (ControlCenterException e) {
            throw new DataProcessingException(e.getMessage());
        }
    }
}
