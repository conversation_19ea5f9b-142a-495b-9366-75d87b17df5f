package com.heal.controlcenter.controller;

import com.heal.controlcenter.beans.ServiceBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.GetServiceDetailsBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.ResponsePojo;
import com.heal.controlcenter.util.JsonFileParser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@Configuration
@RestController
public class ServiceController {

    @Autowired
    private GetServiceDetailsBL getServiceDetailsBL;
    @Autowired
    private JsonFileParser headersParser;

    @Operation(summary = "Retrieves all services for a given account.",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Services fetched successfully.",
                            content = @Content(schema = @Schema(implementation = ServiceBean.class))
                    ),
                    @ApiResponse(
                            responseCode = "500",
                            description = "Exception encountered while fetching services.",
                            content = @Content(schema = @Schema(implementation = com.heal.controlcenter.pojo.ErrorResponsePojo.class))
                    ),
                    @ApiResponse(
                            responseCode = "400",
                            description = "Error occurred while fetching services.",
                            content = @Content(schema = @Schema(implementation = com.heal.controlcenter.pojo.ErrorResponsePojo.class))
                    ),
                    @ApiResponse(
                            responseCode = "401",
                            description = "Unauthorized - Invalid or missing authentication token.",
                            content = @Content(schema = @Schema(implementation = com.heal.controlcenter.pojo.ErrorResponsePojo.class))
                    )
            }
    )
    @GetMapping("/accounts/{identifier}/services")
    public ResponseEntity<ResponsePojo<Page<ServiceBean>>> getServices(
            @Parameter(description = "Account identifier", required = true)
            @PathVariable("identifier") String accountIdentifier,
            @Parameter(description = "Bearer token for authentication", required = true)
            @RequestHeader("Authorization") String authorization,
            Pageable pageable)
            throws ClientException, ServerException, DataProcessingException {

        UtilityBean<String> utilityBean = getServiceDetailsBL.clientValidation(null, authorization, accountIdentifier);
        Page<ServiceBean> services = getServiceDetailsBL.process(utilityBean);
        ResponsePojo<Page<ServiceBean>> response = new ResponsePojo<>("Services fetched successfully.", services, HttpStatus.OK);
        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(response);
    }
}
