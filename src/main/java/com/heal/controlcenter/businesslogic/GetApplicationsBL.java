package com.heal.controlcenter.businesslogic;

import com.heal.configuration.pojos.Account;
import com.heal.controlcenter.beans.ControllerBean;
import com.heal.controlcenter.beans.TimezoneBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.beans.ViewApplicationServiceMappingBean;
import com.heal.controlcenter.dao.mysql.ControllerDao;
import com.heal.controlcenter.dao.mysql.TimeZoneDao;
import com.heal.controlcenter.dao.mysql.UserDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.ApplicationPojo;
import com.heal.controlcenter.pojo.ClusterComponentDetails;
import com.heal.controlcenter.pojo.ServiceClusterDetails;
import com.heal.controlcenter.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class GetApplicationsBL implements BusinessLogic<String, UtilityBean<Account>, Page<ApplicationPojo>> {

    @Autowired
    UserValidationUtil userValidationUtil;
    @Autowired
    ControllerDao controllerDao;
    @Autowired
    DateTimeUtil dateTimeUtil;
    @Autowired
    UserDao userDao;
    @Autowired
    TimeZoneDao timeZoneDao;
    @Autowired
    ClientValidationUtils clientValidationUtils;
    @Autowired
    ServerValidationUtils serverValidationUtils;

    private boolean clusterDataRequired;

    /**
     * Performs client-side validation on request parameters such as authentication token,
     * account identifier, cluster data requirement flag, and search term.
     *
     * @param requestBody   the request body (not used here)
     * @param requestParams request parameters: authKey, accountIdentifier, clusterDataRequired, searchTerm
     * @return UtilityBean containing validated parameters and metadata
     * @throws ClientException if validation fails
     */
    @Override
    public UtilityBean<String> clientValidation(String requestBody, String... requestParams) throws ClientException {
        long start = System.currentTimeMillis();
        try {
            String authKey = requestParams[0];
            clientValidationUtils.authKeyValidation(authKey);

            String accountIdentifier = requestParams[1];
            clientValidationUtils.accountIdentifierValidation(accountIdentifier);

            clusterDataRequired = Boolean.parseBoolean(requestParams[2]);
            String searchTerm = requestParams.length > 3 ? requestParams[3] : "";
            if (searchTerm != null) {
                searchTerm = searchTerm.trim();
                if (searchTerm.isEmpty()) {
                    log.warn("Search term is empty after trimming. Defaulting to empty string.");
                    searchTerm = "";
                }
            } else {
                log.warn("Search term is null in requestParams. Defaulting to empty string.");
                searchTerm = "";
            }

            log.info("clientValidation - authKey: {}, accountIdentifier: {}, clusterDataRequired: {}, searchTerm: '{}'",
                    authKey, accountIdentifier, clusterDataRequired, searchTerm);

            HashMap<String, String> requestParamsMap = CommonUtils.buildRequestParams(authKey, accountIdentifier);
            requestParamsMap.put(Constants.SEARCH_TERM_KEY, searchTerm);

            return UtilityBean.<String>builder()
                    .requestParams(requestParamsMap)
                    .build();
        } finally {
            long end = System.currentTimeMillis();
            log.info("clientValidation completed in {} ms", (end - start));
        }
    }

    /**
     * Performs server-side validation using the client-validated data.
     * It validates the authentication key, fetches the user ID, and verifies the account.
     *
     * @param utilityBean the validated parameters from the client
     * @return UtilityBean containing account info and metadata
     * @throws ServerException if validation fails
     */
    @Override
    public UtilityBean<Account> serverValidation(UtilityBean<String> utilityBean) throws ServerException {
        long start = System.currentTimeMillis();
        try {
            String authKey = utilityBean.getRequestParams().get(Constants.AUTH_KEY);
            String userId = serverValidationUtils.authKeyValidation(authKey);

            String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
            Account account = serverValidationUtils.accountValidation(accountIdentifier);

            log.info("serverValidation - authKey: {}, userId: {}, accountIdentifier: {}",
                    authKey, userId, accountIdentifier);

            return UtilityBean.<Account>builder()
                    .pojoObject(account)
                    .requestParams(utilityBean.getRequestParams())
                    .metadata(Map.of(Constants.USER_ID_KEY, userId))
                    .pageable(utilityBean.getPageable())
                    .build();
        } finally {
            long end = System.currentTimeMillis();
            log.info("serverValidation completed in {} ms", (end - start));
        }
    }

    /**
     * Retrieves and constructs a paginated list of ApplicationPojo objects based on the user's access rights.
     * It applies optional search and sorting, and includes cluster data if requested.
     *
     * @param utilityBean contains validated account and user metadata
     * @return paginated list of ApplicationPojo
     * @throws DataProcessingException if application data processing fails
     */
    @Override
    public Page<ApplicationPojo> process(UtilityBean<Account> utilityBean) throws DataProcessingException {
        long start = System.currentTimeMillis();
        log.info("Start process() for GetApplicationsBL");

        try {
            Account account = utilityBean.getPojoObject();
            String userId = utilityBean.getMetadata().get(Constants.USER_ID_KEY).toString();
            Pageable pageable = utilityBean.getPageable();
            String searchTerm = utilityBean.getRequestParams().get(Constants.SEARCH_TERM_KEY);

            log.debug("Fetching applications for userId: {}, accountId: {}, searchTerm: '{}', pageable: {}",
                    userId, account.getIdentifier(), searchTerm, pageable);

            List<ControllerBean> accessibleApps = userValidationUtil.getAccessibleApplicationsForUser(
                    userId, account.getIdentifier(), searchTerm, pageable);

            if (accessibleApps == null || accessibleApps.isEmpty()) {
                log.warn("No accessible applications found for user [{}] and account [{}]", userId, account.getIdentifier());
                return Page.empty();
            }

            if (searchTerm != null && !searchTerm.isEmpty()) {
                log.debug("Applying search term filter: '{}'", searchTerm);
                accessibleApps = accessibleApps.stream()
                        .filter(app -> app.getName().toLowerCase().contains(searchTerm.toLowerCase()) ||
                                app.getIdentifier().toLowerCase().contains(searchTerm.toLowerCase()))
                        .collect(Collectors.toList());
            }

            if (pageable.getSort().isSorted()) {
                log.debug("Sorting application list based on: {}", pageable.getSort());
                Comparator<ControllerBean> comparator = getApplicationComparator(pageable);
                if (comparator != null) {
                    accessibleApps = accessibleApps.stream().sorted(comparator).collect(Collectors.toList());
                }
            }

            int total = accessibleApps.size();
            int startIdx = Math.min(pageable.getPageNumber() * pageable.getPageSize(), total);
            int endIdx = Math.min(startIdx + pageable.getPageSize(), total);
            log.debug("Pagination indices -> start: {}, end: {}, total: {}", startIdx, endIdx, total);

            List<ControllerBean> pagedList = accessibleApps.subList(startIdx, endIdx);

            List<ViewApplicationServiceMappingBean> mappedServices =
                    controllerDao.getServicesMappedToApplication(
                            pagedList.get(0).getAccountId(), pagedList.get(0).getIdentifier());

            if (mappedServices == null || mappedServices.isEmpty()) {
                log.warn("No services mapped to applications for accountId: {}", pagedList.get(0).getAccountId());
                return Page.empty();
            }

            List<ApplicationPojo> result = new ArrayList<>();
            for (ControllerBean app : pagedList) {
                try {
                    log.debug("Building ApplicationPojo for application: {}", app.getIdentifier());
                    TimezoneBean timezoneBean = timeZoneDao.getApplicationTimezoneDetails(app.getId());
                    if (timezoneBean == null) {
                        log.warn("Timezone not found for application [{}]. Skipping.", app.getIdentifier());
                        continue;
                    }

                    ApplicationPojo applicationPojo = ApplicationPojo.builder()
                            .id(app.getId())
                            .identifier(app.getIdentifier())
                            .name(app.getName())
                            .environment(app.getEnvironment())
                            .lastModifiedBy(userDao.getUsernameFromIdentifier(app.getLastModifiedBy()))
                            .lastModifiedOn(dateTimeUtil.getGMTToEpochTime(app.getUpdatedTime()))
                            .services(getMappedServices(mappedServices))
                            .timezoneMilli(timezoneBean.getOffset())
                            .timeZoneString(timezoneBean.getTimeZoneId())
                            .build();

                    result.add(applicationPojo);
                    log.info("ApplicationPojo built successfully for: {}", app.getIdentifier());
                } catch (Exception e) {
                    log.error("Error processing application [{}]", app.getIdentifier(), e);
                }
            }

            log.info("Returning {} applications to client", result.size());
            return new PageImpl<>(result, pageable, total);
        } catch (Exception ex) {
            log.error("Error in processing applications", ex);
            throw new DataProcessingException("Unable to process application list");
        } finally {
            log.info("End process(), duration: {} ms", System.currentTimeMillis() - start);
        }
    }

    /**
     * Builds the list of {@link ServiceClusterDetails} for each mapped service.
     * Includes host and component cluster details only if clusterDataRequired is true.
     *
     * @param mappedServices list of services mapped to an application
     * @return list of service-cluster details
     */
    private List<ServiceClusterDetails> getMappedServices(List<ViewApplicationServiceMappingBean> mappedServices) {
        long start = System.currentTimeMillis();
        log.debug("Start getMappedServices() at {}", start);
        try {
            if (!clusterDataRequired) {
                log.debug("Cluster data not required. Returning basic service details.");
                return mappedServices.stream().map(s -> ServiceClusterDetails.builder()
                        .id(s.getServiceId())
                        .name(s.getServiceName())
                        .identifier(s.getServiceIdentifier())
                        .build()).collect(Collectors.toList());
            }

            log.debug("Cluster data required. Fetching host and component cluster details.");
            return mappedServices.stream().map(service -> {
                try {
                    List<ClusterComponentDetails> host = controllerDao.getHostClusterComponentDetailsForService(service.getServiceIdentifier());
                    List<ClusterComponentDetails> component = controllerDao.getComponentClusterComponentDetailsForService(service.getServiceIdentifier());
                    return ServiceClusterDetails.builder()
                            .id(service.getServiceId())
                            .name(service.getServiceName())
                            .identifier(service.getServiceIdentifier())
                            .hostCluster(host)
                            .componentCluster(component)
                            .build();
                } catch (Exception e) {
                    log.error("Cluster data fetch failed for serviceIdentifier: {}", service.getServiceIdentifier(), e);
                    return null;
                }
            }).filter(Objects::nonNull).collect(Collectors.toList());
        } finally {
            long end = System.currentTimeMillis();
            log.debug("End getMappedServices() at {}, duration: {} ms", end, (end - start));
        }
    }

    /**
     * Returns a comparator to sort {@link ControllerBean} list based on pageable sort orders.
     *
     * @param pageable contains sorting information
     * @return comparator for ControllerBean or null if unsupported
     */
    private Comparator<ControllerBean> getApplicationComparator(Pageable pageable) {
        Comparator<ControllerBean> comparator = null;
        for (org.springframework.data.domain.Sort.Order order : pageable.getSort()) {
            Comparator<ControllerBean> current = null;
            if (order.getProperty().equalsIgnoreCase("name")) {
                current = Comparator.comparing(ControllerBean::getName, Comparator.nullsLast(String::compareToIgnoreCase));
            } else if (order.getProperty().equalsIgnoreCase("updatedTime")) {
                current = Comparator.comparing(ControllerBean::getUpdatedTime);
            }
            if (current != null && !order.isAscending()) current = current.reversed();
            comparator = comparator == null ? current : comparator.thenComparing(current);
        }
        return comparator;
    }
}
