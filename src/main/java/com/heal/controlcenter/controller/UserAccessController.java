package com.heal.controlcenter.controller;

import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.businesslogic.UserAccessibleActionBL;
import com.heal.controlcenter.businesslogic.UserDetailsBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.ErrorResponsePojo;
import com.heal.controlcenter.pojo.ResponsePojo;
import com.heal.controlcenter.util.JsonFileParser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@Configuration
@Slf4j
public class UserAccessController {

    @Autowired
    UserAccessibleActionBL userAccessibleActionBL;
    @Autowired
    UserDetailsBL userDetailsBL;
    @Autowired
    JsonFileParser headersParser;

    @Operation(
            summary = "Retrieves user access information and permissions.",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "User access information fetched successfully.",
                            content = @Content(schema = @Schema(implementation = UserAccessibleActions.class))
                    ),
                    @ApiResponse(
                            responseCode = "500",
                            description = "Exception encountered while fetching user access information.",
                            content = @Content(schema = @Schema(implementation = ErrorResponsePojo.class))
                    ),
                    @ApiResponse(
                            responseCode = "400",
                            description = "Error occurred while fetching user access information.",
                            content = @Content(schema = @Schema(implementation = ErrorResponsePojo.class))
                    ),
                    @ApiResponse(
                            responseCode = "401",
                            description = "Unauthorized - Invalid or missing authentication token.",
                            content = @Content(schema = @Schema(implementation = ErrorResponsePojo.class))
                    )
            }
    )
    @RequestMapping(value = "/users/access-info", method = RequestMethod.GET)
    public ResponseEntity<ResponsePojo<UserAccessibleActions>> getUserAccessInformation(
            @Parameter(
                    name = "Authorization",
                    description = "Bearer token for authentication",
                    required = true,
                    example = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            )
            @RequestHeader(value = "Authorization") String authorization)
            throws ClientException, ServerException, DataProcessingException {

        UtilityBean<String> userId = userAccessibleActionBL.clientValidation(null, authorization);
        UserAttributesBean userAttributesBean = userAccessibleActionBL.serverValidation(userId);
        UserAccessibleActions userActionsDetails = userAccessibleActionBL.process(userAttributesBean);

        ResponsePojo<UserAccessibleActions> response = new ResponsePojo<>("User access information fetched successfully", userActionsDetails, HttpStatus.OK);

        return new ResponseEntity<>(response, headersParser.loadHeaderConfiguration(), response.getResponseStatus());
    }

    @Operation(
            summary = "Retrieves all users in the system.",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Users fetched successfully.",
                            content = @Content(schema = @Schema(implementation = UserDetailsBean.class))
                    ),
                    @ApiResponse(
                            responseCode = "500",
                            description = "Exception encountered while fetching users.",
                            content = @Content(schema = @Schema(implementation = ErrorResponsePojo.class))
                    ),
                    @ApiResponse(
                            responseCode = "400",
                            description = "Error occurred while fetching users.",
                            content = @Content(schema = @Schema(implementation = ErrorResponsePojo.class))
                    ),
                    @ApiResponse(
                            responseCode = "401",
                            description = "Unauthorized - Invalid or missing authentication token.",
                            content = @Content(schema = @Schema(implementation = ErrorResponsePojo.class))
                    )
            }
    )
    @RequestMapping(value = "/users", method = RequestMethod.GET)
    public ResponseEntity<Object> getUsers(
            @Parameter(
                    name = "Authorization",
                    description = "Bearer token for authentication",
                    required = true,
                    example = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            )
            @RequestHeader(value = "Authorization") String authorization) throws ClientException, DataProcessingException {

        userDetailsBL.clientValidation(null, authorization);
        List<UserDetailsBean> userDetailsBeanList = userDetailsBL.process("Users detail");

        ResponsePojo<List<UserDetailsBean>> responsePojo = new ResponsePojo<>("Users detail fetch successfully", userDetailsBeanList, HttpStatus.OK);

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
    }
}
