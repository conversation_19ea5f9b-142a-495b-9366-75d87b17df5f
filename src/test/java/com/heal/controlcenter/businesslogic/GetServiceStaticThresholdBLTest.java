package com.heal.controlcenter.businesslogic;

import com.heal.configuration.entities.KpiCategoryDetailsBean;
import com.heal.configuration.entities.UserAccessDetails;
import com.heal.configuration.pojos.*;
import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.dao.mysql.KPIDao;
import com.heal.controlcenter.dao.mysql.ServiceDao;
import com.heal.controlcenter.dao.mysql.entity.KpiDetailsBean;
import com.heal.controlcenter.dao.mysql.entity.ServiceKpiThreshold;
import com.heal.controlcenter.dao.opensearch.ServiceOpensearchRepo;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.StaticThresholdRules;
import com.heal.controlcenter.service.CommonDataService;
import com.heal.controlcenter.util.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;

import java.sql.Timestamp;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class GetServiceStaticThresholdBLTest {

    @InjectMocks
    private GetServiceStaticThresholdBL getServiceStaticThresholdBL;

    @Mock
    private CacheWrapper cacheWrapper;

    @Mock
    private CommonDataService commonDataService;

    @Mock
    private KPIDao kpiDao;

    @Mock
    private ServiceDao serviceDao;

    @Mock
    private ServiceOpensearchRepo serviceOpensearchRepo;

    @Mock
    private ServerValidationUtils serverValidationUtils;

    @Spy
    private ClientValidationUtils clientValidationUtils;

    @Spy
    private DateTimeUtil dateTimeUtil;

    @BeforeEach
    void setUp() throws ControlCenterException {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testClientValidation_ValidInputs() throws ClientException {
        String authKey = "validAuthKey";
        String accountIdentifier = "validAccount";
        String serviceIdentifier = "validService";
        String kpiType = "Core";
        String thresholdType = "static";

        String[] requestParams = {authKey, accountIdentifier, serviceIdentifier, kpiType, thresholdType};

        UtilityBean<Object> result = getServiceStaticThresholdBL.clientValidation(null, requestParams);

        assertNotNull(result);
        assertEquals(accountIdentifier, result.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER));
        assertEquals(serviceIdentifier, result.getRequestParams().get(Constants.SERVICE_IDENTIFIER));
        assertEquals(kpiType, result.getRequestParams().get(Constants.KPI_TYPE));
        assertEquals(thresholdType, result.getRequestParams().get(Constants.THRESHOLD_TYPE));
    }

    @Test
    void testClientValidation_InvalidThresholdType() {
        String[] requestParams = {"validAuthKey", "validAccount", "validService", "Core", ""};

        ClientException exception = assertThrows(ClientException.class, () ->
                getServiceStaticThresholdBL.clientValidation(null, requestParams));

        assertEquals("ClientException : " +  UIMessages.THRESHOLD_TYPE_INVALID, exception.getMessage());
    }

//    Server Validation Test Cases
    @Test
    void testServerValidation_ValidInputs() throws ServerException {
        HashMap<String, List<ViewTypes>> viewTypesMap = new HashMap<>();
        HashMap<String, Object> metadata = new HashMap<>();
        metadata.put(Constants.USER_ID, "testUserId");
        fillViewTypeData(viewTypesMap, metadata);
        when(cacheWrapper.getAllViewTypesIdMap()).thenReturn(viewTypesMap);

        UtilityBean<Object> utilityBean = UtilityBean.builder()
                .requestParams(new HashMap<>() {{
                    put("KPI", "Core");
                    put("accountIdentifier", "validAccount");
                    put("serviceIdentifier", "validService");
                    put("authKey", "validAuthKey");
                }})
                .metadata(metadata)
                .build();

        com.heal.configuration.entities.UserAccessDetails userAccessDetails = new UserAccessDetails();

        when(serverValidationUtils.accountValidation("validAccount")).thenReturn((Account) metadata.get(Constants.ACCOUNT));
        when(serverValidationUtils.serviceValidation(anyString(),anyString(), anyString(), any())).thenReturn((Service) metadata.get(Constants.SERVICE));
        when(serverValidationUtils.userAccessDetailsValidation("testUserId", "validAccount")).thenReturn(userAccessDetails);
        UtilityBean<Map<String, Object>> result = getServiceStaticThresholdBL.serverValidation(utilityBean);
        assertNotNull(result);
    }

    @Test
    void testServerValidation_InvalidKpiType() throws ServerException {

        HashMap<String, List<ViewTypes>> viewTypesMap = new HashMap<>();
        HashMap<String, Object> metadata = new HashMap<>();
        metadata.put(Constants.USER_ID, "testUserId");
        fillViewTypeData(viewTypesMap, metadata);
        when(cacheWrapper.getAllViewTypesIdMap()).thenReturn(viewTypesMap);

        UtilityBean<Object> utilityBean = UtilityBean.builder()
                .requestParams(new HashMap<>() {{
                    put("KPI", "invalidKpiType");
                    put("accountIdentifier", "validAccount");
                    put("serviceIdentifier", "validService");
                }})
                .metadata(metadata)
                .build();


        com.heal.configuration.entities.UserAccessDetails userAccessDetails = new UserAccessDetails();

        when(serverValidationUtils.accountValidation("validAccount")).thenReturn((Account) metadata.get(Constants.ACCOUNT));
        when(serverValidationUtils.serviceValidation(anyString(), anyString(), anyString(), any())).thenReturn((Service) metadata.get(Constants.SERVICE));
        when(serverValidationUtils.userAccessDetailsValidation("testUserId", "validAccount")).thenReturn(userAccessDetails);

        ServerException exception = assertThrows(ServerException.class, () ->
                getServiceStaticThresholdBL.serverValidation(utilityBean));

        assertEquals("ServerException : " + UIMessages.INVALID_KPI_TYPE, exception.getMessage());
    }

    @Test
    void testServerValidation_LowThresholdSeverityTypeNull() throws ServerException {
        // Setup view types map with missing low threshold severity type
        HashMap<String, List<ViewTypes>> viewTypesMap = new HashMap<>();
        HashMap<String, Object> metadata = new HashMap<>();
        metadata.put(Constants.USER_ID, "testUserId");
        fillViewTypeData(viewTypesMap, metadata);

        // Remove low threshold severity type
        List<ViewTypes> originalSeverityTypes = viewTypesMap.get(Constants.THRESHOLD_SEVERITY_TYPE);
        List<ViewTypes> modifiedSeverityTypes = new ArrayList<>();
        for (ViewTypes type : originalSeverityTypes) {
            if (!type.getSubTypeName().equals(Constants.THRESHOLD_SEVERITY_TYPE_LOW)) {
                modifiedSeverityTypes.add(type);
            }
        }
        viewTypesMap.put(Constants.THRESHOLD_SEVERITY_TYPE, modifiedSeverityTypes);

        when(cacheWrapper.getAllViewTypesIdMap()).thenReturn(viewTypesMap);

        // Setup utility bean with necessary request params
        UtilityBean<Object> utilityBean = UtilityBean.builder()
                .requestParams(new HashMap<>() {{
                    put(Constants.KPI_TYPE, "Core");
                    put(Constants.ACCOUNT_IDENTIFIER, "validAccount");
                    put(Constants.SERVICE_IDENTIFIER, "validService");
                    put(Constants.AUTH_KEY, "validAuthKey");
                }})
                .pojoObject(null)
                .build();

        // Setup mocks for validations
        Account accountBean = new Account();
        accountBean.setIdentifier("validAccount");
        accountBean.setName("Valid Account");
        accountBean.setId(1);

        Service service = new Service();
        service.setId(1);
        service.setIdentifier("validService");

        UserAccessDetails userAccessDetails = new UserAccessDetails();

        when(serverValidationUtils.authKeyValidation("validAuthKey")).thenReturn("testUserId");
        when(serverValidationUtils.accountValidation("validAccount")).thenReturn(accountBean);
        when(serverValidationUtils.serviceValidation(anyString(), anyString(), anyString(), any())).thenReturn(service);
        when(serverValidationUtils.userAccessDetailsValidation("testUserId", "validAccount")).thenReturn(userAccessDetails);

        // Execute and verify exception
        ServerException exception = assertThrows(ServerException.class, () ->
                getServiceStaticThresholdBL.serverValidation(utilityBean));

        assertEquals("ServerException : Failure in data validation", exception.getMessage());
    }

    @Test
    void testServerValidation_MediumThresholdSeverityTypeNull() throws ServerException {
        // Setup view types map with missing medium threshold severity type
        HashMap<String, List<ViewTypes>> viewTypesMap = new HashMap<>();
        HashMap<String, Object> metadata = new HashMap<>();
        metadata.put(Constants.USER_ID, "testUserId");
        fillViewTypeData(viewTypesMap, metadata);

        // Remove medium threshold severity type
        List<ViewTypes> originalSeverityTypes = viewTypesMap.get(Constants.THRESHOLD_SEVERITY_TYPE);
        List<ViewTypes> modifiedSeverityTypes = new ArrayList<>();
        for (ViewTypes type : originalSeverityTypes) {
            if (!type.getSubTypeName().equals(Constants.THRESHOLD_SEVERITY_TYPE_MEDIUM)) {
                modifiedSeverityTypes.add(type);
            }
        }
        viewTypesMap.put(Constants.THRESHOLD_SEVERITY_TYPE, modifiedSeverityTypes);

        when(cacheWrapper.getAllViewTypesIdMap()).thenReturn(viewTypesMap);

        // Setup utility bean with necessary request params
        UtilityBean<Object> utilityBean = UtilityBean.builder()
                .requestParams(new HashMap<>() {{
                    put(Constants.KPI_TYPE, "Core");
                    put(Constants.ACCOUNT_IDENTIFIER, "validAccount");
                    put(Constants.SERVICE_IDENTIFIER, "validService");
                    put(Constants.AUTH_KEY, "validAuthKey");
                }})
                .pojoObject(null)
                .build();

        // Setup mocks for validations
        Account accountBean = new Account();
        accountBean.setIdentifier("validAccount");
        accountBean.setName("Valid Account");
        accountBean.setId(1);

        Service service = new Service();
        service.setId(1);
        service.setIdentifier("validService");

        UserAccessDetails userAccessDetails = new UserAccessDetails();

        when(serverValidationUtils.authKeyValidation("validAuthKey")).thenReturn("testUserId");
        when(serverValidationUtils.accountValidation("validAccount")).thenReturn(accountBean);
        when(serverValidationUtils.serviceValidation(anyString(), anyString(), anyString(), any())).thenReturn(service);
        when(serverValidationUtils.userAccessDetailsValidation("testUserId", "validAccount")).thenReturn(userAccessDetails);

        // Execute and verify exception
        ServerException exception = assertThrows(ServerException.class, () ->
                getServiceStaticThresholdBL.serverValidation(utilityBean));

        assertEquals("ServerException : Failure in data validation", exception.getMessage());
    }

    @Test
    void testServerValidation_HighThresholdSeverityTypeNull() throws ServerException {
        // Setup view types map with missing high threshold severity type
        HashMap<String, List<ViewTypes>> viewTypesMap = new HashMap<>();
        HashMap<String, Object> metadata = new HashMap<>();
        metadata.put(Constants.USER_ID, "testUserId");
        fillViewTypeData(viewTypesMap, metadata);

        // Remove high threshold severity type
        List<ViewTypes> originalSeverityTypes = viewTypesMap.get(Constants.THRESHOLD_SEVERITY_TYPE);
        List<ViewTypes> modifiedSeverityTypes = new ArrayList<>();
        for (ViewTypes type : originalSeverityTypes) {
            if (!type.getSubTypeName().equals(Constants.THRESHOLD_SEVERITY_TYPE_HIGH)) {
                modifiedSeverityTypes.add(type);
            }
        }
        viewTypesMap.put(Constants.THRESHOLD_SEVERITY_TYPE, modifiedSeverityTypes);

        when(cacheWrapper.getAllViewTypesIdMap()).thenReturn(viewTypesMap);

        // Setup utility bean with necessary request params
        UtilityBean<Object> utilityBean = UtilityBean.builder()
                .requestParams(new HashMap<>() {{
                    put(Constants.KPI_TYPE, "Core");
                    put(Constants.ACCOUNT_IDENTIFIER, "validAccount");
                    put(Constants.SERVICE_IDENTIFIER, "validService");
                    put(Constants.AUTH_KEY, "validAuthKey");
                }})
                .pojoObject(null)
                .build();

        // Setup mocks for validations
        Account accountBean = new Account();
        accountBean.setIdentifier("validAccount");
        accountBean.setName("Valid Account");
        accountBean.setId(1);

        Service service = new Service();
        service.setId(1);
        service.setIdentifier("validService");

        UserAccessDetails userAccessDetails = new UserAccessDetails();

        when(serverValidationUtils.authKeyValidation("validAuthKey")).thenReturn("testUserId");
        when(serverValidationUtils.accountValidation("validAccount")).thenReturn(accountBean);
        when(serverValidationUtils.serviceValidation(anyString(), anyString(), anyString(), any())).thenReturn(service);
        when(serverValidationUtils.userAccessDetailsValidation("testUserId", "validAccount")).thenReturn(userAccessDetails);

        // Execute and verify exception
        ServerException exception = assertThrows(ServerException.class, () ->
                getServiceStaticThresholdBL.serverValidation(utilityBean));

        assertEquals("ServerException : Failure in data validation", exception.getMessage());
    }

    @Test
    void testServerValidation_LessThanOperationTypeNull() throws ServerException {
        // Setup view types map with missing less than operation type
        HashMap<String, List<ViewTypes>> viewTypesMap = new HashMap<>();
        HashMap<String, Object> metadata = new HashMap<>();
        metadata.put(Constants.USER_ID, "testUserId");
        fillViewTypeData(viewTypesMap, metadata);

        // Remove less than operation type
        List<ViewTypes> originalOperationTypes = viewTypesMap.get(Constants.OPERATIONS_TYPE);
        List<ViewTypes> modifiedOperationTypes = new ArrayList<>();
        for (ViewTypes type : originalOperationTypes) {
            if (!type.getSubTypeName().equals(Constants.OPERATIONS_TYPE_LESSER_THAN)) {
                modifiedOperationTypes.add(type);
            }
        }
        viewTypesMap.put(Constants.OPERATIONS_TYPE, modifiedOperationTypes);

        when(cacheWrapper.getAllViewTypesIdMap()).thenReturn(viewTypesMap);

        // Setup utility bean with necessary request params
        UtilityBean<Object> utilityBean = UtilityBean.builder()
                .requestParams(new HashMap<>() {{
                    put(Constants.KPI_TYPE, "Core");
                    put(Constants.ACCOUNT_IDENTIFIER, "validAccount");
                    put(Constants.SERVICE_IDENTIFIER, "validService");
                    put(Constants.AUTH_KEY, "validAuthKey");
                }})
                .pojoObject(null)
                .build();

        // Setup mocks for validations
        Account accountBean = new Account();
        accountBean.setIdentifier("validAccount");
        accountBean.setName("Valid Account");
        accountBean.setId(1);

        Service service = new Service();
        service.setId(1);
        service.setIdentifier("validService");

        UserAccessDetails userAccessDetails = new UserAccessDetails();

        when(serverValidationUtils.authKeyValidation("validAuthKey")).thenReturn("testUserId");
        when(serverValidationUtils.accountValidation("validAccount")).thenReturn(accountBean);
        when(serverValidationUtils.serviceValidation(anyString(), anyString(), anyString(), any())).thenReturn(service);
        when(serverValidationUtils.userAccessDetailsValidation("testUserId", "validAccount")).thenReturn(userAccessDetails);

        // Execute and verify exception
        ServerException exception = assertThrows(ServerException.class, () ->
                getServiceStaticThresholdBL.serverValidation(utilityBean));

        assertEquals("ServerException : Failure in data validation", exception.getMessage());
    }

    @Test
    void testServerValidation_GreaterThanOperationTypeNull() throws ServerException {
        // Setup view types map with missing greater than operation type
        HashMap<String, List<ViewTypes>> viewTypesMap = new HashMap<>();
        HashMap<String, Object> metadata = new HashMap<>();
        metadata.put(Constants.USER_ID, "testUserId");
        fillViewTypeData(viewTypesMap, metadata);

        // Remove greater than operation type
        List<ViewTypes> originalOperationTypes = viewTypesMap.get(Constants.OPERATIONS_TYPE);
        List<ViewTypes> modifiedOperationTypes = new ArrayList<>();
        for (ViewTypes type : originalOperationTypes) {
            if (!type.getSubTypeName().equals(Constants.OPERATIONS_TYPE_GREATER_THAN)) {
                modifiedOperationTypes.add(type);
            }
        }
        viewTypesMap.put(Constants.OPERATIONS_TYPE, modifiedOperationTypes);

        when(cacheWrapper.getAllViewTypesIdMap()).thenReturn(viewTypesMap);

        // Setup utility bean with necessary request params
        UtilityBean<Object> utilityBean = UtilityBean.builder()
                .requestParams(new HashMap<>() {{
                    put(Constants.KPI_TYPE, "Core");
                    put(Constants.ACCOUNT_IDENTIFIER, "validAccount");
                    put(Constants.SERVICE_IDENTIFIER, "validService");
                    put(Constants.AUTH_KEY, "validAuthKey");
                }})
                .pojoObject(null)
                .build();

        // Setup mocks for validations
        Account accountBean = new Account();
        accountBean.setIdentifier("validAccount");
        accountBean.setName("Valid Account");
        accountBean.setId(1);

        Service service = new Service();
        service.setId(1);
        service.setIdentifier("validService");

        UserAccessDetails userAccessDetails = new UserAccessDetails();

        when(serverValidationUtils.authKeyValidation("validAuthKey")).thenReturn("testUserId");
        when(serverValidationUtils.accountValidation("validAccount")).thenReturn(accountBean);
        when(serverValidationUtils.serviceValidation(anyString(), anyString(), anyString(), any())).thenReturn(service);
        when(serverValidationUtils.userAccessDetailsValidation("testUserId", "validAccount")).thenReturn(userAccessDetails);

        // Execute and verify exception
        ServerException exception = assertThrows(ServerException.class, () ->
                getServiceStaticThresholdBL.serverValidation(utilityBean));

        assertEquals("ServerException : Failure in data validation", exception.getMessage());
    }

    @Test
    void testServerValidation_NotBetweenOperationTypeNull() throws ServerException {
        // Setup view types map with missing not between operation type
        HashMap<String, List<ViewTypes>> viewTypesMap = new HashMap<>();
        HashMap<String, Object> metadata = new HashMap<>();
        metadata.put(Constants.USER_ID, "testUserId");
        fillViewTypeData(viewTypesMap, metadata);

        // Remove not between operation type
        List<ViewTypes> originalOperationTypes = viewTypesMap.get(Constants.OPERATIONS_TYPE);
        List<ViewTypes> modifiedOperationTypes = new ArrayList<>();
        for (ViewTypes type : originalOperationTypes) {
            if (!type.getSubTypeName().equals(Constants.OPERATIONS_TYPE_NOT_BETWEEN)) {
                modifiedOperationTypes.add(type);
            }
        }
        viewTypesMap.put(Constants.OPERATIONS_TYPE, modifiedOperationTypes);

        when(cacheWrapper.getAllViewTypesIdMap()).thenReturn(viewTypesMap);

        // Setup utility bean with necessary request params
        UtilityBean<Object> utilityBean = UtilityBean.builder()
                .requestParams(new HashMap<>() {{
                    put(Constants.KPI_TYPE, "Core");
                    put(Constants.ACCOUNT_IDENTIFIER, "validAccount");
                    put(Constants.SERVICE_IDENTIFIER, "validService");
                    put(Constants.AUTH_KEY, "validAuthKey");
                }})
                .pojoObject(null)
                .build();

        // Setup mocks for validations
        Account accountBean = new Account();
        accountBean.setIdentifier("validAccount");
        accountBean.setName("Valid Account");
        accountBean.setId(1);

        Service service = new Service();
        service.setId(1);
        service.setIdentifier("validService");

        UserAccessDetails userAccessDetails = new UserAccessDetails();

        when(serverValidationUtils.authKeyValidation("validAuthKey")).thenReturn("testUserId");
        when(serverValidationUtils.accountValidation("validAccount")).thenReturn(accountBean);
        when(serverValidationUtils.serviceValidation(anyString(), anyString(), anyString(), any())).thenReturn(service);
        when(serverValidationUtils.userAccessDetailsValidation("testUserId", "validAccount")).thenReturn(userAccessDetails);

        // Execute and verify exception
        ServerException exception = assertThrows(ServerException.class, () ->
                getServiceStaticThresholdBL.serverValidation(utilityBean));

        assertEquals("ServerException : Failure in data validation", exception.getMessage());
    }


//    process test case start

    @Test
    void testProcess_NormalExecution() throws Exception {
        // Setup view types and metadata
        HashMap<String, List<ViewTypes>> viewTypesMap = new HashMap<>();
        HashMap<String, Object> metadata = new HashMap<>();
        metadata.put(Constants.USER_ID, "testUserId");
        fillViewTypeData(viewTypesMap, metadata);
        when(cacheWrapper.getAllViewTypesIdMap()).thenReturn(viewTypesMap);

        // Setup service, account, and request params
        UtilityBean<Map<String, Object>> utilityBean = UtilityBean.<Map<String, Object>>builder()
                .requestParams(new HashMap<>() {{
                    put(Constants.KPI_TYPE, "Core");
                    put(Constants.ACCOUNT_IDENTIFIER, "validAccount");
                    put(Constants.SERVICE_IDENTIFIER, "validService");
                }})
                .pojoObject(metadata)
                .build();

        // Mock component instances
        CompInstClusterDetailsBean comp = new CompInstClusterDetailsBean();
        comp.setCompId(1); comp.setCommonVersionId(1);
        when(commonDataService.getComponentClusterList(anyInt(), anyInt()))
                .thenReturn(List.of(comp));

        // Mock KPI details and categories
        KpiDetailsBean kpi = new KpiDetailsBean();
        kpi.setId(1); kpi.setTypeId(4); kpi.setCommonVersionId(1); kpi.setComponentId(1);
        kpi.setName("KPI"); kpi.setDataType("numeric"); kpi.setMeasureUnits("ms"); kpi.setComputedFormula("f");
        when(kpiDao.getAllKpiDetailsKpiList()).thenReturn(List.of(kpi));
        KpiCategoryDetailsBean cat = new KpiCategoryDetailsBean();
        cat.setId(1); cat.setKpiId(1); cat.setName("Cat");
        when(kpiDao.getAllKpiCategoryDetails()).thenReturn(List.of(cat));

        // Mock thresholds
        ServiceKpiThreshold threshold = new ServiceKpiThreshold();
        threshold.setKpiId(1); threshold.setThresholdSeverityId(1); threshold.setOperationTypeId(5);
        threshold.setApplicableTo("instances"); threshold.setStatus(1);

        ServiceKpiThreshold threshold1 = new ServiceKpiThreshold();
        threshold1.setKpiId(1); threshold1.setThresholdSeverityId(2); threshold1.setOperationTypeId(5);
        threshold1.setApplicableTo("instances"); threshold1.setStatus(1);

        ServiceKpiThreshold threshold2 = new ServiceKpiThreshold();
        threshold2.setKpiId(1); threshold2.setThresholdSeverityId(3); threshold2.setOperationTypeId(5);
        threshold2.setApplicableTo("instances"); threshold2.setStatus(1);
        when(serviceDao.getAllKpiThresholds(anyInt(), anyInt())).thenReturn(List.of(threshold,threshold2,threshold1));

        // Mock OpenSearch
        com.appnomic.appsone.opeasearchquery.results.TabularResults osResults = mock(com.appnomic.appsone.opeasearchquery.results.TabularResults.class);
        when(osResults.getRowResults()).thenReturn(Collections.emptyList());
        when(serviceOpensearchRepo.getAllConfigureThresholdsKpiListsFromOS(anyString(), anyString(), anySet()))
                .thenReturn(osResults);
        when(dateTimeUtil.getCurrentTimestampInGMT()).thenReturn(new Timestamp(System.currentTimeMillis()));
        doNothing().when(serviceOpensearchRepo).insertBulkServiceKpiThresholdsIntoOS(anyString(), anyString(), anyList(), any(), anyString());

        // Execute
        List<StaticThresholdRules> result = getServiceStaticThresholdBL.process(utilityBean);

        // Verify
        assertNotNull(result);
        assertFalse(result.isEmpty());
        verify(serviceOpensearchRepo).insertBulkServiceKpiThresholdsIntoOS(anyString(), anyString(), anyList(), any(), anyString());
    }

    @Test
    void testProcess_EmptyComponentInstances() throws Exception {
        // Setup
        HashMap<String, List<ViewTypes>> viewTypesMap = new HashMap<>();
        HashMap<String, Object> metadata = new HashMap<>();
        metadata.put(Constants.USER_ID, "testUserId");
        fillViewTypeData(viewTypesMap, metadata);
        when(cacheWrapper.getAllViewTypesIdMap()).thenReturn(viewTypesMap);

        UtilityBean<Map<String, Object>> utilityBean = UtilityBean.<Map<String, Object>>builder()
                .requestParams(new HashMap<>() {{
                    put(Constants.KPI_TYPE, "Core");
                    put(Constants.ACCOUNT_IDENTIFIER, "validAccount");
                    put(Constants.SERVICE_IDENTIFIER, "validService");
                }})
                .pojoObject(metadata)
                .build();

        when(commonDataService.getComponentClusterList(anyInt(), anyInt()))
                .thenReturn(Collections.emptyList());

        // Execute
        List<StaticThresholdRules> result = getServiceStaticThresholdBL.process(utilityBean);

        // Verify
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }


    private void fillViewTypeData(HashMap<String, List<ViewTypes>> viewTypesIdMap, HashMap<String,Object> metadata){
        // Mock cacheWrapper methods
        ViewTypes lowThresholdViewType = new ViewTypes();
        lowThresholdViewType.setTypeId(1);
        lowThresholdViewType.setTypeName(Constants.THRESHOLD_SEVERITY_TYPE);
        lowThresholdViewType.setSubTypeName(Constants.THRESHOLD_SEVERITY_TYPE_LOW);
        lowThresholdViewType.setSubTypeId(1);
        metadata.put(Constants.THRESHOLD_SEVERITY_TYPE_LOW, lowThresholdViewType);

        ViewTypes mediumThresholdViewType = new ViewTypes();
        mediumThresholdViewType.setTypeId(1);
        mediumThresholdViewType.setTypeName(Constants.THRESHOLD_SEVERITY_TYPE);
        mediumThresholdViewType.setSubTypeName(Constants.THRESHOLD_SEVERITY_TYPE_MEDIUM);
        mediumThresholdViewType.setSubTypeId(2);
        metadata.put(Constants.THRESHOLD_SEVERITY_TYPE_MEDIUM, mediumThresholdViewType);

        ViewTypes highThresholdViewType = new ViewTypes();
        highThresholdViewType.setTypeId(1);
        highThresholdViewType.setTypeName(Constants.THRESHOLD_SEVERITY_TYPE);
        highThresholdViewType.setSubTypeName(Constants.THRESHOLD_SEVERITY_TYPE_HIGH);
        highThresholdViewType.setSubTypeId(3);
        metadata.put(Constants.THRESHOLD_SEVERITY_TYPE_HIGH, highThresholdViewType);

        ViewTypes kpiTypeView = new ViewTypes();
        kpiTypeView.setTypeId(2);
        kpiTypeView.setTypeName(Constants.KPI_TYPE);
        kpiTypeView.setSubTypeName("Core");
        kpiTypeView.setSubTypeId(4);
        metadata.put("Core", kpiTypeView);

        ViewTypes lesserOperationType = new ViewTypes();
        lesserOperationType.setTypeId(3);
        lesserOperationType.setTypeName(Constants.OPERATIONS_TYPE);
        lesserOperationType.setSubTypeName(Constants.OPERATIONS_TYPE_LESSER_THAN);
        lesserOperationType.setSubTypeId(5);
        metadata.put(Constants.OPERATIONS_TYPE_LESSER_THAN, lesserOperationType);

        ViewTypes greaterOperationType = new ViewTypes();
        greaterOperationType.setTypeId(3);
        greaterOperationType.setTypeName(Constants.OPERATIONS_TYPE);
        greaterOperationType.setSubTypeName(Constants.OPERATIONS_TYPE_GREATER_THAN);
        greaterOperationType.setSubTypeId(6);
        metadata.put(Constants.OPERATIONS_TYPE_GREATER_THAN, greaterOperationType);

        ViewTypes notBetweenOperationType = new ViewTypes();
        notBetweenOperationType.setTypeId(3);
        notBetweenOperationType.setTypeName(Constants.OPERATIONS_TYPE);
        notBetweenOperationType.setSubTypeName(Constants.OPERATIONS_TYPE_NOT_BETWEEN);
        notBetweenOperationType.setSubTypeId(7);
        metadata.put(Constants.OPERATIONS_TYPE_NOT_BETWEEN, notBetweenOperationType);

        ViewTypes coreKpiTypeView = new ViewTypes();
        coreKpiTypeView.setTypeId(2);
        coreKpiTypeView.setTypeName(Constants.KPI_TYPE);
        coreKpiTypeView.setSubTypeName("Core");
        coreKpiTypeView.setSubTypeId(4);
        metadata.put("Core", coreKpiTypeView);

        ViewTypes availabilityKpiType = new ViewTypes();
        availabilityKpiType.setTypeId(2);
        availabilityKpiType.setTypeName(Constants.KPI_TYPE);
        availabilityKpiType.setSubTypeName("Availability");
        availabilityKpiType.setSubTypeId(8);
        metadata.put("Availability", availabilityKpiType);

        viewTypesIdMap.put(Constants.THRESHOLD_SEVERITY_TYPE, Arrays.asList(lowThresholdViewType, mediumThresholdViewType, highThresholdViewType));
        viewTypesIdMap.put(Constants.KPI_TYPE, Arrays.asList(kpiTypeView, coreKpiTypeView, availabilityKpiType));
        viewTypesIdMap.put(Constants.OPERATIONS_TYPE, Arrays.asList(lesserOperationType, greaterOperationType, notBetweenOperationType));

        Account accountBean = Account.builder().id(1).identifier("validAccount").name("Valid Account")
                .lastModifiedBy("testUserId").build();
        Service controllerBean = Service.builder().id(1).identifier("validService").name("Valid Service")
                .accountId(1).lastModifiedBy("testUserId")
                .serviceConfiguration(ServiceConfiguration.builder()
                        .lowEnable(true)
                        .mediumEnable(true)
                        .highEnable(true)
                        .persistenceSuppressionConfigurations(Collections.singletonList(
                                PersistenceSuppressionConfiguration.builder()
                                        .startCollectionInterval(60)
                                        .lowPersistence(10)
                                        .lowSuppression(20)
                                        .mediumPersistence(30)
                                        .mediumSuppression(40)
                                        .highPersistence(50)
                                        .highSuppression(60)
                                        .build()))
                .build()).build();

        metadata.put(Constants.ACCOUNT, accountBean);
        metadata.put(Constants.SERVICE, controllerBean);
    }

}
