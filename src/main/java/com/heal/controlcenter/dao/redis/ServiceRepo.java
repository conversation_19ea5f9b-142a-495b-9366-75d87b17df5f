package com.heal.controlcenter.dao.redis;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.configuration.pojos.KpiDetails;

import com.heal.configuration.pojos.Service;
import com.heal.controlcenter.util.RedisUtilities;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
@Slf4j
public class ServiceRepo {
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private RedisUtilities redisUtilities;

    public List<BasicEntity> getAllServicesDetails(String accountIdentifier) {
        String key = "/accounts/" + accountIdentifier + "/services";
        String hashKey = "ACCOUNTS_" + accountIdentifier + "_SERVICES";
        try {
            String serviceDetails = redisUtilities.getKey(key, hashKey);
            if (serviceDetails == null || serviceDetails.trim().isEmpty()) {
                log.warn("Null/Empty value found for key {} in redis.", key);
                return Collections.emptyList();
            }
            return objectMapper.readValue(serviceDetails, new TypeReference<List<BasicEntity>>() {
            });
        } catch (Exception e) {
            log.error("Exception encountered while getting key '{}': {}", key, hashKey, e);
            return Collections.emptyList();
        }
    }

    public Service getServiceConfigurationByIdentifier(String accountIdentifier, String serviceIdentifier) {
        String key = "/accounts/" + accountIdentifier + "/services/" + serviceIdentifier;
        String hashKey = "ACCOUNTS_" + accountIdentifier + "_SERVICES_" + serviceIdentifier;
        try {
            String serviceDetails = redisUtilities.getKey(key, hashKey);
            if (serviceDetails == null || serviceDetails.trim().isEmpty()) {
                log.warn("Null/Empty value found for key {} in redis.", key);
                return null;
            }
            return objectMapper.readValue(serviceDetails, new TypeReference<Service>() {
            });
        } catch (Exception e) {
            log.error("Exception encountered while getting key '{}': {}", key, hashKey, e);
            return null;
        }
    }

    public void updateServiceConfigurationByServiceIdentifier(String accountIdentifier, String serviceIdentifier, Service serviceConfiguration) {
        String key = "/accounts/" + accountIdentifier + "/services/" + serviceIdentifier;
        String hashKey = "ACCOUNTS_" + accountIdentifier + "_SERVICES_" + serviceIdentifier;
        try {
            redisUtilities.updateKey(key, hashKey,serviceConfiguration);
        } catch (Exception e) {
            log.error("Exception encountered while getting key '{}': {}", key, hashKey, e);
        }
    }

    public KpiDetails getServiceKPI(String accountIdentifier, String serviceIdentifier, int kpiId) {

        String key = "/accounts/" + accountIdentifier + "/services/" + serviceIdentifier + "/kpis/" + kpiId;
        String hashKey = "ACCOUNTS_" + accountIdentifier + "_SERVICES_" + serviceIdentifier + "_KPIS_" + kpiId;
        try {
            String serviceKpi = redisUtilities.getKey(key, hashKey);
            if (serviceKpi == null || serviceKpi.trim().isEmpty()) {
                log.warn("Null/Empty value found for key {} in redis.", key);
                return null;
            }
            return objectMapper.readValue(serviceKpi, new TypeReference<KpiDetails>() {
            });
        } catch (Exception e) {
            log.error("Error occurred while getting service KPI for service [{}] and KPI id [{}] ", serviceIdentifier, kpiId);
            return null;
        }
    }

    public void updateServiceKpiById(String accountIdentifier, String serviceIdentifier, int kpiId, KpiDetails serviceKPI) {
        String key = "/accounts/" + accountIdentifier + "/services/" + serviceIdentifier + "/kpis/" + kpiId;
        String hashKey = "ACCOUNTS_" + accountIdentifier + "_SERVICES_" + serviceIdentifier + "_KPIS_" + kpiId;
        try {
            redisUtilities.updateKey(key, hashKey,serviceKPI);
        } catch (Exception e) {
            log.error("Error occurred while updating service Kpi for service: [{}] and kpiId: [{}]", serviceIdentifier, kpiId, e);
        }
    }

    public void updateServiceKpiByIdentifier(String accountIdentifier, String serviceIdentifier, String kpiIdentifier, KpiDetails serviceKPI) {
        String key = "/accounts/" + accountIdentifier + "/services/" + serviceIdentifier + "/kpis/" + kpiIdentifier;
        String hashKey = "ACCOUNTS_" + accountIdentifier + "_SERVICES_" + serviceIdentifier + "_KPIS_" + kpiIdentifier;
        try {
            redisUtilities.updateKey(key, hashKey,serviceKPI);
        } catch (Exception e) {
            log.error("Error occurred while updating service Kpi for service: [{}] and kpiIdentifier: [{}]", serviceIdentifier, kpiIdentifier, e);
        }
    }
}

