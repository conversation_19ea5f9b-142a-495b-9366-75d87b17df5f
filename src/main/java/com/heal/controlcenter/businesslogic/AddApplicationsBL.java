package com.heal.controlcenter.businesslogic;

import com.appnomic.appsone.util.ConfProperties;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.AnomalyConfiguration;
import com.heal.controlcenter.aop.LogExecutionAnnotation;
import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.dao.mysql.*;
import com.heal.controlcenter.dao.redis.ApplicationRepo;
import com.heal.controlcenter.dao.redis.MasterDataRepo;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.Application;
import com.heal.controlcenter.pojo.ApplicationAnomalyConfiguration;
import com.heal.controlcenter.pojo.IdPojo;
import com.heal.controlcenter.pojo.TagMappingDetails;
import com.heal.controlcenter.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AddApplicationsBL implements BusinessLogic<List<Application>, UtilityBean<List<ApplicationBean>>, List<IdPojo>> {

    private final UserDao userDao;
    private final AccountsDao accountsDao;
    private final ControllerDao controllerDao;
    private final MasterDataDao masterDataDao;
    private final ApplicationNotifAndPercentileDao applicationNotifAndPercentileDao;
    private final TagsDao tagsDao;
    private final DateTimeUtil dateTimeUtil;
    private final ClientValidationUtils clientValidationUtils;
    private final ServerValidationUtils serverValidationUtils;
    private final ApplicationRepo applicationRepo;
    private final MasterDataRepo masterDataRepo;

    public AddApplicationsBL(UserDao userDao, AccountsDao accountsDao, ControllerDao controllerDao,
                             MasterDataDao masterDataDao, ApplicationNotifAndPercentileDao applicationNotifAndPercentileDao,
                             TagsDao tagsDao, DateTimeUtil dateTimeUtil, ClientValidationUtils clientValidationUtils,
                             ServerValidationUtils serverValidationUtils, ApplicationRepo applicationRepo,
                             MasterDataRepo masterDataRepo) {
        this.userDao = userDao;
        this.accountsDao = accountsDao;
        this.controllerDao = controllerDao;
        this.masterDataDao = masterDataDao;
        this.applicationNotifAndPercentileDao = applicationNotifAndPercentileDao;
        this.tagsDao = tagsDao;
        this.dateTimeUtil = dateTimeUtil;
        this.clientValidationUtils = clientValidationUtils;
        this.serverValidationUtils = serverValidationUtils;
        this.applicationRepo = applicationRepo;
        this.masterDataRepo = masterDataRepo;
    }

    /**
     * Performs client-side validation for a list of {@link Application} objects.
     * <p>
     * Validates the application list and account identifier, and checks each application for required fields and correctness.
     * Throws {@link ClientException} if validation fails.
     *
     * @param applications  List of {@link Application} objects to be validated.
     * @param requestParams Variable arguments, where the first element is expected to be the account identifier.
     * @return A {@link UtilityBean} containing the validated application list, request parameters, and empty metadata.
     * @throws ClientException if validation fails for the request body, account identifier, or any application fields.
     */
    @LogExecutionAnnotation
    public UtilityBean<List<Application>> clientValidation(List<Application> applications, String... requestParams) throws ClientException {
        log.debug("[clientValidation] Start - applications: {}, requestParams: {}", applications, Arrays.toString(requestParams));
        if (applications == null || applications.isEmpty()) {
            log.error("[ClientValidation] Request body is empty");
            throw new ClientException("Request body cannot be empty");
        }
        String accountIdentifier = requestParams[0];
        log.debug("[ClientValidation] Validating accountIdentifier: {}", accountIdentifier);
        clientValidationUtils.accountIdentifierValidation(accountIdentifier);

        Map<String, String> errors = new HashMap<>();
        for (int i = 0; i < applications.size(); i++) {
            Application app = applications.get(i);
            if (app == null) {
                errors.put("applications[" + i + "]", "Application object is null");
                continue;
            }
            log.debug("[ClientValidation] Validating application at index {}: {}", i, app);
            Map<String, String> appErrors = app.validate();
            for (Map.Entry<String, String> entry : appErrors.entrySet()) {
                errors.put("applications[" + i + "]." + entry.getKey(), entry.getValue());
            }
        }

        if (!errors.isEmpty()) {
            log.error("[ClientValidation] Application validation failed: {}", errors);
            throw new ClientException(errors.toString());
        }

        Map<String, String> requestParamsMap = new HashMap<>();
        requestParamsMap.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier);

        log.info("[ClientValidation] All applications validated successfully for accountIdentifier: {}", accountIdentifier);
        return UtilityBean.<List<Application>>builder()
                .requestParams(requestParamsMap)
                .pojoObject(applications)
                .metadata(new HashMap<>())
                .build();
    }

    /**
     * Performs server-side validation for a list of incoming {@link Application} objects.
     * <p>
     * Validates account, user, user profile, authorization, uniqueness of application name/identifier, and timezone.
     * Throws {@link ServerException} if any validation fails.
     *
     * @param utilityBean A {@link UtilityBean} containing the list of raw {@link Application} objects,
     *                    request parameters (including account identifier), and metadata (including userId).
     * @return A {@link UtilityBean} with validated {@link ApplicationBean} list, updated metadata, and request parameters.
     * @throws ServerException if validation fails for account, user, authorization, uniqueness, or timezone.
     */
    @LogExecutionAnnotation
    public UtilityBean<List<ApplicationBean>> serverValidation(UtilityBean<List<Application>> utilityBean) throws ServerException {
        log.debug("[serverValidation] Start - utilityBean: {}", utilityBean);
        List<Application> applications = utilityBean.getPojoObject();
        String userId = String.valueOf(utilityBean.getMetadata().get(Constants.USER_ID_KEY));
        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
        log.debug("[serverValidation] userId: {}, accountIdentifier: {}", userId, accountIdentifier);
        Account account = serverValidationUtils.accountValidation(accountIdentifier);

        int accountId = account.getId();
        UserInfoBean userInfo = fetchUserInfo(userId);
        log.debug("[serverValidation] userInfo: {}", userInfo);
        UserProfileBean userProfile = fetchUserProfile(userInfo);
        log.debug("[serverValidation] userProfile: {}", userProfile);

        validateUserAuthorization(userId, userProfile);
        log.info("[serverValidation] User [{}] authorized to create applications", userId);

        List<ApplicationBean> validatedApplications = new ArrayList<>();
        TimezoneBean timezone = null;

        for (Application app : applications) {
            log.debug("[ServerValidation] Validating application: name='{}', identifier='{}'", app.getName(), app.getIdentifier());
            if (controllerDao.existsByIdentifier(app.getIdentifier())) {
                log.error("[ServerValidation] Application with identifier [{}] already exists", app.getIdentifier());
                throw new ServerException("Application with identifier already exists");
            }
            try {
                if (controllerDao.existsByName(app.getName())) {
                    log.error("[ServerValidation] Application with name [{}] already exists", app.getName());
                    throw new ServerException("Application with name already exists");
                }
            } catch (HealControlCenterException e) {
                throw new ServerException(e.getMessage());
            }

            try {
                timezone = masterDataDao.getTimeZoneWithId(app.getTimezoneId());
                log.debug("[ServerValidation] Fetched timezone [{}] for application [{}]", timezone != null ? timezone.getId() : null, app.getName());
            } catch (HealControlCenterException e) {
                log.error("[ServerValidation] Invalid timezone [{}] for application [{}]", app.getTimezoneId(), app.getName());
                throw new ServerException(e.getMessage());
            }

            ApplicationBean bean = ApplicationBean.builder()
                    .name(app.getName().trim())
                    .identifier(app.getIdentifier())
                    .accountId(accountId)
                    .environment(app.getEnvironment())
                    .severity(app.getSeverity())
                    .linkedEnvironment(app.getLinkedEnvironment())
                    .tags(app.getTags())
                    .userId(userId)
                    .build();

            validatedApplications.add(bean);
            log.info("[ServerValidation] Application [{}] validated and added to the list for processing.", app.getName());
        }

        Map<String, Object> metadata = new HashMap<>();
        metadata.put(Constants.USER_ID_KEY, userId);
        metadata.put(Constants.TIME_ZONE_TAG, timezone);

        log.info("[ServerValidation] All applications validated successfully for userId: {}", userId);
        return UtilityBean.<List<ApplicationBean>>builder()
                .pojoObject(validatedApplications)
                .requestParams(utilityBean.getRequestParams())
                .metadata(metadata)
                .build();
    }

    /**
     * Processes a list of {@link ApplicationBean} objects by inserting them as controllers, handling tags, anomaly configs,
     * notification preferences, percentiles, and updating Redis cache. Returns a list of created application IDs.
     * Throws {@link DataProcessingException} if any step fails.
     *
     * @param utilityBean A utility wrapper containing the application list, metadata (userId, timezone), and request parameters.
     * @return A list of {@link IdPojo} representing the successfully created applications.
     * @throws DataProcessingException if any part of the process (insertion, configuration, or Redis update) fails.
     */
    @LogExecutionAnnotation
    @Transactional(rollbackFor = {Exception.class, RuntimeException.class})
    public List<IdPojo> process(UtilityBean<List<ApplicationBean>> utilityBean) throws DataProcessingException {
        log.debug("[process] Start - utilityBean: {}", utilityBean);
        String createdTime = dateTimeUtil.getCurrentTimestampInGMT().toString();
        String userId = utilityBean.getMetadata().get(Constants.USER_ID_KEY).toString();
        TimezoneBean timezone = (TimezoneBean) utilityBean.getMetadata().get(Constants.TIME_ZONE_TAG);
        List<ApplicationBean> applications = utilityBean.getPojoObject();
        List<IdPojo> idPojos = new ArrayList<>();

        for (ApplicationBean application : applications) {
            log.info("[process] Processing application [{}] (identifier: {})", application.getName(), application.getIdentifier());
            int applicationId;
            try {
                applicationId = insertController(application, userId, createdTime);
            } catch (HealControlCenterException e) {
                throw new DataProcessingException(e.getMessage());
            }
            log.debug("[process] Inserted controller for application [{}] with id [{}]", application.getName(), applicationId);

            if (application.getLinkedEnvironment() != null) {
                log.debug("[process] Handling linked environment for application [{}]", application.getName());
                handleLinkedApplications(application, userId, createdTime);
            }

            // Insert application tags
            if (timezone != null) {
                try {
                    int tagId = tagsDao.getTagDetailsByName(Constants.TIME_ZONE_TAG).getId();
                    TagMappingDetails timezoneTag = TagMappingDetails.builder()
                            .tagId(tagId)
                            .tagKey(String.valueOf(timezone.getId()))
                            .tagValue(String.valueOf(timezone.getOffset()))
                            .objectId(applicationId)
                            .objectRefTable(Constants.CONTROLLER)
                            .accountId(application.getAccountId())
                            .createdTime(createdTime)
                            .updatedTime(createdTime)
                            .userDetailsId(userId)
                            .build();
                    tagsDao.addTagMappingDetails(timezoneTag);
                    log.info("[process] Timezone tag added for [{}]", application.getName());
                } catch (Exception e) {
                    log.error("[process] Timezone mapping to application [{}] failed: {}", application.getName(), e.getMessage(), e);
                    throw new DataProcessingException("Timezone mapping to application failed: " + e.getMessage());
                }
            }
            log.info("[process] Application [{}] with ID [{}] inserted successfully", application.getName(), applicationId);

            // Fetch account anomaly configuration
            int closingWindow = 0;
            int maxDataBreaks = 0;
            try {
                log.info("[process] Fetching account anomaly config for accountId [{}]", application.getAccountId());
                AnomalyConfiguration config = accountsDao.getAccountAnomalyConfiguration(application.getAccountId());
                if (config != null) {
                    closingWindow = config.getClosingWindow();
                    maxDataBreaks = config.getMaxDataBreaks();
                    log.info("[process] Fetched account anomaly config: closingWindow={}, maxDataBreaks={}", closingWindow, maxDataBreaks);
                } else {
                    log.warn("[process] No account anomaly config found for accountId [{}]", application.getAccountId());
                }
            } catch (Exception e) {
                log.warn("[process] Could not fetch account anomaly config for accountId {}: {}", application.getAccountId(), e.getMessage());
            }

            // Anomaly configurations
            if (application.getSeverity() != null) {
                boolean low = false, medium = false, high = false;
                log.info("[process] Processing severity configuration for application [{}]", application.getName());
                for (var severityType : application.getSeverity()) {
                    log.debug("[process] Severity type: {} with status {}", severityType.getType(), severityType.getStatus());
                    if ("low".equalsIgnoreCase(severityType.getType())) {
                        low = severityType.getStatus() == 1;
                    } else if ("medium".equalsIgnoreCase(severityType.getType())) {
                        medium = severityType.getStatus() == 1;
                    } else if ("high".equalsIgnoreCase(severityType.getType())) {
                        high = severityType.getStatus() == 1;
                    }
                }
                ApplicationAnomalyConfiguration anomalyConfig = ApplicationAnomalyConfiguration.builder()
                        .accountId(application.getAccountId())
                        .applicationId(applicationId)
                        .userDetailsId(application.getUserId())
                        .createdTime(createdTime)
                        .updatedTime(createdTime)
                        .lowEnable(low)
                        .mediumEnable(medium)
                        .highEnable(high)
                        .closingWindow(closingWindow)
                        .maxDataBreaks(maxDataBreaks)
                        .build();
                try {
                    controllerDao.insertApplicationAnomalyConfigurations(anomalyConfig);
                    log.info("[process] Inserted anomaly configuration for application [{}]", application.getName());
                } catch (HealControlCenterException e) {
                    log.error("[process][Error] Failed to insert anomaly configuration for application [{}]: {}", application.getName(), e.getMessage(), e);
                    throw new DataProcessingException(e.getMessage());
                }
            }

            //Notification preferences and percentiles
            try {
                applicationNotifAndPercentileDao.addDefaultNotificationPreferences(
                        generateDefaultNotificationList(applicationId, application.getAccountId(), application.getUserId()));
                log.info("[process] Added default notification preferences for application [{}]", application.getName());
            } catch (Exception e) {
                log.error("[process][Error] Adding default notification preferences for application [{}] and accountId [{}]: {}", application.getIdentifier(), application.getAccountId(), e.getMessage(), e);
                throw new DataProcessingException(e.getMessage());
            }

            // Percentiles
            try {
                applicationNotifAndPercentileDao.addApplicationPercentiles(
                        createApplicationPercentiles(applicationId, application.getAccountId(), application.getUserId()));
                log.info("[process] Added application percentiles for application [{}]", application.getName());
            } catch (HealControlCenterException e) {
                log.error("[process][Error] Adding application percentiles for application [{}] and accountId [{}]: {}", application.getIdentifier(), application.getAccountId(), e.getMessage(), e);
                throw new DataProcessingException(e.getMessage());
            }

            idPojos.add(IdPojo.builder()
                    .id(applicationId)
                    .name(application.getName())
                    .identifier(application.getIdentifier())
                    .build());

            log.info("[process] Application [{}] processed and added to result list.", application.getName());
        }

        log.debug("[process] Updating Redis cache with new applications");
        updateRedisCache(utilityBean, applications, idPojos);
        log.info("[process] All applications processed and Redis cache updated");
        return idPojos;
    }

    /**
     * Fetches user information for the given user ID.
     *
     * @param userId The ID of the user whose details are to be fetched.
     * @return {@link UserInfoBean} containing user details.
     * @throws ServerException if user details cannot be retrieved from the database.
     */
    private UserInfoBean fetchUserInfo(String userId) throws ServerException {
        try {
            return userDao.getUserDetails(userId);
        } catch (HealControlCenterException e) {
            log.error("[ServerValidation] Error fetching user details for userId [{}]: {}", userId, e.getMessage());
            throw new ServerException(e.getMessage());
        }
    }

    /**
     * Fetches the user profile details based on the profile ID present in the given user information.
     *
     * @param userInfo {@link UserInfoBean} object containing the user's information.
     * @return {@link UserProfileBean} containing profile details of the user.
     * @throws ServerException if profile details cannot be retrieved from the database.
     */
    private UserProfileBean fetchUserProfile(UserInfoBean userInfo) throws ServerException {
        try {
            return userDao.getUserProfile(userInfo.getProfileId());
        } catch (HealControlCenterException e) {
            log.error("[ServerValidation] Error fetching user profile for profileId [{}]: {}", userInfo.getProfileId(), e.getMessage());
            throw new ServerException(e.getMessage());
        }
    }

    /**
     * Validates whether the user is authorized to create an application.
     * Only users with the profiles "SUPER_ADMIN" or "HEAL_ADMIN" are allowed.
     *
     * @param userId      The ID of the user performing the action.
     * @param userProfile The {@link UserProfileBean} containing the profile info of the user.
     * @throws ServerException if the user is not authorized to create applications.
     */
    private void validateUserAuthorization(String userId, UserProfileBean userProfile) throws ServerException {
        String profileName = userProfile.getUserProfileName();
        if (!Constants.SUPER_ADMIN.equalsIgnoreCase(profileName) && !Constants.HEAL_ADMIN.equalsIgnoreCase(profileName)) {
            log.error("[ServerValidation] User [{}] not allowed to create application", userId);
            throw new ServerException(UIMessages.USER_NOT_ALLOWED_CREATE_APP + " " + userId);
        }
    }

    /**
     * Inserts a new controller record into the database based on the given application data.
     *
     * @param applicationBean  The application bean containing controller-related metadata.
     * @param userId           The user ID performing the operation.
     * @param createdTimestamp Timestamp for creation and update.
     * @return The generated controller ID.
     * @throws DataProcessingException if controller insertion fails.
     */
    public int insertController(ApplicationBean applicationBean, String userId, String createdTimestamp) throws HealControlCenterException {
        log.debug("[insertController] Start - applicationBean: {}, userId: {}, createdTimestamp: {}", applicationBean, userId, createdTimestamp);
        String controllerName = applicationBean.getName() + "_" + applicationBean.getEnvironment().toLowerCase();
        String controllerIdentifier = applicationBean.getIdentifier() + "_" + applicationBean.getEnvironment().toLowerCase();

        ControllerBean controllerBean = new ControllerBean();
        controllerBean.setName(controllerName);
        controllerBean.setIdentifier(controllerIdentifier);
        controllerBean.setAccountId(applicationBean.getAccountId());
        controllerBean.setLastModifiedBy(userId);
        controllerBean.setCreatedTime(createdTimestamp);
        controllerBean.setUpdatedTime(createdTimestamp);
        controllerBean.setControllerTypeId(191); // 191 = Application
        controllerBean.setStatus(1);
        controllerBean.setPluginSuppressionInterval(0);
        controllerBean.setPluginWhitelisted(false);
        controllerBean.setEnvironment(applicationBean.getEnvironment().toLowerCase());

        log.info("[insertController] Attempting to insert controller: name={}, identifier={}, accountId={}, userId={}, createdTime={}, updatedTime={}, typeId={}, environment={}",
                controllerBean.getName(), controllerBean.getIdentifier(), controllerBean.getAccountId(), controllerBean.getLastModifiedBy(),
                controllerBean.getCreatedTime(), controllerBean.getUpdatedTime(), controllerBean.getControllerTypeId(), controllerBean.getEnvironment());

        try {
            int id = controllerDao.insertController(controllerBean);
            log.info("[insertController] Controller inserted successfully with id: {}", id);
            return id;
        } catch (HealControlCenterException ex) {
            log.error("[insertController] Failed to insert controller. Reason: {}", ex.getMessage(), ex);
            throw new HealControlCenterException(ex.getMessage());
        }
    }

    /**
     * Creates alias mapping between the DC and DR application controllers if the application
     * has a linked environment.
     *
     * @param applicationBean The application data bean.
     * @param userId          The user ID performing the operation.
     * @param createdTime     Timestamp for creation and update.
     */
    private void handleLinkedApplications(ApplicationBean applicationBean, String userId, String createdTime) {
        log.debug("[handleLinkedApplications] Start - applicationBean: {}, userId: {}, createdTime: {}", applicationBean, userId, createdTime);
        String environment = applicationBean.getEnvironment();
        String applicationName = applicationBean.getName();
        String linkedApplicationName = applicationBean.getLinkedEnvironment().getMappedToApplication();

        String linkedAppIdentifier = null;
        String dcAppIdentifier = null;
        String drAppIdentifier = null;

        // Resolve identifier for linked application, if provided
        if (linkedApplicationName != null && !linkedApplicationName.trim().isEmpty()) {
            try {
                ControllerBean linkedApp = controllerDao.getApplicationIdByName(linkedApplicationName);
                if (linkedApp != null) {
                    linkedAppIdentifier = linkedApp.getIdentifier();
                    log.debug("[handleLinkedApplications] Found linked application [{}] with identifier [{}]", linkedApplicationName, linkedAppIdentifier);
                } else {
                    log.warn("[handleLinkedApplications] No application found with name [{}] for alias mapping", linkedApplicationName);
                }
            } catch (Exception ex) {
                log.error("[handleLinkedApplications] Error fetching linked application by name [{}]: {}", linkedApplicationName, ex.getMessage(), ex);
            }
        }

        try {
            // Set DC/DR identifiers based on current environment
            String currentAppIdentifier = applicationBean.getIdentifier() + "_" + environment.toLowerCase();
            if ("dr".equalsIgnoreCase(environment)) {
                drAppIdentifier = currentAppIdentifier;
                dcAppIdentifier = linkedAppIdentifier;
            } else if ("dc".equalsIgnoreCase(environment)) {
                dcAppIdentifier = currentAppIdentifier;
                drAppIdentifier = linkedAppIdentifier;
            } else {
                log.warn("[handleLinkedApplications] Invalid environment [{}]. Alias creation skipped.", environment);
                return;
            }

            ApplicationAliases aliasRecord = ApplicationAliases.builder()
                    .commonName(applicationName)
                    .status(1)
                    .dcApplicationIdentifier(dcAppIdentifier)
                    .drApplicationIdentifier(drAppIdentifier)
                    .userDetailsId(userId)
                    .createdTime(createdTime)
                    .updatedTime(createdTime)
                    .build();

            controllerDao.insertApplicationAlias(aliasRecord);
            log.info("[handleLinkedApplications] Inserted application_alias for [{}] with DC/DR identifiers: [{}/{}]", applicationName, dcAppIdentifier, drAppIdentifier);

        } catch (Exception ex) {
            log.error("[handleLinkedApplications] Failed to handle linked applications for [{}]: {}", applicationName, ex.getMessage(), ex);
        }
    }

    /**
     * Generates the default set of notification preferences for a given application.
     *
     * @param id        The application ID.
     * @param accountId The account ID the application belongs to.
     * @param userId    The user creating the preferences.
     * @return List of default notification preference objects.
     * @throws HealControlCenterException if master data lookup fails.
     */
    public List<DefaultNotificationPreferences> generateDefaultNotificationList(int id, int accountId, String userId) throws HealControlCenterException {
        List<SignalTypeSeverityMapping> preferencesType = getDefaultPreferencesType();

        int defaultNotificationTypeId = masterDataDao.getViewTypesFromMstTypeAndSubTypeName(Constants.NOTIFICATION_TYPE_LITERAL, Constants.IMMEDIATELY).getSubTypeId();

        return preferencesType.parallelStream().map(m -> {
            DefaultNotificationPreferences defaultNotificationPreferences = new DefaultNotificationPreferences();
            defaultNotificationPreferences.setApplicationId(id);
            defaultNotificationPreferences.setNotificationTypeId(defaultNotificationTypeId);
            defaultNotificationPreferences.setSignalTypeId(m.getSignalTypeId());
            defaultNotificationPreferences.setSignalSeverityId(m.getSignalSeverityId());
            defaultNotificationPreferences.setAccountId(accountId);
            defaultNotificationPreferences.setCreatedTime(dateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
            defaultNotificationPreferences.setUpdatedTime(dateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
            defaultNotificationPreferences.setUserDetailsId(userId);

            return defaultNotificationPreferences;
        }).collect(Collectors.toList());
    }

    /**
     * Fetches the predefined combinations of signal types and severity levels
     * used for initializing notification preferences.
     *
     * @return List of signal type and severity mappings.
     * @throws HealControlCenterException if lookup from master data fails.
     */
    private List<SignalTypeSeverityMapping> getDefaultPreferencesType() throws HealControlCenterException {
        List<SignalTypeSeverityMapping> signalTypeSeverityMappingList = new ArrayList<>();

        int problemTypeId = masterDataDao.getViewTypesFromMstTypeAndSubTypeName(Constants.SIGNAL_TYPE_LITERAL, Constants.PROBLEM).getSubTypeId();
        int earlyWarningTypeId = masterDataDao.getViewTypesFromMstTypeAndSubTypeName(Constants.SIGNAL_TYPE_LITERAL, Constants.EARLY_WARNING).getSubTypeId();
        int infoTypeId = masterDataDao.getViewTypesFromMstTypeAndSubTypeName(Constants.SIGNAL_TYPE_LITERAL, Constants.INFO).getSubTypeId();
        int batchTypeId = masterDataDao.getViewTypesFromMstTypeAndSubTypeName(Constants.SIGNAL_TYPE_LITERAL, Constants.BATCH).getSubTypeId();

        int severeTypeId = masterDataDao.getViewTypesFromMstTypeAndSubTypeName(Constants.SIGNAL_SEVERITY_TYPE_LITERAL, Constants.SEVERE).getSubTypeId();
        int defaultTypeId = masterDataDao.getViewTypesFromMstTypeAndSubTypeName(Constants.SIGNAL_SEVERITY_TYPE_LITERAL, Constants.DEFAULT).getSubTypeId();

        signalTypeSeverityMappingList.add(new SignalTypeSeverityMapping(problemTypeId, severeTypeId));
        signalTypeSeverityMappingList.add(new SignalTypeSeverityMapping(problemTypeId, defaultTypeId));
        signalTypeSeverityMappingList.add(new SignalTypeSeverityMapping(earlyWarningTypeId, severeTypeId));
        signalTypeSeverityMappingList.add(new SignalTypeSeverityMapping(earlyWarningTypeId, defaultTypeId));
        signalTypeSeverityMappingList.add(new SignalTypeSeverityMapping(infoTypeId, severeTypeId));
        signalTypeSeverityMappingList.add(new SignalTypeSeverityMapping(infoTypeId, defaultTypeId));
        signalTypeSeverityMappingList.add(new SignalTypeSeverityMapping(batchTypeId, severeTypeId));
        signalTypeSeverityMappingList.add(new SignalTypeSeverityMapping(batchTypeId, defaultTypeId));

        return signalTypeSeverityMappingList;
    }

    /**
     * Creates default percentile configurations for the given application.
     *
     * @param applicationId The application ID.
     * @param accountId     The associated account ID.
     * @param userId        The user performing the operation.
     * @return List of application percentile configuration beans.
     */
    public List<ApplicationPercentilesBean> createApplicationPercentiles(int applicationId, int accountId, String userId) {
        String defaultPercentiles = ConfProperties.getString(Constants.APPLICATION_PERCENTILES_DEFAULT_CONFIGURATION, Constants.APPLICATION_PERCENTILES_DEFAULT_VALUES);
        String[] percentile = defaultPercentiles.split(",");

        return Arrays.stream(percentile)
                .map(i -> ApplicationPercentilesBean.builder()
                        .applicationId(applicationId)
                        .accountId(accountId)
                        .displayName(i)
                        .percentileValue(Integer.parseInt(i))
                        .userDetailsId(userId)
                        .createdTime(dateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                        .updatedTime(dateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                        .build())
                .collect(Collectors.toList());
    }

    /**
     * Updates the Redis cache with the newly created applications.
     *
     * @param utilityBean  The input utility bean containing request metadata and parameters.
     * @param applications The list of processed application beans.
     * @param idPojos      The list of processed application identifiers.
     */
    private void updateRedisCache(UtilityBean<List<ApplicationBean>> utilityBean,
                                  List<ApplicationBean> applications,
                                  List<IdPojo> idPojos) {

        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
        try {
            log.info("[updateRedisCache] Updating Redis with new applications for accountIdentifier: {}", accountIdentifier);

            List<com.heal.configuration.pojos.Application> redisApplications =
                    Optional.ofNullable(applicationRepo.getApplicationsForAccount(accountIdentifier))
                            .orElse(new ArrayList<>());

            List<com.heal.configuration.pojos.ViewTypes> viewTypes = masterDataRepo.getTypes();
            com.heal.configuration.pojos.ViewTypes applicationType = viewTypes.parallelStream()
                    .filter(type -> Constants.CONTROLLER_TYPE_NAME_DEFAULT.equals(type.getTypeName()) &&
                            Constants.APPLICATION_CONTROLLER_TYPE.equals(type.getSubTypeName()))
                    .findAny()
                    .orElseThrow(() -> new HealControlCenterException("Type details unavailable for type name "
                            + Constants.CONTROLLER_TYPE_NAME_DEFAULT));

            String kpiSuffix = ConfProperties.getString(Constants.PERCENTILE_KPIS_IDENTIFIER_SUFFIX,
                    Constants.PERCENTILE_KPIS_IDENTIFIER_SUFFIX_DEFAULT);
            String[] defaultKpis = ConfProperties.getString(
                    Constants.APPLICATION_PERCENTILES_DEFAULT_CONFIGURATION,
                    Constants.APPLICATION_PERCENTILES_DEFAULT_VALUES).split(",");

            Map<String, Set<Double>> defaultPercentiles = buildPercentileMap(defaultKpis, kpiSuffix);

            for (IdPojo idPojo : idPojos) {
                applications.stream()
                        .filter(app -> app.getIdentifier().equals(idPojo.getIdentifier()))
                        .findFirst()
                        .ifPresent(app -> {
                            com.heal.configuration.pojos.Application redisApp =
                                    com.heal.configuration.pojos.Application.builder()
                                            .id(idPojo.getId())
                                            .name(app.getName() + "_" + app.getEnvironment().toLowerCase())
                                            .identifier(app.getIdentifier() + "_" + app.getEnvironment().toLowerCase())
                                            .tags(Optional.ofNullable(app.getTags()).orElse(new ArrayList<>())
                                                    .stream()
                                                    .filter(Objects::nonNull)
                                                    .map(tag -> com.heal.configuration.pojos.Tags.builder()
                                                            .key(tag.getKey())
                                                            .type(tag.getType())
                                                            .value(tag.getValue())
                                                            .build())
                                                    .collect(Collectors.toList()))
                                            .createdTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                                            .updatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                                            .lastModifiedBy(app.getUserId())
                                            .status(1)
                                            .percentiles(defaultPercentiles)
                                            .typeId(applicationType.getSubTypeId())
                                            .build();

                            redisApplications.add(redisApp);
                                log.debug("[updateRedisCache] Updating Redis for application: {}", redisApp.getIdentifier());
                                applicationRepo.updateApplication(accountIdentifier, redisApp);
                                log.info("[updateRedisCache] Redis updated for application: {}", redisApp.getIdentifier());
                        });
            }

            applicationRepo.updateApplicationDetailsForAccount(accountIdentifier, redisApplications);
            log.info("[updateRedisCache] Redis update successful for accountIdentifier: {}", accountIdentifier);

        } catch (Exception e) {
            log.error("[updateRedisCache][Error] Updating Redis with new applications for accountIdentifier: {}: {}", accountIdentifier, e.getMessage(), e);
            //throw new HealControlCenterException("Error occurred while updating Redis: " + e.getMessage());
        }
    }

    /**
     * Builds a map of KPI base identifiers to their corresponding set of percentile values.
     * <p>
     * Each input string in {@code kpiIdentifiers} is expected to follow the format:
     * {@code kpi_name_percentile_suffix} (e.g., {@code response_time_95_percentile}).
     * The method extracts the base KPI name and maps it to a set of percentiles.
     * Entries with malformed formats or non-numeric percentile values are skipped.
     *
     * @param kpiIdentifiers An array of KPI identifier strings with percentile suffixes.
     * @param suffix         The expected suffix (e.g., "percentile") to filter relevant identifiers.
     * @return A map where the key is the base KPI name and the value is a set of Double percentiles.
     */
    private Map<String, Set<Double>> buildPercentileMap(String[] kpiIdentifiers, String suffix) {
        Map<String, Set<Double>> percentilesMap = new HashMap<>();

        for (String identifier : kpiIdentifiers) {
            String[] parts = identifier.trim().split("_");

            if (parts.length >= 2 && suffix.equals(parts[parts.length - 1])) {
                try {
                    Double percentile = Double.valueOf(parts[parts.length - 2]);
                    String baseKey = String.join("_", Arrays.copyOf(parts, parts.length - 2));

                    percentilesMap.computeIfAbsent(baseKey, k -> new HashSet<>()).add(percentile);
                } catch (NumberFormatException e) {
                    log.warn("Skipping malformed percentile identifier: {}", identifier);
                }
            }
        }
        return percentilesMap;
    }
}
