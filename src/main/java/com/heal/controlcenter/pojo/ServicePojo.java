package com.heal.controlcenter.pojo;


import com.appnomic.appsone.common.util.StringUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.heal.controlcenter.util.UIMessages;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;


import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Slf4j
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ServicePojo {
    private int isEntryPointService;
    private String name;
    private String identifier;
    private String layer;
    private List<String> appIdentifiers;
    private String type;
    private String environment;

    @JsonProperty("linkedEnvironment")
    private LinkedEnvironmentPojo linkedEnvironment;
    private ServiceGroupPojo serviceGroup;

    private Map<String, String> error = new HashMap<>();

    public Map<String, String> validate() {
        if (StringUtils.isEmpty(name) || name.length() > 128) {
            log.error(UIMessages.INVALID_SERVICE_NAME);
            error.put("Service name", UIMessages.INVALID_SERVICE_NAME);
        }

        if (!StringUtils.isEmpty(identifier) && identifier.length() > 128) {
            log.error(UIMessages.INVALID_SERVICE_IDENTIFIER);
            // FIX: Corrected the error key from "Service name" to "identifier"
            error.put("identifier", UIMessages.INVALID_SERVICE_IDENTIFIER);
        }

        if (StringUtils.isEmpty(type)) {
            log.warn(UIMessages.INVALID_SERVICE_TYPE);
        }

        if(StringUtils.isEmpty(type)) {
            if(StringUtils.isEmpty(layer)) {
                log.error("Service layer is invalid. Reason: It is either NULL or empty");
                error.put("layer", "It is either NULL or empty");
            }

            if(appIdentifiers == null || appIdentifiers.isEmpty()) {
                log.error("No applications mapped to service [{}]", name);
                error.put("appIdentifiers", "No mapped applications");
            }
        }
        // FIX: Always return the error map to avoid NullPointerExceptions
        return error;
    }



    @Override
    public int hashCode() {
        return 1;
    }

    @Override
    public boolean equals(Object obj) {
        if (!(obj instanceof ServicePojo))
            return false;

        ServicePojo svc = (ServicePojo) obj;
        return (svc.name != null && svc.name.equals(name)) || (svc.identifier != null && svc.identifier.equals(identifier));
    }

    public boolean validateForUpdate() {
        if (this.getName() != null && (this.getName().trim().length() == 0 || this.getName().length() > 128)) {
            log.error(UIMessages.INVALID_SERVICE_NAME);
            return false;
        }
        return true;
    }
}
