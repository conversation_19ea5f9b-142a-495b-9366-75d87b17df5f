package com.heal.controlcenter.dao.redis;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.configuration.pojos.CompInstKpiEntity;
import com.heal.controlcenter.util.RedisUtilities;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;

@Repository
@Slf4j
public class InstanceRepo {

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private RedisUtilities redisUtilities;

    String ACCOUNTS_KEY = "/accounts";
    String INSTANCES_KEY = "/instances";
    String KPI_KEY = "/kpis";
    String ACCOUNTS_HASH = "ACCOUNTS";
    String INSTANCES_HASH = "_INSTANCES_";
    String KPIS_HASH = "_KPIS";

    public void updateKpiDetails(String accountIdentifier, String instanceIdentifier, List<CompInstKpiEntity> bean) {
        log.info("Updating kpi details for accountId: {}, instance: {}", accountIdentifier, instanceIdentifier);
        try {
            redisUtilities.updateKey(ACCOUNTS_KEY + "/" + accountIdentifier + INSTANCES_KEY + "/" + instanceIdentifier + KPI_KEY,
                    ACCOUNTS_HASH + "_" + accountIdentifier + INSTANCES_HASH + instanceIdentifier + KPIS_HASH, bean);
        } catch (Exception e) {
            log.error("Error occurred while updating kpi details for accountId:{}, instance:{}", accountIdentifier, instanceIdentifier, e);
        }
    }

    public void updateKpiDetailsForKpiIdentifier(String accountIdentifier, String instanceIdentifier, CompInstKpiEntity kpiEntity) {
        log.info("Updating kpi details for accountId: {}, instance: {}, kpiId: {}", accountIdentifier, instanceIdentifier, kpiEntity.getId());
        try {
            redisUtilities.updateKey(ACCOUNTS_KEY + "/" + accountIdentifier + INSTANCES_KEY + "/" + instanceIdentifier + KPI_KEY + "/" + kpiEntity.getIdentifier(),
                    ACCOUNTS_HASH + "_" + accountIdentifier + INSTANCES_HASH + instanceIdentifier + KPIS_HASH + "_" + kpiEntity.getIdentifier(), kpiEntity);
        } catch (Exception e) {
            log.error("Error occurred while updating kpi details for accountId:{}, instance:{}", accountIdentifier, instanceIdentifier, e);
        }
    }

    public void updateKpiDetailsForKpiId(String accountIdentifier, String instanceIdentifier, CompInstKpiEntity kpiEntity) {
        log.info("Updating kpi details for accountId: {}, instance: {}, kpiIdentifier: {}", accountIdentifier, instanceIdentifier, kpiEntity.getId());
        try {
            redisUtilities.updateKey(ACCOUNTS_KEY + "/" + accountIdentifier + INSTANCES_KEY + "/" + instanceIdentifier + KPI_KEY + "/" + kpiEntity.getId(),
                    ACCOUNTS_HASH + "_" + accountIdentifier + INSTANCES_HASH + instanceIdentifier + KPIS_HASH + "_" + kpiEntity.getId(), kpiEntity);
        } catch (Exception e) {
            log.error("Error occurred while updating kpi details for accountId:{}, instance:{}", accountIdentifier, instanceIdentifier, e);
        }
    }

    public List<CompInstKpiEntity> getInstanceWiseKpis(String accountIdentifier, String instanceIdentifier) {
        log.info("Fetching instance wise kpi details for accountId: {}, instance: {}", accountIdentifier, instanceIdentifier);
        try {
            String instanceKpiObject = redisUtilities.getKey(ACCOUNTS_KEY + "/" + accountIdentifier + INSTANCES_KEY + "/" + instanceIdentifier + KPI_KEY,
                    ACCOUNTS_HASH + "_" + accountIdentifier + INSTANCES_HASH + instanceIdentifier + KPIS_HASH);
            if (instanceKpiObject == null) {
                log.debug("Instance Kpi details not found for account: [{}]", accountIdentifier);
                return Collections.emptyList();
            }
            return objectMapper.readValue(instanceKpiObject, new TypeReference<List<CompInstKpiEntity>>() {
            });
        } catch (Exception e) {
            log.error("Error occurred while getting  instance wise kpi details for account [{}]. Details: ", accountIdentifier, e);
            return Collections.emptyList();
        }
    }
}
