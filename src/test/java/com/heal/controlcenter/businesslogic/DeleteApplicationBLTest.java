package com.heal.controlcenter.businesslogic;

import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.pojo.Application;
import com.heal.controlcenter.util.CommonUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(MockitoExtension.class)
public class DeleteApplicationBLTest {

    @Autowired
    @InjectMocks
    DeleteApplicationsBL applications;

    @Mock
    CommonUtils commonUtils;

    Application application = new Application();

    @BeforeEach
    void setUp() {
    }

    @AfterEach
    void tearDown() {
    }

    @Test
    void deleteClientValidations_InvalidAuthToken()  {
        String[] appIdentifiers = new String[3];
        String[] params = new String[2];
        params[0] = "";
        params[1] = "";
        String expectedMessage = "ClientException : Invalid authorization token";
        ClientException requestException = assertThrows(ClientException.class, () ->
                applications.clientValidation(appIdentifiers,params));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void deleteClientValidations_InvalidIdentifier()  {
        String[] appIdentifiers = new String[3];
        String[] params = new String[2];
        params[0] = "fskjejiojjiw";
        params[1] = "";
        String expectedMessage = "ClientException : Invalid account identifier";
        ClientException requestException = assertThrows(ClientException.class, () ->
                applications.clientValidation(appIdentifiers,params));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void deleteClientValidations_InvalidAppIdentifier()  {
        String[] appIdentifiers = new String[3];
        String[] params = new String[2];
        params[0] = "fskjejiojjiw";
        params[1] = "hadsjkfhjka";
        String expectedMessage = "ClientException : appIdentifier should not be empty or null in the query parameter.";
        ClientException requestException = assertThrows(ClientException.class, () ->
                applications.clientValidation(appIdentifiers,params));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void deleteClientValidations_NullApp()  {
        String[] appIdentifiers = new String[3];
        appIdentifiers[0] = " abc," + " ," + " " ;
        String[] params = new String[2];
        params[0] = "fskjejiojjiw";
        params[1] = "hadsjkfhjka";
        String expectedMessage = "ClientException : Found empty/null Application(s) name in request parameter.";
        ClientException requestException = assertThrows(ClientException.class, () ->
                applications.clientValidation(appIdentifiers,params));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

  
}
