package com.heal.controlcenter.controller;

import com.heal.controlcenter.beans.ControllerBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.GetApplicationsBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.ApplicationPojo;
import com.heal.controlcenter.pojo.ErrorResponsePojo;
import com.heal.controlcenter.pojo.ResponsePojo;
import com.heal.controlcenter.util.JsonFileParser;
import groovy.util.logging.Slf4j;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@Configuration
@RestController
public class ApplicationController {
    @Autowired
    GetApplicationsBL getApplicationsBL;
    @Autowired
    JsonFileParser headersParser;

    @Operation(
            summary = "Retrieves applications for the specified account.",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Applications fetched successfully.",
                            content = @Content(schema = @Schema(implementation = ApplicationPojo.class))
                    ),
                    @ApiResponse(
                            responseCode = "500",
                            description = "Exception encountered while fetching applications.",
                            content = @Content(schema = @Schema(implementation = ErrorResponsePojo.class))
                    ),
                    @ApiResponse(
                            responseCode = "400",
                            description = "Error occurred while fetching applications.",
                            content = @Content(schema = @Schema(implementation = ErrorResponsePojo.class))
                    ),
                    @ApiResponse(
                            responseCode = "401",
                            description = "Unauthorized - Invalid or missing authentication token.",
                            content = @Content(schema = @Schema(implementation = ErrorResponsePojo.class))
                    )
            }
    )
    @RequestMapping(value = "accounts/{identifier}/applications", method = RequestMethod.GET)
    public ResponseEntity<ResponsePojo<List<ApplicationPojo>>> getApplications(
            @Parameter(
                    name = "Authorization",
                    description = "Bearer token for authentication",
                    required = true,
                    example = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            )
            @RequestHeader(value = "Authorization") String authorization,
            @Parameter(
                    name = "identifier",
                    description = "Unique identifier of the account",
                    required = true,
                    example = "account-001"
            )
            @PathVariable(value = "identifier") String accountIdentifier,
            @Parameter(
                    name = "clusterDataRequired",
                    description = "Whether to include cluster data in the response",
                    required = false,
                    example = "true"
            )
            @RequestParam(value = "clusterDataRequired", required = false, defaultValue = "true") String clusterDataRequired)
            throws ClientException, DataProcessingException, ServerException, ControlCenterException {

        UtilityBean<String> applicationBean = getApplicationsBL.clientValidation(null, authorization, accountIdentifier, clusterDataRequired);
        UtilityBean<List<ControllerBean>> controller = getApplicationsBL.serverValidation(applicationBean);
        List<ApplicationPojo> listOfApplications = getApplicationsBL.process(controller);

        ResponsePojo<List<ApplicationPojo>> responsePojo = new ResponsePojo<>("Applications fetching successfully",
                listOfApplications, HttpStatus.OK);

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
    }
}
