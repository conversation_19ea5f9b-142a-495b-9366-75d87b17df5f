
// In a configuration class, e.g., src/main/java/com/heal/controlcenter/config/HeaderConfig.java
package com.heal.controlcenter.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import java.util.HashMap;
import java.util.Map;

@Configuration
public class HeaderConfig {
    @Bean(name = "headerConfigurations")
    public Map<String, String> headerConfigurations() {
        Map<String, String> headers = new HashMap<>();
        // Add your header key-value pairs here
        headers.put("X-Frame-Options", "DENY");
        headers.put("X-Content-Type-Options", "nosniff");
        return headers;
    }
}