package com.heal.controlcenter.controller;

import com.heal.controlcenter.beans.InstallationAttributeBean;
import com.heal.controlcenter.businesslogic.InstallationAttributeBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.pojo.ResponsePojo;
import com.heal.controlcenter.swagger.ErrorResponseSchemas;
import com.heal.controlcenter.util.JsonFileParser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@Configuration
public class InstallationAttributesController {

    @Autowired
    InstallationAttributeBL installationAttributeBL;
    @Autowired
    JsonFileParser headersParser;

    @Operation(
            summary = "Retrieves installation attributes for the system.",
            description = "Returns installation attributes and configuration details. Authorization is optional for this endpoint.",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Installation attributes fetched successfully.",
                            content = @Content(schema = @Schema(implementation = InstallationAttributeBean.class))
                    ),
                    @ApiResponse(
                            responseCode = "400",
                            description = "Bad Request - Invalid input parameters",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ErrorResponseSchemas.ErrorResponse.class)
                            )
                    ),
                    @ApiResponse(
                            responseCode = "500",
                            description = "Internal Server Error",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ErrorResponseSchemas.ServerErrorResponse.class)
                            )
                    )
            }
    )
    @RequestMapping(value = "/installation-attributes", method = RequestMethod.GET)
    public ResponseEntity<Object> getInstallationAttributes(
            @Parameter(
                    name = "Authorization",
                    description = "Bearer token for authentication.",
                    required = true,
                    example = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            )
            @RequestHeader(value = "Authorization", required = true) String authorization) throws ClientException, DataProcessingException {
        installationAttributeBL.clientValidation(null, authorization);
        List<InstallationAttributeBean> listOfInstallationAttributes = installationAttributeBL.process("Installation attributes");
        ResponsePojo<List<InstallationAttributeBean>> responsePojo = new ResponsePojo<>("Installation details fetching successfully", listOfInstallationAttributes, HttpStatus.OK);

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
    }
}
