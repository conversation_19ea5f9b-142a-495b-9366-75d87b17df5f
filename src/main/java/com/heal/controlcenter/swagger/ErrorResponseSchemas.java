package com.heal.controlcenter.swagger;

import io.swagger.v3.oas.annotations.media.Schema;

public class ErrorResponseSchemas {

    @Schema(name = "ErrorResponse", description = "Standard error response format")
    public static class ErrorResponse {
        @Schema(description = "Error message", example = "Invalid account identifier format")
        public String message;
        
        @Schema(description = "Additional data (usually null for errors)", nullable = true, example = "null")
        public Object data;
        
        @Schema(description = "HTTP status", example = "BAD_REQUEST")
        public String status;
    }

    @Schema(name = "UnauthorizedResponse", description = "Unauthorized error response")
    public static class UnauthorizedResponse {
        @Schema(description = "Error message", example = "Invalid or expired authentication token")
        public String message;
        
        @Schema(description = "Additional data (usually null for errors)", nullable = true, example = "null")
        public Object data;
        
        @Schema(description = "HTTP status", example = "UNAUTHORIZED")
        public String status;
    }

    @Schema(name = "ServerErrorResponse", description = "Server error response")
    public static class ServerErrorResponse {
        @Schema(description = "Error message", example = "Internal server error occurred")
        public String message;
        
        @Schema(description = "Additional data (usually null for errors)", nullable = true, example = "null")
        public Object data;
        
        @Schema(description = "HTTP status", example = "INTERNAL_SERVER_ERROR")
        public String status;
    }
}