package com.heal.controlcenter.config;


import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.heal.controlcenter.util.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.CacheManager;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import java.util.concurrent.TimeUnit;

@Configuration
@Slf4j
public class CacheConfig {
    @Value("${redis.cache.mode:0}")
    int redisCacheMode;
    @Value("${opensearch.cache.mode:0}")
    int opensearchCacheMode;
    @Value("${percona.cache.mode:0}")
    int perconaCacheMode;
    @Value("${record.cache.stats:false}")
    boolean recordCacheStats;

    @Value("${opensearch.cache.max.size:50}")
    private int OPENSEARCH_CACHE_MAX_SIZE;
    @Value("${opensearch.cache.expire.interval.minutes:2}")
    private int OPENSEARCH_CACHE_EXPIRATION_TIME;

    @Value("${accounts_id_tenants.cache.max.size:1000}")
    private int ACCOUNTS_ID_TENANT_CACHE_MAX_SIZE;
    @Value("${accounts_id_tenants.cache.max.size:60}")
    private int ACCOUNTS_ID_TENANT_EXPIRATION_TIME;

    @Value("${heal_index_zones.cache.max.size:1000}")
    private int HEAL_INDEX_ZONE_CACHE_MAX_SIZE;
    @Value("${heal_index_zones.cache.expire.interval.minutes:60}")
    private int HEAL_INDEX_ZONE_EXPIRATION_TIME;

    @Value("${view_types.cache.max.size:1000}")
    private int VIEW_TYPE_CACHE_MAX_SIZE;
    @Value("${view_types.cache.expire.interval.minutes:60}")
    private int VIEW_TYPE_EXPIRATION_TIME;

    @Bean
    public CacheManager cacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();


        if (redisCacheMode == 0) {
            cacheManager.registerCustomCache(Constants.HEAL_INDEX_ZONES, createCacheConfig(Constants.HEAL_INDEX_ZONES, HEAL_INDEX_ZONE_CACHE_MAX_SIZE, HEAL_INDEX_ZONE_EXPIRATION_TIME));
            cacheManager.registerCustomCache(Constants.ACCOUNTS_ID_TENANTS, createCacheConfig(Constants.ACCOUNTS_ID_TENANTS, ACCOUNTS_ID_TENANT_CACHE_MAX_SIZE, ACCOUNTS_ID_TENANT_EXPIRATION_TIME));
        }

        if (perconaCacheMode == 0) {
            cacheManager.registerCustomCache(Constants.VIEW_TYPES, createCacheConfig(Constants.VIEW_TYPES, VIEW_TYPE_CACHE_MAX_SIZE, VIEW_TYPE_EXPIRATION_TIME));
        }

        if (opensearchCacheMode == 0) {
            cacheManager.registerCustomCache(Constants.OPENSEARCH_CACHE, createCacheConfig(Constants.OPENSEARCH_CACHE, OPENSEARCH_CACHE_MAX_SIZE, OPENSEARCH_CACHE_EXPIRATION_TIME));
        }

        cacheManager.setAllowNullValues(true);

        return cacheManager;
    }

    private Cache<Object, Object> createCacheConfig(String name, int maxSize, int expireTime) {
        Cache<Object, Object> cache;
        if (recordCacheStats) {
            cache = Caffeine.newBuilder()
                    .initialCapacity(maxSize / 10)
                    .maximumSize(maxSize)
                    .expireAfterWrite(expireTime, TimeUnit.MINUTES)
                    .recordStats()
                    .build();
        } else {
            cache = Caffeine.newBuilder()
                    .initialCapacity(maxSize / 10)
                    .maximumSize(maxSize)
                    .expireAfterWrite(expireTime, TimeUnit.MINUTES)
                    .build();
        }

        return cache;
    }

}
