package com.heal.controlcenter.businesslogic;

import com.heal.configuration.pojos.Account;
import com.heal.controlcenter.beans.ServiceBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.AccountServiceDao;
import com.heal.controlcenter.dao.mysql.ControllerDao;
import com.heal.controlcenter.dao.redis.ApplicationRepo;
import com.heal.controlcenter.dao.redis.ServiceRepo;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.ServiceDeleteRequestPojo;
import com.heal.controlcenter.util.ClientValidationUtils;
import com.heal.controlcenter.util.CommonUtils;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.ServerValidationUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DeleteAccountServiceBLTest {

    @InjectMocks
    private DeleteAccountServicesBL deleteAccountServiceBL;

    @Mock
    private ClientValidationUtils clientValidationUtils;
    @Mock
    private ServerValidationUtils serverValidationUtils;
    @Mock
    private AccountServiceDao accountServiceDao;
    @Mock
    private ControllerDao controllerDao;
    @Mock
    private ServiceRepo serviceRepo;
    @Mock
    private ApplicationRepo applicationRepo;

    private ServiceDeleteRequestPojo serviceDeleteRequestPojo;
    private Account account;
    private ServiceBean serviceBean;

    @BeforeEach
    void setUp() {
        serviceDeleteRequestPojo = new ServiceDeleteRequestPojo();
        serviceDeleteRequestPojo.setServiceIdentifiers(Collections.singletonList("service-123"));
        serviceDeleteRequestPojo.setHardDelete(false);

        account = new Account();
        account.setId(1);
        account.setIdentifier("test-account");
        account.setName("Test Account");

        serviceBean = ServiceBean.builder()
                .id(101)
                .identifier("service-123")
                .name("Test Service")
                .accountIdentifier("test-account")
                .accountId(1)
                .build();
    }

    @Test
    @DisplayName("clientValidation - Success")
    void clientValidation_Success() throws ClientException {
        doNothing().when(clientValidationUtils).accountIdentifierValidation(anyString());
        UtilityBean<ServiceDeleteRequestPojo> utilityBean = deleteAccountServiceBL.clientValidation(serviceDeleteRequestPojo, "test-account");

        assertNotNull(utilityBean);
        assertEquals("test-account", utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER));
        assertFalse((Boolean) utilityBean.getMetadata().get(Constants.HARD_DELETE));
        assertEquals(serviceDeleteRequestPojo, utilityBean.getPojoObject());
    }

    @Test
    @DisplayName("clientValidation - Empty Service Identifiers")
    void clientValidation_EmptyServiceIdentifiers() {
        serviceDeleteRequestPojo.setServiceIdentifiers(Collections.emptyList());
        assertThrows(ClientException.class, () -> deleteAccountServiceBL.clientValidation(serviceDeleteRequestPojo, "test-account"));
    }

    @Test
    @DisplayName("clientValidation - Null Service Identifiers")
    void clientValidation_NullServiceIdentifiers() {
        serviceDeleteRequestPojo.setServiceIdentifiers(null);
        assertThrows(ClientException.class, () -> deleteAccountServiceBL.clientValidation(serviceDeleteRequestPojo, "test-account"));
    }

    @Test
    @DisplayName("clientValidation - Null Request Body")
    void clientValidation_NullRequestBody() {
        assertThrows(ClientException.class, () -> deleteAccountServiceBL.clientValidation(null, "test-account"));
    }

    @Test
    @DisplayName("serverValidation - Success")
    void serverValidation_Success() throws ServerException {
        when(serverValidationUtils.accountValidation(anyString())).thenReturn(account);
        when(controllerDao.getServiceBeanByIdentifierAndAccount(anyString(), anyInt())).thenReturn(serviceBean);

        UtilityBean<ServiceDeleteRequestPojo> clientUtilityBean = UtilityBean.<ServiceDeleteRequestPojo>builder()
                .pojoObject(serviceDeleteRequestPojo)
                .requestParams(CommonUtils.buildRequestParams(null, "test-account"))
                .metadata(new HashMap<String, Object>() {{
                    put(Constants.HARD_DELETE, false);
                    put(Constants.USER_ID_KEY, "user-123");
                }})
                .build();

        UtilityBean<List<ServiceBean>> serverUtilityBean = deleteAccountServiceBL.serverValidation(clientUtilityBean);

        assertNotNull(serverUtilityBean);
        assertEquals(1, serverUtilityBean.getPojoObject().size());
        assertEquals(serviceBean, serverUtilityBean.getPojoObject().get(0));
        assertEquals(1, serverUtilityBean.getMetadata().get(Constants.ACCOUNT_ID));
    }

    @Test
    @DisplayName("serverValidation - Account Not Found")
    void serverValidation_AccountNotFound() throws ServerException {
        when(serverValidationUtils.accountValidation(anyString())).thenThrow(new ServerException("Account not found"));
        UtilityBean<ServiceDeleteRequestPojo> clientUtilityBean = UtilityBean.<ServiceDeleteRequestPojo>builder()
                .pojoObject(serviceDeleteRequestPojo)
                .requestParams(CommonUtils.buildRequestParams(null, "test-account"))
                .metadata(new HashMap<String, Object>() {{
                    put(Constants.HARD_DELETE, false);
                    put(Constants.USER_ID_KEY, "user-123");
                }})
                .build();

        assertThrows(ServerException.class, () -> deleteAccountServiceBL.serverValidation(clientUtilityBean));
    }

    @Test
    @DisplayName("serverValidation - Service Not Found")
    void serverValidation_ServiceNotFound() throws ServerException {
        when(serverValidationUtils.accountValidation(anyString())).thenReturn(account);
        when(controllerDao.getServiceBeanByIdentifierAndAccount(anyString(), anyInt())).thenReturn(null);
        UtilityBean<ServiceDeleteRequestPojo> clientUtilityBean = UtilityBean.<ServiceDeleteRequestPojo>builder()
                .pojoObject(serviceDeleteRequestPojo)
                .requestParams(CommonUtils.buildRequestParams(null, "test-account"))
                .metadata(new HashMap<String, Object>() {{
                    put(Constants.HARD_DELETE, false);
                    put(Constants.USER_ID_KEY, "user-123");
                }})
                .build();

        assertThrows(ServerException.class, () -> deleteAccountServiceBL.serverValidation(clientUtilityBean));
    }

    @Test
    @DisplayName("process - Soft Delete Success")
    void process_SoftDelete_Success() throws DataProcessingException, ControlCenterException {
        UtilityBean<List<ServiceBean>> utilityBean = UtilityBean.<List<ServiceBean>>builder()
                .pojoObject(Collections.singletonList(serviceBean))
                .metadata(new HashMap<String, Object>() {{
                    put(Constants.HARD_DELETE, false);
                    put(Constants.ACCOUNT_ID, 1);
                    put(Constants.USER_ID_KEY, "user-123");
                }})
                .build();

        doNothing().when(controllerDao).updateControllerStatus(anyInt(), anyInt(), anyString());
        doNothing().when(accountServiceDao).updateServiceAliasStatus(anyString(), anyInt(), anyString());
        doNothing().when(serviceRepo).deleteService(anyString(), anyString());
        doNothing().when(applicationRepo).removeServiceFromAllApplications(anyString(), anyInt());

        String result = deleteAccountServiceBL.process(utilityBean);

        assertEquals("Service(s) deleted successfully.", result);
        verify(controllerDao, times(1)).updateControllerStatus(101, 0, "user-123");
        verify(accountServiceDao, times(1)).updateServiceAliasStatus("service-123", 0, "user-123");
        verify(serviceRepo, times(1)).deleteService("test-account", "service-123");
        verify(applicationRepo, times(1)).removeServiceFromAllApplications("test-account", 101);
    }

    @Test
    @DisplayName("process - Hard Delete Success")
    void process_HardDelete_Success() throws DataProcessingException, ControlCenterException {
        UtilityBean<List<ServiceBean>> utilityBean = UtilityBean.<List<ServiceBean>>builder()
                .pojoObject(Collections.singletonList(serviceBean))
                .metadata(new HashMap<String, Object>() {{
                    put(Constants.HARD_DELETE, true);
                    put(Constants.ACCOUNT_ID, 1);
                    put(Constants.USER_ID_KEY, "user-123");
                }})
                .build();

        doNothing().when(accountServiceDao).hardDeleteServiceGroupMappingByServiceId(anyInt());
        doNothing().when(accountServiceDao).deleteTagMappingByEntityId(anyInt(), anyString());
        doNothing().when(accountServiceDao).deleteServiceAliasByIdentifier(anyString());
        doNothing().when(accountServiceDao).deleteAnomalySuppressionByServiceId(anyInt());
        doNothing().when(controllerDao).deleteController(anyInt());
        doNothing().when(serviceRepo).deleteService(anyString(), anyString());
        doNothing().when(applicationRepo).removeServiceFromAllApplications(anyString(), anyInt());

        String result = deleteAccountServiceBL.process(utilityBean);

        assertEquals("Service(s) deleted successfully.", result);
        verify(accountServiceDao, times(1)).hardDeleteServiceGroupMappingByServiceId(101);
        verify(accountServiceDao, times(1)).deleteTagMappingByEntityId(101, Constants.CONTROLLER);
        verify(accountServiceDao, times(1)).deleteServiceAliasByIdentifier("service-123");
        verify(accountServiceDao, times(1)).deleteAnomalySuppressionByServiceId(101);
        verify(controllerDao, times(1)).deleteController(101);
        verify(serviceRepo, times(1)).deleteService("test-account", "service-123");
        verify(applicationRepo, times(1)).removeServiceFromAllApplications("test-account", 101);
    }
}
