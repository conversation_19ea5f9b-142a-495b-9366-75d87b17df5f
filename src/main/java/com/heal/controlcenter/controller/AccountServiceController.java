package com.heal.controlcenter.controller;

import com.heal.controlcenter.beans.AccountServiceValidationBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.GetAccountServicesBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.ErrorResponsePojo;
import com.heal.controlcenter.pojo.ResponsePojo;
import com.heal.controlcenter.pojo.ServiceListPage;
import com.heal.controlcenter.util.JsonFileParser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.heal.configuration.pojos.IdPojo;
import com.heal.configuration.pojos.ServiceConfiguration;
import com.heal.controlcenter.beans.ServiceBean;
import com.heal.controlcenter.businesslogic.AddAccountServiceBL;
import com.heal.controlcenter.pojo.ServicePojo;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@Configuration
@RestController
public class AccountServiceController {

    @Autowired
    GetAccountServicesBL getAccountServicesBL;
    @Autowired
    JsonFileParser headersParser;
    @Autowired
    AddAccountServiceBL addAccountServiceBL;

    @Operation(
            summary = "Retrieve Services for Account",
            responses = {
                    @ApiResponse(
                            responseCode = "200", description = "Services retrieved successfully",
                            content = @Content(
                                    schema = @Schema(
                                            implementation = Page.class
                                    )
                            )
                    ),
                    @ApiResponse(
                            responseCode = "400", description = "Error in retrieving services",
                            content = @Content(schema = @Schema(implementation = ErrorResponsePojo.class))
                    ),
                    @ApiResponse(
                            responseCode = "500", description = "Internal Server Exception encountered while retrieving services",
                            content = @Content(schema = @Schema(implementation = ErrorResponsePojo.class))
                    )
            }
    )
    /**
     * Retrieves a paginated list of services for a given account, with optional search and sorting.
     *
     * @param authorization The Bearer token for authentication.
     * @param accountIdentifier The unique identifier of the account.
     * @param pageable Pagination and sorting information.
     * @param searchTerm An optional term to filter services by name or identifier.
     * @return A {@link ResponseEntity} containing a paginated list of {@link ServiceListPage} objects.
     * @throws ClientException If the client request is invalid.
     * @throws DataProcessingException If an error occurs during data processing.
     * @throws ServerException If a server-side error occurs.
     */
    @GetMapping(value = "/accounts/{accountIdentifier}/services")
    public ResponseEntity<ResponsePojo<Page<ServiceListPage>>> getAccountServices(
            @Parameter(
                    name = "Authorization",
                    description = "Bearer token for authentication",
                    required = true,
                    example = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            )
            @RequestHeader(value = "Authorization") String authorization,
            @Parameter(
                    name = "accountIdentifier",
                    description = "Unique identifier of the account",
                    required = true,
                    example = "account-001"
            )
            @PathVariable("accountIdentifier") String accountIdentifier,
            @Parameter(
                    description = "Pagination and sorting information. " +
                            "Use `page` (0-indexed), `size` (items per page), and `sort` (property[,asc|desc]). " +
                            "Example: `?page=0&size=10&sort=name,asc`",
                    required = false
            )
            Pageable pageable,
            @Parameter(
                    description = "Search term to filter services by name or identifier.",
                    required = false,
                    example = "TestService")
            @RequestParam(required = false) String searchTerm)
            throws ClientException, DataProcessingException, ServerException {

        UtilityBean<Object> utilityBean = getAccountServicesBL.clientValidation(null, authorization, accountIdentifier, searchTerm);
        utilityBean.setPageable(pageable);
        UtilityBean<AccountServiceValidationBean> validatedBean = getAccountServicesBL.serverValidation(utilityBean);
        Page<ServiceListPage> services = getAccountServicesBL.process(validatedBean);

        ResponsePojo<Page<ServiceListPage>> responsePojo = new ResponsePojo<>("success",
                services, HttpStatus.OK);

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
    }

    @Operation(summary = "Add or update a service for an account",
            responses = {
                    @ApiResponse(responseCode = "200", description = "Service added/updated successfully.",
                            content = @Content(schema = @Schema(implementation = ServiceConfiguration.class))),
                    @ApiResponse(responseCode = "500", description = "Internal server error.")
            })
    @PostMapping
    public ResponseEntity<ResponsePojo<List<IdPojo>>> addService(
            @RequestHeader("Authorization") String authorization,
            @PathVariable String accountIdentifier,
            @RequestBody List<ServicePojo> body) throws ClientException, ServerException, DataProcessingException {
        log.trace("Method Invoked: addOrUpdateService");
        UtilityBean<List<ServicePojo>> utilityBean = addAccountServiceBL.clientValidation(body, authorization, accountIdentifier);
        UtilityBean<List<ServiceBean>> updatedUtilityBean = addAccountServiceBL.serverValidation(utilityBean);
        List<IdPojo> result = addAccountServiceBL.process(updatedUtilityBean);
        ResponsePojo<List<IdPojo>> responseBean = new ResponsePojo<>("Service(s) added/updated successfully", result, HttpStatus.OK);
        return ResponseEntity.ok(responseBean);
    }

}

