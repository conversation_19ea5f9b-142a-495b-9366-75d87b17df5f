package com.heal.controlcenter.controller;

import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.GetEmailConfigurationsBL;
import com.heal.controlcenter.businesslogic.PostEmailConfigurationsBL;
import com.heal.controlcenter.businesslogic.PutEmailConfigurationsBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.ResponsePojo;
import com.heal.controlcenter.pojo.SMTPDetailsPojo;
import com.heal.controlcenter.swagger.ErrorResponseSchemas;
import com.heal.controlcenter.util.JsonFileParser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Slf4j
@Configuration
@RestController
public class EmailConfigurationsController {

    @Autowired
    GetEmailConfigurationsBL getEmailConfigurationsBL;
    @Autowired
    PutEmailConfigurationsBL putEmailConfigurationsBL;
    @Autowired
    PostEmailConfigurationsBL postEmailConfigurationsBL;
    @Autowired
    JsonFileParser headersParser;

    @Operation(
            summary = "Fetches Email configurations of the user.",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Email configurations fetched successfully.",
                            content = @Content(schema = @Schema(implementation = SMTPDetailsPojo.class))
                    ),
                    @ApiResponse(
                            responseCode = "400",
                            description = "Bad Request - Invalid input parameters",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ErrorResponseSchemas.ErrorResponse.class)
                            )
                    ),
                    @ApiResponse(
                            responseCode = "500",
                            description = "Internal Server Error",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ErrorResponseSchemas.ServerErrorResponse.class)
                            )
                    )
            }
    )
    @RequestMapping(value = "/accounts/{identifier}/email-configurations", method = RequestMethod.GET)
    public ResponseEntity<ResponsePojo<SMTPDetailsPojo>> getEmailConfigurations(
            @Parameter(
                    name = "Authorization",
                    description = "Bearer token for authentication",
                    required = true,
                    example = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            )
            @RequestHeader("Authorization") String authorization,
            @Parameter(
                    name = "identifier",
                    description = "Unique identifier of the account",
                    required = true,
                    example = "account-001"
            )
            @PathVariable("identifier") String accountIdentifier)
            throws ClientException, ServerException, DataProcessingException {
        log.trace("Method Invoked : getEmailConfigurations");

        UtilityBean<Object> emailUtilityBean = getEmailConfigurationsBL.clientValidation(null, authorization, accountIdentifier);
        Integer accountId = getEmailConfigurationsBL.serverValidation(emailUtilityBean);
        SMTPDetailsPojo data = getEmailConfigurationsBL.process(accountId);

        ResponsePojo<SMTPDetailsPojo> responsePojo = new ResponsePojo<>("Email configurations fetched successfully.", data, HttpStatus.OK);

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
    }

    @Operation(
            summary = "Updates Email configurations for the specified account.",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Email configurations updated successfully."
                    ),
                    @ApiResponse(
                            responseCode = "400",
                            description = "Bad Request - Invalid input parameters",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ErrorResponseSchemas.ErrorResponse.class)
                            )
                    ),
                    @ApiResponse(
                            responseCode = "500",
                            description = "Internal Server Error",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ErrorResponseSchemas.ServerErrorResponse.class)
                            )
                    )
            }
    )
    @RequestMapping(value = "/accounts/{identifier}/email-configurations", method = RequestMethod.PUT)
    public ResponseEntity<ResponsePojo<Object>> putEmailConfigurations(
            @Parameter(
                    name = "Authorization",
                    description = "Bearer token for authentication",
                    required = true,
                    example = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            )
            @RequestHeader("Authorization") String authorization,
            @Parameter(
                    name = "identifier",
                    description = "Unique identifier of the account",
                    required = true,
                    example = "account-001"
            )
            @PathVariable("identifier") String accountIdentifier,
            @Parameter(
                    description = "SMTP configuration details to update",
                    required = true
            )
            @Validated @RequestBody SMTPDetailsPojo body)
            throws ClientException, ServerException, DataProcessingException {
        log.trace("Method Invoked : putEmailConfigurations");

        UtilityBean<SMTPDetailsPojo> smtpUtilityBean = putEmailConfigurationsBL.clientValidation(body, authorization, accountIdentifier);
        putEmailConfigurationsBL.serverValidation(smtpUtilityBean);
        putEmailConfigurationsBL.process(smtpUtilityBean);

        ResponsePojo<Object> responsePojo = new ResponsePojo<>("Email configurations updated successfully.", null, HttpStatus.OK);

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
    }

    @Operation(
            summary = "Adds Email configurations for the specified account.",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Email configurations added successfully."
                    ),
                    @ApiResponse(
                            responseCode = "400",
                            description = "Bad Request - Invalid input parameters",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ErrorResponseSchemas.ErrorResponse.class)
                            )
                    ),
                    @ApiResponse(
                            responseCode = "500",
                            description = "Internal Server Error",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ErrorResponseSchemas.ServerErrorResponse.class)
                            )
                    )
            }
    )
    @RequestMapping(value = "/accounts/{identifier}/email-configurations", method = RequestMethod.POST)
    public ResponseEntity<ResponsePojo<Object>> addEmailConfiguration(
            @Parameter(
                    name = "Authorization",
                    description = "Bearer token for authentication",
                    required = true,
                    example = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            )
            @RequestHeader("Authorization") String authorization,
            @Parameter(
                    name = "identifier",
                    description = "Unique identifier of the account",
                    required = true,
                    example = "account-001"
            )
            @PathVariable("identifier") String accountIdentifier,
            @Parameter(
                    description = "SMTP configuration details to add",
                    required = true
            )
            @Validated @RequestBody SMTPDetailsPojo body)
            throws ClientException, ServerException, DataProcessingException {
        log.trace("Method Invoked : addEmailConfiguration");

        UtilityBean<SMTPDetailsPojo> smtpDetailsUtilityBean = postEmailConfigurationsBL.clientValidation(body, authorization, accountIdentifier);
        postEmailConfigurationsBL.serverValidation(smtpDetailsUtilityBean);
        postEmailConfigurationsBL.process(smtpDetailsUtilityBean);

        ResponsePojo<Object> responsePojo = new ResponsePojo<>("Email configurations added successfully.", null, HttpStatus.OK);

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
    }
}
