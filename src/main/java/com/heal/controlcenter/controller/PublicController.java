package com.heal.controlcenter.controller;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import com.heal.controlcenter.pojo.ResponsePojo;

@RestController
public class PublicController {

    @GetMapping("/public/health")
    public ResponseEntity<ResponsePojo<String>> getPublicHealth() {
        ResponsePojo<String> response = new ResponsePojo<>("OK", "Service is publicly accessible.", HttpStatus.OK);
        return ResponseEntity.ok(response);
    }
}
