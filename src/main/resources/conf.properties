#=====================================
# PATHS
#=====================================
server.servlet.context-path=/v2.0/api
logging.path=/opt/jboss/keycloak/standalone/log

#=====================================
# KEYCLOAK CONFIG
#KeyCloak parameters, these are used for session management
#=====================================
keycloak.ip=**************
keycloak.port=8443
keycloak.user=appsoneadmin
keycloak.pwd=QXBwc29uZUAxMjM=
setup.type=
filename.headers.properties=headers_details.json
filename.keycloak.details=keycloak_details.json

#=====================================
# CORS CONFIGURATION
#=====================================
# Allow all origins for development (use specific domains in production)
header.access.control.allow.origins=*
# Allow common headers
header.access.control.allow.headers=Content-Type,Authorization,X-Requested-With,Content-Length,Accept,Origin
# Allow all HTTP methods
header.access.control.allow.methods=GET,POST,PUT,DELETE,OPTIONS,HEAD,PATCH
# Don't allow credentials with wildcard origin (security best practice)
header.access.control.allow.credentials=false

#=====================================
# SPRING DATASOURCE CONFIG
#=====================================
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.url=************************************************************************************************************************************************************
spring.datasource.username=dbadmin
spring.datasource.password=cm9vdEAxMjM=
spring.datasource.hikari.connection-timeout=5000
spring.datasource.hikari.minimumIdle=5
spring.datasource.hikari.maximumPoolSize=5
spring.datasource.hikari.poolName=Heal_ControlCenter_Pool

#=====================================
# OPTIMIZATION
#=====================================
spring.datasource.hikari.data-source-properties.useConfigs=maxPerformance
spring.datasource.hikari.data-source-properties.rewriteBatchedStatements=true

#=======================================
# NOTIFICATION SETTINGS
#=======================================
openForLong.minDuration.time.min=15
openForTooLong.minDuration.time.min=30
openForLong.maxDuration.time.min=1440
openForTooLong.maxDuration.time.min=2880

# ======================================
# Dormant Schedular Configuration
# ======================================
user.dormant.creation.time.days=30
user.dormant.login.time.days=90

# ==========================================================
#Redis Server Configuration
# ==========================================================
spring.redis.cluster.nodes=**************:5001,**************:5002,**************:5003,**************:5004,**************:5005,**************:5006
spring.redis.ssl=true
spring.redis.username=
spring.redis.password=cmVkaXNAMTIz
spring.redis.max.idle.connections=500
spring.redis.min.idle.connections=100
spring.redis.max.total.connections=500
spring.redis.max.wait.secs=20
spring.redis.share.native.connection=false
spring.redis.cluster.mode=true

# ==========================================================
# Rabbit MQ Configuration
# ==========================================================
spring.rabbitmq.addresses=**************
spring.rabbitmq.port=5672
spring.rabbitmq.username=guest
spring.rabbitmq.password=
spring.rabbitmq.ssl.enabled=false
spring.rabbitmq.ssl.protocol=TLSv1.3
spring.rabbitmq.outputQueueName=command-messages

# Swagger/OpenAPI Configuration
springdoc.api-docs.path=/v3/api-docs
springdoc.api-docs.enabled=true
springdoc.swagger-ui.path=/swagger-ui.html
# ==========================================================
#Cache Configuration
# ==========================================================
record.cache.stats=true

run.cache.refresh.scheduler=false
cache.refresh.scheduler.interval.minutes=10

redis.cache.mode=0
opensearch.cache.mode=0
percona.cache.mode=0

opensearch.cache.max.size = 50
opensearch.cache.expire.interval.minutes = 2

accounts_id_tenants.cache.max.size = 10000
accounts_id_tenants.cache.expire.interval.minutes = 2

heal_index_zones.cache.max.size = 50
heal_index_zones.cache.expire.interval.minutes = 2

view_types.cache.max.size = 10000
view_types.cache.expire.interval.minutes = 2

# =====================================
# OPENSEARCH NODES CONFIGURATION
# =====================================
opensearch.nodes=**************:9211
opensearch.nodes.eum=**************:9250
opensearch.nodes.jaeger=https://**************:9211
opensearch.nodes.jim=**************:9250
opensearch.nodes.kpi=**************:9200
opensearch.nodes.misc=**************:9212
opensearch.nodes.txn=**************:9225

# =====================================
# OPENSEARCH CREDENTIALS CONFIGURATION
# =====================================
opensearch.username=healadmin
opensearch.username.eum=healadmin
opensearch.username.jim=healadmin
opensearch.username.kpi=healadmin
opensearch.username.misc=healadmin
opensearch.username.txn=healadmin

opensearch.password.encrypted=cm9vdEAxMjM=
opensearch.password.encrypted.eum=cm9vdEAxMjM=
opensearch.password.encrypted.jim=cm9vdEAxMjM=
opensearch.password.encrypted.kpi=cm9vdEAxMjM=
opensearch.password.encrypted.misc=cm9vdEAxMjM=
opensearch.password.encrypted.txn=cm9vdEAxMjM=