package com.heal.controlcenter.dao.mysql;


import com.heal.controlcenter.beans.ServiceSuppPersistenceConfigurationBean;
import com.heal.controlcenter.exception.ControlCenterException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.List;

@Slf4j
@Repository
public class ServiceConfigurationDao {

    @Autowired
    JdbcTemplate jdbcTemplate;

    public void updateServiceSuppPersistenceConfig(List<ServiceSuppPersistenceConfigurationBean> serviceConfigList) throws ControlCenterException {

        String query = "UPDATE service_anomaly_configurations SET user_details_id = ?, " +
                "low_suppression = ?, low_persistence = ?, low_enable = ?, " +
                "medium_suppression = ?, medium_persistence = ?, medium_enable = ?, " +
                "high_suppression = ?, high_persistence = ?, high_enable = ?, updated_time = ?, closing_window = ?, max_data_breaks = ? " +
                "where account_id = ? and service_id = ? and id = ?";
        try {
            log.info("Bulk updating service level suppression persistence configs.");

            jdbcTemplate.batchUpdate(query, new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps, int i) throws SQLException {
                    ServiceSuppPersistenceConfigurationBean configBean = serviceConfigList.get(i);
                    ps.setObject(1, configBean.getUserDetailsId());
                    ps.setObject(2, configBean.getLowSuppression());
                    ps.setObject(3, configBean.getLowPersistence());
                    ps.setObject(4, configBean.isLowEnable());
                    ps.setObject(5, configBean.getMediumSuppression());
                    ps.setObject(6, configBean.getMediumPersistence());
                    ps.setObject(7, configBean.isMediumEnable());
                    ps.setObject(8, configBean.getHighSuppression());
                    ps.setObject(9, configBean.getHighPersistence());
                    ps.setObject(10, configBean.isHighEnable());
                    ps.setObject(11, configBean.getUpdatedTime());
                    ps.setObject(12, configBean.getClosingWindow());
                    ps.setObject(13, configBean.getMaxDataBreaks());
                    ps.setObject(14, configBean.getAccountId());
                    ps.setObject(15, configBean.getServiceId());
                    ps.setObject(16, configBean.getId());
                }

                @Override
                public int getBatchSize() {
                    return serviceConfigList.size();
                }
            });

        } catch (Exception e) {
            log.error("Exception encountered while bulk updating service configurations. Details: ", e);
            throw new ControlCenterException("Error occurred while bulk updating service configurations.");
        }
    }

    public List<ServiceSuppPersistenceConfigurationBean> getServiceConfiguration(int accountId, int serviceId) {
        String query = "SELECT id, account_id, service_id, user_details_id, created_time, updated_time, " +
                "start_collection_interval, end_collection_interval, low_enable, low_persistence, low_suppression, " +
                "medium_enable, medium_persistence, medium_suppression, high_enable, high_persistence, high_suppression, closing_window, max_data_breaks " +
                "FROM service_anomaly_configurations WHERE account_id = ? AND service_id = ?";

        return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(ServiceSuppPersistenceConfigurationBean.class), accountId, serviceId);
    }

    public int[] addServiceConfiguration(List<ServiceSuppPersistenceConfigurationBean> serviceDetails) {
        String sql = "INSERT INTO service_anomaly_configurations (" +
                "service_id, account_id, user_details_id, created_time, updated_time, " +
                "start_collection_interval, end_collection_interval, low_persistence, low_suppression, " +
                "medium_persistence, medium_suppression, high_persistence, high_suppression, low_enable, medium_enable, high_enable, closing_window, max_data_breaks) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        return jdbcTemplate.batchUpdate(sql, new BatchPreparedStatementSetter() {
            @Override
            public void setValues(PreparedStatement ps, int i) throws SQLException {
                ServiceSuppPersistenceConfigurationBean bean = serviceDetails.get(i);
                ps.setLong(1, bean.getServiceId());
                ps.setLong(2, bean.getAccountId());
                ps.setString(3, bean.getUserDetailsId());
                ps.setTimestamp(4, Timestamp.valueOf(bean.getCreatedTime()));
                ps.setTimestamp(5, Timestamp.valueOf(bean.getUpdatedTime()));
                ps.setInt(6, bean.getStartCollectionInterval());
                ps.setInt(7, bean.getEndCollectionInterval());
                ps.setInt(8, bean.getLowPersistence());
                ps.setInt(9, bean.getLowSuppression());
                ps.setInt(10, bean.getMediumPersistence());
                ps.setInt(11, bean.getMediumSuppression());
                ps.setInt(12, bean.getHighPersistence());
                ps.setInt(13, bean.getHighSuppression());
                ps.setBoolean(14, bean.isLowEnable());
                ps.setBoolean(15, bean.isMediumEnable());
                ps.setBoolean(16, bean.isHighEnable());
                ps.setInt(17, bean.getClosingWindow());
                ps.setInt(18, bean.getMaxDataBreaks());
            }

            @Override
            public int getBatchSize() {
                return serviceDetails.size();
            }
        });
    }
}
