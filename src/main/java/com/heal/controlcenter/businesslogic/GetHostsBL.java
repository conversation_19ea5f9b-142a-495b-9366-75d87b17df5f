package com.heal.controlcenter.businesslogic;

import com.appnomic.appsone.common.enums.DiscoveryStatus;
import com.google.common.base.Throwables;
import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.dao.mysql.AgentDao;
import com.heal.controlcenter.dao.mysql.AutoDiscoveryDao;
import com.heal.controlcenter.dao.mysql.MasterDataDao;
import com.heal.controlcenter.dao.mysql.entity.ComponentDao;
import com.heal.controlcenter.dao.mysql.entity.ComponentInstanceDao;
import com.heal.controlcenter.dao.opensearch.AutoDiscoveryRepo;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.AgentInstanceMappingPojo;
import com.heal.controlcenter.pojo.HostProcessCountPojo;
import com.heal.controlcenter.pojo.InstanceComponentMappingDetails;
import com.heal.controlcenter.pojo.InstanceServiceApplicationDetailsPojo;
import com.heal.controlcenter.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.heal.controlcenter.util.Constants.ACCOUNT;

@Slf4j
@Component
public class GetHostsBL implements BusinessLogic<Object, UtilityBean<AccountBean>, Page<GetHost>> {

    @Autowired
    ClientValidationUtils clientValidationUtils;
    @Autowired
    ServerValidationUtils serverValidationUtils;
    @Autowired
    private MasterDataDao masterDataDao;

    @Autowired
    private AccountsDao accountsDao;

    @Autowired
    private AutoDiscoveryDao autoDiscoveryDao;

    @Autowired
    private ComponentInstanceDao componentInstanceDao;

    @Autowired
    private ComponentDao componentDao;

    @Autowired
    private AgentDao agentDao;

    @Autowired
    private DateTimeUtil dateTimeUtil;

    @Override
    public UtilityBean<Object> clientValidation(Object requestObject, String... requestParams) throws ClientException {
        String authKey = requestParams[0];
        clientValidationUtils.authKeyValidation(authKey);

        String accountIdentifier = requestParams[1];
        clientValidationUtils.accountIdentifierValidation(accountIdentifier);
        //Handle search term
        String searchTerm = requestParams.length > 2 ? requestParams[2] : null;
        if (searchTerm != null) {
            searchTerm = searchTerm.trim();
            if (searchTerm.isEmpty()) searchTerm = null;
        }
        HashMap<String, String> requestParamsMap = CommonUtils.buildRequestParams(authKey, accountIdentifier);
        //Add search term to request params
        if (searchTerm != null) {
            requestParamsMap.put(Constants.SEARCH_TERM_KEY, searchTerm);
        }

        return UtilityBean.builder()
                .requestParams(requestParamsMap)
                .build();
    }

    @Override
    public UtilityBean<AccountBean> serverValidation(UtilityBean<Object> utilityBean) throws ServerException {
        String authKey = utilityBean.getRequestParams().get(Constants.AUTH_KEY);
        String userId = serverValidationUtils.authKeyValidation(authKey);
        Map<String, String> requestParams = utilityBean.getRequestParams();
        // 1. Validate account using AccountsDao
        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
        AccountBean account = accountsDao.getAccountByIdentifier(accountIdentifier);
        if (account == null) {
            String message = String.format(UIMessages.ACCOUNT_IDENTIFIER_UNAVAILABLE, accountIdentifier);
            log.error(message);
            throw new ServerException(message);
        }
        // 2. Get host component type using MasterDataDao
        List<MasterComponentTypeBean> componentTypes = masterDataDao.getMasterComponentTypesData(account.getId());
        MasterComponentTypeBean componentTypeBean = componentTypes.stream()
                .filter(ct -> Constants.COMPONENT_TYPE_HOST.equalsIgnoreCase(ct.getName()))
                .findFirst()
                .orElse(null);

        if (componentTypeBean == null) {
            log.error("Host component type not found");
            throw new ServerException("Host component type doesn't exist");
        }
        // 4. Prepare metadata
        Map<String, Object> metadata = Map.of("hostComponentTypeId", componentTypeBean.getId(), "userId", userId);
        // 5. Build UtilityBean with results
        return UtilityBean.<AccountBean>builder()
                .metadata(Map.of(ACCOUNT, account))
                .requestParams(requestParams)
                .metadata(metadata)
                .pageable(utilityBean.getPageable())
                .build();
    }

    @Override
    public Page<GetHost> process(UtilityBean<AccountBean> accountBean) throws DataProcessingException {
        try {
            int hostComponentTypeId = (Integer) accountBean.getMetadata().get("hostComponentTypeId");
            AccountBean healHealthAccount = accountsDao.getAccountDetailsForIdentifier(Constants.HEAL_HEALTH_ACCOUNT_IDENTIFIER);
            int healHealthAccountId = 0;
            if (healHealthAccount == null) {
                log.warn("heal_health account not found in DB");
            } else {
                healHealthAccountId = healHealthAccount.getId();
            }

            // Use AutoDiscoveryDao instead of AutoDiscoveryDataService
            Map<String, HostWiseConnections> hostIpAddressWiseConnections = autoDiscoveryDao.getNumberOfConnectionsForHostIpAddress(((AccountBean) accountBean.getMetadata().get(ACCOUNT)).getId(), healHealthAccountId).stream()
                    .collect(Collectors.toMap(HostWiseConnections::getHostAddress, Function.identity()));

            Map<String, HostWiseConnections> hostIdentifierWiseConnections = autoDiscoveryDao.getNumberOfConnectionsForHostIdentifier(((AccountBean) accountBean.getMetadata().get(ACCOUNT)).getId(), healHealthAccountId).stream()
                    .collect(Collectors.toMap(HostWiseConnections::getHostIdentifier, Function.identity()));

            Map<String, String> hostIdErrorMap = new AutoDiscoveryRepo().getAutoDiscoveryHostErrorLogs();
            List<GetHost> preExistingData = preExistingData(((AccountBean) accountBean.getMetadata().get(ACCOUNT)).getId(), hostIdErrorMap, hostIpAddressWiseConnections, hostIdentifierWiseConnections,hostComponentTypeId);
            List<GetHost> stagingTableData = autoDiscoveryPart(hostIdErrorMap, hostIpAddressWiseConnections, hostIdentifierWiseConnections);

            List<GetHost> duplicatesFromSDMTable = new ArrayList<>();
            List<GetHost> duplicatesFromStagingTable = new ArrayList<>();

            removeDuplicates(preExistingData, stagingTableData, duplicatesFromSDMTable, duplicatesFromStagingTable);

            stagingTableData.removeAll(duplicatesFromStagingTable);
            preExistingData.removeAll(duplicatesFromSDMTable);
            preExistingData.addAll(stagingTableData);

            preExistingData.sort(Comparator.comparing(GetHost::getStatus, Comparator.reverseOrder())
                    .thenComparing(GetHost::getLastDiscoveryRunTime, Comparator.reverseOrder())
                    .thenComparing(GetHost::getHostName, Comparator.comparingInt(o -> Character.toLowerCase(o.charAt(0)))));

            //Apply search filtering if term exists
            String searchTerm = accountBean.getRequestParams().get(Constants.SEARCH_TERM_KEY);
            // To this (remove lowercase conversion - handled in matchesSingleTerm):
            if (searchTerm != null && !searchTerm.isEmpty()) {
                preExistingData = preExistingData.stream()
                        .filter(host -> matchesSearchTerm(host, searchTerm))
                        .collect(Collectors.toList());
            }

            List<HostProcessCountPojo> newProcessList = autoDiscoveryDao.getNewProcessCount();
            if (newProcessList != null && !newProcessList.isEmpty() && newProcessList.get(0).getHostIdentifier() != null) {
                preExistingData.forEach(one -> newProcessList.forEach(two -> {
                    if (two.getHostIdentifier().equals(one.getHostIdentifier()) && two.getCountOfNewProcess() > 0)
                        one.setNewInstanceDiscovered(true);
                }));
            }

            Pageable pageable = accountBean.getPageable() != null ? accountBean.getPageable() : PageRequest.of(0, 20);
            int start = (int) pageable.getOffset();
            int end = Math.min(start + pageable.getPageSize(), preExistingData.size());
            List<GetHost> paginatedHosts = preExistingData.subList(start, end);

            return new PageImpl<>(paginatedHosts, pageable, preExistingData.size());

        } catch (Exception e) {
            throw new DataProcessingException(Throwables.getRootCause(e).getMessage());
        }
    }

    private boolean matchesSearchTerm(GetHost host, String searchTerm) {
        if (searchTerm == null || searchTerm.isEmpty()) return true;

        String[] terms = searchTerm.split(",");
        for (String term : terms) {
            term = term.trim().toLowerCase();
            if (term.isEmpty()) continue;

            // Check if ANY field matches the current term
            if (matchesSingleTerm(host, term)) {
                return true;
            }
        }
        return false;
    }

    private boolean matchesSingleTerm(GetHost host, String term) {
        // Check simple string fields
        if (containsIgnoreCase(host.getHostName(), term) ||
                containsIgnoreCase(host.getComponentTypeName(), term) ||
                containsIgnoreCase(host.getEnvironment(), term) ||
                containsIgnoreCase(host.getProcess(), term) ||
                containsIgnoreCase(host.getComponentName(), term) ||
                containsIgnoreCase(host.getStatus().name(), term)) {
            return true;
        }
        // Check host addresses
        if (host.getHostAddress() != null &&
                host.getHostAddress().stream().anyMatch(addr -> containsIgnoreCase(addr, term))) {
            return true;
        }
        // Check applications
        if (host.getApplication() != null &&
                host.getApplication().stream()
                        .filter(Objects::nonNull)
                        .anyMatch(app -> containsIgnoreCase(app.getName(), term))) {
            return true;
        }
        // Check services
        return host.getService() != null &&
                host.getService().stream()
                        .filter(Objects::nonNull)
                        .anyMatch(service -> containsIgnoreCase(service.getName(), term));
    }
    // Case-insensitive contains check with null safety
    private boolean containsIgnoreCase(String source, String search) {
        return source != null && source.toLowerCase().contains(search);
    }

    public void removeDuplicates(List<GetHost> preExistingData, List<GetHost> stagingTableData, List<GetHost> duplicatesFromSDMTable, List<GetHost> duplicatesFromStagingTable) {
        preExistingData.forEach(one -> stagingTableData
                .forEach(two -> {
                    if (one.getHostIdentifier().equals(two.getHostIdentifier()) && one.getService().size() < two.getService().size()) {
                        duplicatesFromSDMTable.add(one);
                    }
                    if ((one.getHostIdentifier().equals(two.getHostIdentifier()) && one.getService().size() >= two.getService().size())
                            || (one.getHostAddress().equals(two.getHostAddress()) && two.getStatus() == DiscoveryStatus.DISCOVERED_NOT_ADDED_TO_SYSTEM)) {
                        duplicatesFromStagingTable.add(two);
                    }
                }));
    }

    private List<GetHost> preExistingData(int accountId, Map<String, String> hostIdErrorMap, Map<String, HostWiseConnections> hostIpAddressWiseConnections,
                                          Map<String, HostWiseConnections> hostIdentifierWiseConnections, int hostComponentTypeId) throws ControlCenterException {
        long time = System.currentTimeMillis();
        try {
            EnvironmentHelper envHelper = new EnvironmentHelper();
            List<ViewComponentInstanceBean> instanceBeans =
                    componentInstanceDao.getActiveInstanceDetailsForAccount(accountId);
            log.trace("Time taken for instanceBeans:{} ms", (System.currentTimeMillis() - time));
            time = System.currentTimeMillis();


            List<ViewComponentInstanceBean> hostInstanceBeans = instanceBeans.parallelStream()
                    .filter(c -> c.getMstComponentTypeId() == hostComponentTypeId)  // Use parameter
                    .collect(Collectors.toList());
            log.trace("Time taken for hostInstanceBeans:{} ms", (System.currentTimeMillis() - time));
            time = System.currentTimeMillis();

            Map<Integer, List<ViewComponentInstanceBean>> hostComponentInstanceMap = instanceBeans
                    .parallelStream().filter(c -> c.getMstComponentTypeId() != hostComponentTypeId)
                    .collect(Collectors.groupingBy(ViewComponentInstanceBean::getHostId));
            log.trace("Time taken for hostComponentInstanceMap:{} ms", (System.currentTimeMillis() - time));
            time = System.currentTimeMillis();

            List<CompClusterMappingBean> instClusterMapping = componentDao.getInstanceClusterMapping(accountId);
            Map<Integer, List<CompClusterMappingBean>> instClusterMap = instClusterMapping.parallelStream()
                    .collect(Collectors.groupingBy(CompClusterMappingBean::getCompInstanceId));
            log.trace("Time taken for hostClusterMap:{} ms", (System.currentTimeMillis() - time));
            time = System.currentTimeMillis();
            List<ViewClusterServicesBean> clusterServicesBeans = componentInstanceDao.getAllClusterServices();
            Map<Integer, List<IdNamePojo>> clusterServicesBeanMap = clusterServicesBeans.parallelStream()
                    .collect(Collectors.groupingBy(ViewClusterServicesBean::getClusterId)).entrySet().stream()
                    .collect(Collectors.toMap(Map.Entry::getKey, c -> c.getValue().stream().distinct().map(d -> IdNamePojo.builder()
                            .id(d.getServiceId())
                            .name(d.getServiceName())
                            .identifier(d.getServiceIdentifier()).build()).collect(Collectors.toList())));
            log.trace("Time taken for clusterServicesBeanMap:{} ms", (System.currentTimeMillis() - time));
            time = System.currentTimeMillis();
            List<ViewApplicationServiceMappingBean> serviceApplicationsBeans = componentInstanceDao.getAllServiceApplication(accountId);
            Map<Integer, List<IdNamePojo>> serviceApplicationsBeanMap = serviceApplicationsBeans.parallelStream()
                    .collect(Collectors.groupingBy(ViewApplicationServiceMappingBean::getServiceId)).entrySet().stream()
                    .collect(Collectors.toMap(Map.Entry::getKey, c -> c.getValue().stream().distinct().map(d -> IdNamePojo.builder()
                            .id(d.getApplicationId())
                            .name(d.getApplicationName())
                            .identifier(d.getApplicationIdentifier()).build()).collect(Collectors.toList())));
            log.trace("Time taken for serviceApplicationsBeanMap:{} ms", (System.currentTimeMillis() - time));
            time = System.currentTimeMillis();

            List<AgentInstanceMappingPojo> agentBeanList = agentDao.getInstanceAgentMapping(accountId);
            Map<Integer, List<AgentInstanceMappingPojo>> agentBeanMap = agentBeanList.parallelStream()
                    .collect(Collectors.groupingBy(AgentInstanceMappingPojo::getInstanceId));
            log.trace("Time taken for agentBeanMap:{} ms", (System.currentTimeMillis() - time));
            time = System.currentTimeMillis();

            List<CompInstanceAttributesBean> instCompAttributesDetails = componentInstanceDao.getInstAttributeMapping("MonitorPort");
            Map<Integer, List<CompInstanceAttributesBean>> instCompAttributesMap = instCompAttributesDetails.parallelStream()
                    .collect(Collectors.groupingBy(CompInstanceAttributesBean::getCompInstanceId));
            log.trace("Time taken for instCompAttributesMap:{} ms", (System.currentTimeMillis() - time));
            time = System.currentTimeMillis();

            /*
             * Create GetHost Details List
             */
            List<GetHost> getHostList = hostInstanceBeans.parallelStream()
                    .map(c -> {
                        HostWiseConnections hostWiseConnections = hostIdentifierWiseConnections.getOrDefault(c.getIdentifier(), hostIpAddressWiseConnections.get(c.getHostAddress()));
                        int newHostsCount = hostWiseConnections == null ? 0 : hostWiseConnections.getNoOfConnections();
                        List<String> newHostIps = hostWiseConnections == null ? new ArrayList<>() : new ArrayList<>(Arrays.asList(hostWiseConnections.getListOfRemoteIps().split(",")));

                        GetHost getHost = GetHost.builder()
                                .id(String.valueOf(c.getId()))
                                .hostName(c.getName())
                                .hostIdentifier(c.getIdentifier())
                                .hostAddress(Collections.singletonList(c.getHostAddress()))
                                .environment(envHelper.getOrDefaultEnvironmentName(c.getIsDR()))
                                .status(DiscoveryStatus.ADDED_TO_SYSTEM)
                                .newHosts(newHostsCount)
                                .newHostIps(newHostIps)
                                .process(c.getDiscovery() == 0 ? "Manual" : "Auto")
                                .lastDiscoveryRunTime(dateTimeUtil.getGMTToEpochTime(String.valueOf(c.getUpdatedTime())))
                                .componentId(c.getMstComponentId())
                                .componentName(c.getMstComponentName())
                                .componentVersionId(c.getMstComponentVersionId())
                                .componentVersionName(c.getComponentVersionName())
                                .commonVersionId(c.getCommonVersionId())
                                .commonVersionName(c.getCommonVersionName())
                                .componentTypeId(c.getMstComponentTypeId())
                                .componentTypeName(c.getMstComponentTypeName())
                                .build();

                        List<IdNamePojo> services = new ArrayList<>();
                        List<IdNamePojo> applications = new ArrayList<>();

                        instClusterMap.getOrDefault(c.getId(), new ArrayList<>()).forEach(clu ->
                                services.addAll(clusterServicesBeanMap
                                        .getOrDefault(clu.getClusterId(), new ArrayList<>())));

                        services.forEach(srv -> applications.addAll(serviceApplicationsBeanMap
                                .getOrDefault(srv.getId(), new ArrayList<>())));

                        getHost.setService(services.stream().distinct().sorted(Comparator.comparingInt(IdNamePojo::getId)).collect(Collectors.toList()));
                        getHost.setApplication(applications.stream().distinct().sorted(Comparator.comparingInt(IdNamePojo::getId)).collect(Collectors.toList()));


                        /*
                         * Get Agent details for each Host
                         */
                        getHost.setMappedAgents(agentBeanMap.getOrDefault(c.getId(), new ArrayList<>())
                                .parallelStream().distinct().map(agentInstance -> AgentDetails.builder()
                                        .agentId(agentInstance.getAgentId())
                                        .agentName(agentInstance.getAgentName())
                                        .agentIdentifier(agentInstance.getUniqueToken())
                                        .physicalAgentId(agentInstance.getPhysicalAgentId())
                                        .physicalAgentIdentifier(agentInstance.getPhysicalAgentIdentifier())
                                        .agentTypeId(agentInstance.getAgentTypeId())
                                        .agentTypeName(agentInstance.getAgentTypeName())
                                        .status(agentInstance.getStatus())
                                        .build()).collect(Collectors.toList()));

                        /*
                         * Get Error log for each Host
                         */
                        getHost.setErrors(hostIdErrorMap.get(getHost.getId()) == null ?
                                hostIdErrorMap.get(getHost.getHostIdentifier()) : hostIdErrorMap.get(getHost.getId()));

                        /*
                         * Get discovered entities for each Host
                         */
                        List<String> discoveredEntities = new ArrayList<>();
                        hostComponentInstanceMap.getOrDefault(c.getId(), new ArrayList<>())
                                .forEach(dis -> {
                                            String monitorPort = instCompAttributesMap.get(dis.getId()) == null ?
                                                    "" : instCompAttributesMap.get(dis.getId()).get(0).getAttributeValue();
                                            String sb = dis.getMstComponentName() + ":" + monitorPort;
                                            discoveredEntities.add(sb);
                                        }
                                );
                        getHost.setDiscoveredEntities(discoveredEntities);
                        return getHost;
                    })
                    .collect(Collectors.toList());
            log.trace("Time taken for getHostList:{} ms", (System.currentTimeMillis() - time));
            return getHostList;
        } catch (Exception e) {
            throw new ControlCenterException("Error while getting HEAL System side host data:" + e);
        }
    }

    private List<GetHost> autoDiscoveryPart(Map<String, String> hostIdErrorMap, Map<String, HostWiseConnections> hostIpAddressWiseConnections,
                                            Map<String, HostWiseConnections> hostIdentifierWiseConnections) throws ControlCenterException {
        try {
            EnvironmentHelper envHelper = new EnvironmentHelper();
            List<InstanceServiceApplicationDetailsPojo> hostBeanList = autoDiscoveryDao.getHostInstanceServiceAppDetails();
            List<InstanceComponentMappingDetails> instCompAttributesDetails = autoDiscoveryDao.getInstCompAttributeMapping();
            /*
             * Create GetHost Details List
             */
            List<GetHost> getHostList = hostBeanList.stream()
                    .map(c -> {
                        HostWiseConnections hostWiseConnections = hostIdentifierWiseConnections.getOrDefault(c.getHostIdentifier(), hostIpAddressWiseConnections.get(c.getHostAddress()));
                        int newHostsCount = hostWiseConnections == null ? 0 : hostWiseConnections.getNoOfConnections();
                        List<String> newHostIps = hostWiseConnections == null ? new ArrayList<>() : new ArrayList<>(Arrays.asList(hostWiseConnections.getListOfRemoteIps().split(",")));

                        return GetHost.builder()
                                .id(String.valueOf(c.getInstanceIdentifier()))
                                .hostName(c.getInstanceName())
                                .hostIdentifier(c.getInstanceIdentifier())
                                .hostAddress(Collections.singletonList(c.getHostAddress()))
                                .environment(envHelper.getOrDefaultEnvironmentName(c.getIsDR()))
                                .status(c.getStatus())
                                .newHosts(newHostsCount)
                                .newHostIps(newHostIps)
                                .process("Auto")
                                .lastDiscoveryRunTime(c.getLastDiscoveryRunTime() == null ? 0 : Long.parseLong(c.getLastDiscoveryRunTime()))
                                .componentId(c.getComponentId())
                                .componentName(c.getComponentName())
                                .componentVersionId(c.getComponentVersionId())
                                .componentVersionName(c.getComponentVersionName())
                                .commonVersionId(c.getCommonVersionId())
                                .commonVersionName(c.getCommonVersionName())
                                .componentTypeId(c.getComponentTypeId())
                                .componentTypeName(c.getComponentTypeName())
                                .service(Collections.singletonList(IdNamePojo.builder()
                                        .id(c.getServiceId())
                                        .name(c.getServiceName())
                                        .identifier(c.getServiceIdentifier())
                                        .build()))
                                .application(Collections.singletonList(IdNamePojo.builder()
                                        .id(c.getApplicationId())
                                        .name(c.getApplicationName())
                                        .identifier(c.getApplicationIdentifier())
                                        .build()))
                                .build();
                    }).sorted(Comparator.comparing(GetHost::getId)).collect(Collectors.toList());

            /*
             * Remove Duplicates
             */
            for (int i = 0; i < getHostList.size() - 1; i++) {
                List<IdNamePojo> temp = new ArrayList<>();
                if (getHostList.get(i).getId().equals(getHostList.get(i + 1).getId())) {
                    if (!getHostList.get(i).getService().containsAll(getHostList.get(i + 1).getService())) {
                        /*
                        Remove Instances on basis of Service duplicates
                         */
                        temp.addAll(getHostList.get(i).getService());
                        temp.addAll(getHostList.get(i + 1).getService());
                        getHostList.get(i).setService(temp);
                        temp = new ArrayList<>();
                    }

                    /*
                    Remove Instances on basis of Application duplicates
                     */
                    if (!getHostList.get(i).getApplication().containsAll(getHostList.get(i + 1).getApplication())) {
                        temp.addAll(getHostList.get(i).getApplication());
                        temp.addAll(getHostList.get(i + 1).getApplication());
                        getHostList.get(i).setApplication(temp);
                    }

                    getHostList.remove(i + 1);
                    i--;
                }
            }

            for (GetHost getHost : getHostList) {
                List<String> discoveredEntities = new ArrayList<>();

                /*
                 * Get Error log for each Host
                 */
                getHost.setErrors(hostIdErrorMap.get(getHost.getId()) == null ?
                        hostIdErrorMap.get(getHost.getHostIdentifier()) : hostIdErrorMap.get(getHost.getId()));

                /*
                 * Get discovered entities for each Host
                 */
                List<String> instanceIdentifier = new ArrayList<>();
                for (InstanceComponentMappingDetails hostInstComponentMappingDetails : instCompAttributesDetails) {

                    if (String.valueOf(hostInstComponentMappingDetails.getHostIdentifier()).equals(getHost.getHostIdentifier())) {
                        if (!instanceIdentifier.contains(hostInstComponentMappingDetails.getCompInstanceIdentifier())
                                && !discoveredEntities.contains(hostInstComponentMappingDetails.getComponentName())) {
                            discoveredEntities.add(hostInstComponentMappingDetails.getComponentName());
                            instanceIdentifier.add(hostInstComponentMappingDetails.getCompInstanceIdentifier());
                        }
                        if (hostInstComponentMappingDetails.getAttributeName().equalsIgnoreCase("MonitorPort")) {
                            discoveredEntities.remove(hostInstComponentMappingDetails.getComponentName());
                            discoveredEntities.add(hostInstComponentMappingDetails.getComponentName() + ":" + hostInstComponentMappingDetails.getAttributeValue());
                        }
                    }
                }
                getHost.setDiscoveredEntities(discoveredEntities);
            }
            return getHostList;
        } catch (Exception e) {
            throw new ControlCenterException("Error while getting AutoDiscovery data:" + e);
        }
    }
}
