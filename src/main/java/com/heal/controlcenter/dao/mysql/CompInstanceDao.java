package com.heal.controlcenter.dao.mysql;


import com.heal.controlcenter.beans.CompInstanceKpiGroupDetailsBean;
import com.heal.controlcenter.beans.ComponentInstanceBean;
import com.heal.controlcenter.beans.KpiMaintenanceStatusBean;
import com.heal.controlcenter.dao.mysql.entity.InstanceKpiAttributeThresholdBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.*;

@Slf4j
@Repository
public class CompInstanceDao {

    @Autowired
    JdbcTemplate jdbcTemplate;

    public Set<Integer> getAgentIdForService(int serviceId, int accountId) {
        String query = "SELECT acim.agent_id agentId FROM component_cluster_mapping c, comp_instance ci, view_cluster_services vcs, agent_comp_instance_mapping acim " +
                "WHERE c.cluster_id = vcs.id and c.comp_instance_id = ci.id and c.comp_instance_id=acim.comp_instance_id and ci.status = 1 " +
                "and vcs.service_id=? and ci.account_id=?";
        try {
            return new HashSet<>(jdbcTemplate.query(query, new BeanPropertyRowMapper<>(Integer.class), serviceId, accountId));
        } catch (Exception e) {
            log.error("Error occurred while fetching agent details for service Id: [{}]. Details: {}", serviceId, e.getMessage());
        }
        return Collections.emptySet();
    }

    public Set<Integer> getAgentIdForServiceFromTagMapping(int serviceId, int accountId) {
        String query = "SELECT distinct object_id FROM tag_mapping tm " +
                "WHERE tm.account_id=? and tm.object_ref_table='agent' and tm.tag_key=?";
        try {
            return new HashSet<>(jdbcTemplate.query(query, new BeanPropertyRowMapper<>(Integer.class), accountId, serviceId));
        } catch (Exception e) {
            log.error("Error occurred while fetching agent details for service Id: [{}]. Details: {}", serviceId, e.getMessage());
        }
        return Collections.emptySet();
    }

    public Map<Integer, String> getCompInstanceIdentifiersFromIds(List<Integer> instanceIds, int accountId) {
        if (instanceIds == null || instanceIds.isEmpty()) {
            log.warn("No instanceIds provided for fetching component instance identifiers.");
            return Collections.emptyMap();
        }
        String inSql = String.join(",", Collections.nCopies(instanceIds.size(), "?"));
        String query = "select id, identifier from comp_instance where id IN (" + inSql + ") and account_id=?";
        try {
            List<Object> params = new java.util.ArrayList<>(instanceIds);
            params.add(accountId);
            return jdbcTemplate.query(query, params.toArray(), rs -> {
                Map<Integer, String> result = new HashMap<>();
                while (rs.next()) {
                    result.put(rs.getInt("id"), rs.getString("identifier"));
                }
                return result;
            });
        } catch (Exception e) {
            log.error("Error occurred while fetching component instance identifiers for instance ids [{}] and account Id: [{}]. Details: {}", instanceIds, accountId, e.getMessage());
        }
        return Collections.emptyMap();
    }

    public List<InstanceKpiAttributeThresholdBean> checkGroupKpiCompInstanceMapping(List<Integer> instanceIds) {
        if (instanceIds == null || instanceIds.isEmpty()) {
            log.warn("No instanceIds provided for group KPI component instance mapping.");
            return Collections.emptyList();
        }
        String inSql = String.join(",", Collections.nCopies(instanceIds.size(), "?"));
        String query = "SELECT comp_instance_id compInstanceId, mst_kpi_group_id kpiGroupId, mst_kpi_details_id kpiId, " +
                "attribute_value attributeValue, is_discovery isDiscovery " +
                "FROM comp_instance_kpi_group_details WHERE comp_instance_id IN (" + inSql + ")";
        try {
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(InstanceKpiAttributeThresholdBean.class), instanceIds.toArray());
        } catch (Exception e) {
            log.error("Error occurred while fetching group KPI component instance mapping for instanceIds [{}]. Details: {}", instanceIds, e.getMessage());
            return Collections.emptyList();
        }
    }

    public List<InstanceKpiAttributeThresholdBean> checkKpiCompInstanceMapping(List<Integer> instanceIds) {
        if (instanceIds == null || instanceIds.isEmpty()) {
            log.warn("No instanceIds provided for KPI component instance mapping.");
            return Collections.emptyList();
        }
        String inSql = String.join(",", Collections.nCopies(instanceIds.size(), "?"));
        String query = "SELECT comp_instance_id compInstanceId, mst_kpi_details_id kpiId " +
                "FROM comp_instance_kpi_details WHERE comp_instance_id IN (" + inSql + ")";
        try {
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(InstanceKpiAttributeThresholdBean.class), instanceIds.toArray());
        } catch (Exception e) {
            log.error("Error occurred while fetching KPI component instance mapping for instanceIds [{}]. Details: {}", instanceIds, e.getMessage());
            return Collections.emptyList();
        }
    }

    public void updateNonGroupInstanceKpiAnomaly(List<KpiMaintenanceStatusBean> beans) {
        if(beans.isEmpty()) {
            log.warn("No beans provided for updating non-group instance KPI anomaly.");
            return;
        }

        String query = "UPDATE comp_instance_kpi_details " +
                "SET notification = ? " +
                "WHERE comp_instance_id = ? AND mst_kpi_details_id = ?";
        try {
            jdbcTemplate.batchUpdate(query, new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps, int i) throws SQLException {
                    KpiMaintenanceStatusBean bean = beans.get(i);
                    ps.setInt(1, bean.getStatus());
                    ps.setInt(2, bean.getCompInstanceId());
                    ps.setInt(3, bean.getKpiId());
                }

                @Override
                public int getBatchSize() {
                    return beans.size();
                }
            });
        } catch (Exception e) {
            log.error("Error occurred while updating non-group instance KPI anomaly. Details: {}", e.getMessage());
        }
    }

    public void updateGroupInstanceKpiAnomaly(List<KpiMaintenanceStatusBean> beans) {
        if(beans.isEmpty()) {
            log.warn("No beans provided for updating group instance KPI anomaly.");
            return;
        }

        String query = "UPDATE comp_instance_kpi_group_details " +
                "SET notification = ? " +
                "WHERE comp_instance_id = ? AND mst_kpi_details_id = ?";
        try {
            jdbcTemplate.batchUpdate(query, new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps, int i) throws SQLException {
                    KpiMaintenanceStatusBean bean = beans.get(i);
                    ps.setInt(1, bean.getStatus());
                    ps.setInt(2, bean.getCompInstanceId());
                    ps.setInt(3, bean.getKpiId());
                }

                @Override
                public int getBatchSize() {
                    return beans.size();
                }
            });
        } catch (Exception e) {
            log.error("Error occurred while updating group instance KPI anomaly. Details: {}", e.getMessage());
        }
    }

    public List<CompInstanceKpiGroupDetailsBean> getNonGroupKpiListForCompInstance(int mstKpiDetailsId) {
        String query = "SELECT comp_instance_id compInstanceId, mst_kpi_details_id mstKpiDetailsId " +
                "FROM comp_instance_kpi_details " +
                "WHERE mst_kpi_details_id = ?";
        try {
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(CompInstanceKpiGroupDetailsBean.class), mstKpiDetailsId);
        } catch (Exception e) {
            log.error("Error occurred while fetching non-group KPI list for mstKpiDetailsId [{}]. Details: {}", mstKpiDetailsId, e.getMessage());
            return Collections.emptyList();
        }
    }

    public List<CompInstanceKpiGroupDetailsBean> getGroupKpiListForCompInstance(int groupKpiId) {
        String query = "SELECT attribute_value attributeValue, comp_instance_id compInstanceId, mst_kpi_details_id mstKpiDetailsId, " +
                "mst_kpi_group_id mstKpiGroupId, mst_producer_kpi_mapping_id mstProducerKpiMappingId, collection_interval collectionInterval, " +
                "kpi_group_name kpiGroupName, mst_producer_id mstProducerId, notification " +
                "FROM comp_instance_kpi_group_details " +
                "WHERE mst_kpi_group_id = ?";
        try {
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(CompInstanceKpiGroupDetailsBean.class), groupKpiId);
        } catch (Exception e) {
            log.error("Error occurred while fetching group KPI list for groupKpiId [{}]. Details: {}", groupKpiId, e.getMessage());
            return Collections.emptyList();
        }
    }

    public int[] addGroupCompInstanceKpiDetails(List<CompInstanceKpiGroupDetailsBean> beans) {
        if (beans == null || beans.isEmpty()) {
            log.warn("No beans provided for adding group component instance KPI details.");
            return new int[0];
        }
        String query = "INSERT INTO comp_instance_kpi_group_details " +
                "(attribute_value, created_time, updated_time, user_details_id, comp_instance_id, " +
                "mst_producer_kpi_mapping_id, collection_interval, mst_kpi_details_id, is_discovery, " +
                "kpi_group_name, mst_kpi_group_id, mst_producer_id, alias_name) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        try {
            return jdbcTemplate.batchUpdate(query, new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps, int i) throws SQLException {
                    CompInstanceKpiGroupDetailsBean bean = beans.get(i);
                    ps.setString(1, bean.getAttributeValue());
                    ps.setString(2, bean.getCreatedTime());
                    ps.setString(3, bean.getUpdatedTime());
                    ps.setString(4, bean.getUserDetailsId());
                    ps.setInt(5, bean.getCompInstanceId());
                    ps.setInt(6, bean.getMstProducerKpiMappingId());
                    ps.setInt(7, bean.getCollectionInterval());
                    ps.setInt(8, bean.getMstKpiDetailsId());
                    ps.setInt(9, bean.getIsDiscovery());
                    ps.setString(10, bean.getKpiGroupName());
                    ps.setInt(11, bean.getMstKpiGroupId());
                    ps.setInt(12, bean.getMstProducerId());
                    ps.setString(13, bean.getAliasName());
                }
                @Override
                public int getBatchSize() {
                    return beans.size();
                }
            });
        } catch (Exception e) {
            log.error("Error occurred while adding group component instance KPI details in bulk. Details: {}", e.getMessage());
            return new int[0];
        }
    }

    public List<ComponentInstanceBean> getComponentInstancesByIdsAndAccountId(List<Integer> ids, int accountId) {
        if (ids == null || ids.isEmpty()) {
            log.warn("No ids provided for fetching component instances.");
            return Collections.emptyList();
        }
        String inSql = String.join(",", Collections.nCopies(ids.size(), "?"));
        String query = "SELECT id, name, status, identifier, host_id AS hostId, is_dr AS isDR, is_cluster AS isCluster, " +
                "mst_component_version_id AS mstComponentVersionId, created_time AS createdTime, updated_time AS updatedTime, " +
                "user_details_id AS userDetailsId, account_id AS accountId, mst_component_id AS mstComponentId, " +
                "mst_component_type_id AS mstComponentTypeId, discovery, host_address AS hostAddress, " +
                "identifier, mst_common_version_id AS mstCommonVersionId " +
                "FROM comp_instance WHERE id IN (" + inSql + ") AND account_id = ?";
        List<Object> params = new ArrayList<>(ids);
        params.add(accountId);
        try {
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(ComponentInstanceBean.class), params.toArray());
        } catch (Exception e) {
            log.error("Error occurred while fetching component instances for ids [{}] and accountId [{}]. Details: {}", ids, accountId, e.getMessage());
            return Collections.emptyList();
        }
    }
}
