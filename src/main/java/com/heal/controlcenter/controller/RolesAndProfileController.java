package com.heal.controlcenter.controller;

import com.heal.controlcenter.beans.IdBean;
import com.heal.controlcenter.businesslogic.UserProfilesByRoleIdBL;
import com.heal.controlcenter.businesslogic.UserRoleBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.pojo.ResponsePojo;
import com.heal.controlcenter.swagger.CommonApiResponses;
import com.heal.controlcenter.util.JsonFileParser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@Configuration
public class RolesAndProfileController {

    @Autowired
    UserRoleBL userRoleBL;
    @Autowired
    JsonFileParser headersParser;
    @Autowired
    UserProfilesByRoleIdBL userProfilesByRoleIdBL;

    @Operation(
            summary = "Retrieves all user roles in the system.",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "User roles fetched successfully.",
                            content = @Content(schema = @Schema(implementation = IdBean.class))
                    )
            }
    )
    @CommonApiResponses
    @RequestMapping(value = "/roles", method = RequestMethod.GET)
    public ResponseEntity<ResponsePojo<List<IdBean>>> getUserRoles(
            @Parameter(
                    name = "Authorization",
                    description = "Bearer token for authentication",
                    required = true,
                    example = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            )
            @RequestHeader(value = "Authorization") String authorization)
            throws ClientException, DataProcessingException {
        userRoleBL.clientValidation(null, authorization);
        List<IdBean> listOfUserRoles = userRoleBL.process("User roles");
        ResponsePojo<List<IdBean>> responsePojo = new ResponsePojo<>("User roles fetched successfully", listOfUserRoles, HttpStatus.OK);
        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
    }

    @Operation(
            summary = "Retrieves user profiles associated with a specific role.",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "User profiles fetched successfully.",
                            content = @Content(schema = @Schema(implementation = IdBean.class))
                    )
            }
    )
    @CommonApiResponses
    @RequestMapping(value = "/roles/{roleId}/profiles", method = RequestMethod.GET)
    public ResponseEntity<ResponsePojo<List<IdBean>>> getProfilesByRoleId(
            @Parameter(
                    name = "Authorization",
                    description = "Bearer token for authentication",
                    required = true,
                    example = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            )
            @RequestHeader(value = "Authorization") String authorization,
            @Parameter(
                    name = "roleId",
                    description = "Unique identifier of the role",
                    required = true,
                    example = "1"
            )
            @PathVariable(value = "roleId") Long id)
            throws ClientException, DataProcessingException {
        userProfilesByRoleIdBL.clientValidation(authorization);
        List<IdBean> listOfProfilesByRoleId = userProfilesByRoleIdBL.process(id);
        ResponsePojo<List<IdBean>> responseBean = new ResponsePojo<>("User profiles fetched successfully", listOfProfilesByRoleId, HttpStatus.OK);
        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responseBean);
    }
}
