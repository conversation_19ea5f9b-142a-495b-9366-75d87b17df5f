package com.heal.controlcenter.businesslogic;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.dao.mysql.TagsDao;
import com.heal.controlcenter.dao.mysql.UserDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.Account;
import com.heal.controlcenter.pojo.Tags;
import com.heal.controlcenter.pojo.ThresholdSeverity;
import com.heal.controlcenter.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * Business logic class for handling account retrieval operations.
 * Implements validation, authorization, and data transformation logic.
 */
@Slf4j
@Component
public class GetAccountsBL implements BusinessLogic<Account, UtilityBean<AccessDetailsBean>, Page<Account>> {

    @Autowired
    TagsDao tagsDao;
    @Autowired
    UserDao userDao;
    @Autowired
    AccountsDao accountDao;
    @Autowired
    ObjectMapper objectMapper;
    @Autowired
    KeyCloakAuthService keyCloakAuthService;
    @Autowired
    ClientValidationUtils clientValidationUtils;
    @Autowired
    ServerValidationUtils serverValidationUtils;

    /**
     * Performs client-side validation for the Get Accounts request.
     * This method validates only the authorization key.
     *
     * @param requestBody   The {@link Account} object, not used directly for validation in this case.
     * @param requestParams Varargs containing the authorization token as the first parameter.
     * @return A {@link UtilityBean} containing request parameters (like the auth key) for further processing.
     * @throws ClientException If the authorization key is missing or invalid.
     */
    @Override
    public UtilityBean<Account> clientValidation(Account requestBody, String... requestParams) throws ClientException {
        String authKey = requestParams[0];
        clientValidationUtils.authKeyValidation(authKey);

        HashMap<String, String> requestParamsMap = CommonUtils.buildRequestParams(authKey, null);

        return UtilityBean.<Account>builder()
                .requestParams(requestParamsMap)
                .build();
    }

    /**
     * Performs server-side validation by extracting and validating user access context.
     * The Pageable and SearchTerm are expected to be present in the incoming utilityBean's dedicated fields.
     *
     * @param utilityBean The {@link UtilityBean} generated during client validation (and enriched by controller),
     *                    containing request parameters, metadata, and now pageable/searchTerm fields.
     * @return A {@link UtilityBean} enriched with the user's access context, user ID metadata, and copied pageable/searchTerm.
     * @throws ServerException If the user ID is invalid, or if the access details cannot be fetched or parsed.
     */
    @Override
    public UtilityBean<AccessDetailsBean> serverValidation(UtilityBean<Account> utilityBean) throws ServerException {
        String authKey = utilityBean.getRequestParams().get(Constants.AUTH_KEY);
        String userId = serverValidationUtils.authKeyValidation(authKey);

        UserAccessBean accessDetails = userDao.fetchUserAccessDetailsUsingIdentifier(userId);

        AccessDetailsBean accessDetailsBean;
        try {
            accessDetailsBean = objectMapper.readValue(accessDetails.getAccessDetails(), AccessDetailsBean.class);
        } catch (Exception e) {
            log.error("Error occurred while mapping userAccessDetails Json to java object of AccessDetailsBean class");
            throw new ServerException(UIMessages.USER_ACCESS_DETAILS_NOT_FOUND);
        }

        if (accessDetailsBean == null) {
            log.error(UIMessages.INVALID_USER_ACCESS_DETAILS);
            throw new ServerException(UIMessages.INVALID_USER_ACCESS_DETAILS);
        }

        return UtilityBean.<AccessDetailsBean>builder()
                .pojoObject(accessDetailsBean)
                .metadata(Map.of(Constants.USER_ID_KEY, userId))
                .pageable(utilityBean.getPageable())
                .searchTerm(utilityBean.getSearchTerm())
                .build();
    }

    /**
     * Processes the business logic to retrieve paginated and filtered accounts.
     *
     * <p>This method:
     * <ul>
     * <li>Fetches all accessible accounts based on user access details.</li>
     * <li>Filters accounts based on the user's access permissions.</li>
     * <li>Retrieves timezone and anomaly configuration for each account.</li>
     * <li>Maps account data to {@link Account} objects with necessary transformations.</li>
     * <li>Applies filtering, sorting, and pagination to the final list of accounts using a common utility.</li>
     * </ul>
     *
     * @param utilityBean The {@link UtilityBean} containing access details and now direct Pageable and searchTerm fields.
     * @return A {@link Page} object containing the processed list of {@link Account} objects.
     * @throws DataProcessingException If any error occurs during data processing or fetching accounts.
     */
    @Override
    public Page<Account> process(UtilityBean<AccessDetailsBean> utilityBean) throws DataProcessingException {
        AccessDetailsBean bean = utilityBean.getPojoObject();
        String userId = (String) utilityBean.getMetadata().get(Constants.USER_ID_KEY);

        Pageable pageable = utilityBean.getPageable();
        String searchTerm = utilityBean.getSearchTerm();

        if (pageable == null) {
            log.warn("Pageable object is null in UtilityBean. Defaulting to PageRequest.of(0, 10). This usually means it was not provided in the request or set correctly.");
            pageable = org.springframework.data.domain.PageRequest.of(0, 10);
        }
        if (searchTerm == null) {
            log.warn("Search term is null in UtilityBean. Defaulting to empty string.");
            searchTerm = "";
        }
        log.info("Processing accounts with Pageable: {} and SearchTerm: '{}'", pageable, searchTerm);

        try {
            List<AccountBean> accessibleAccounts = accountDao.getAccounts();

            if (Objects.nonNull(accessibleAccounts) && !bean.getAccounts().contains("*")) {
                accessibleAccounts = accessibleAccounts.parallelStream()
                        .filter(acc -> bean.getAccounts().contains(acc.getIdentifier()))
                        .toList();
            }

            List<Account> accountsList = new ArrayList<>();

            for (AccountBean accBean : accessibleAccounts) {
                TimezoneBean timezoneBean;
                try {
                    timezoneBean = accountDao.getAccountTimezoneDetails(accBean.getId());
                } catch (ControlCenterException e) {
                    log.warn("Could not fetch timezone for account ID {}. Skipping this account for now.", accBean.getId(), e);
                    continue;
                }
                if (timezoneBean == null) {
                    log.warn("Timezone details are null for account ID {}. Skipping this account for now.", accBean.getId());
                    continue;
                }

                AccountAnomalyConfigurationBean config;
                try {
                    config = accountDao.getAccountAnomalyConfiguration(accBean.getId());
                } catch (ControlCenterException e) {
                    log.warn("Could not fetch anomaly configuration for account ID {}. Continuing with defaults.", accBean.getId(), e);
                    config = null;
                }

                List<Tags> tagsList = tagsDao.getTagsByObjectId(accBean.getId(), "account")
                        .stream()
                        .map(tag -> new Tags(tag.getType(), tag.getKey()))
                        .collect(Collectors.toList());

                ThresholdSeverity thresholdSeverity = new ThresholdSeverity(
                        config != null && config.isLowEnable(),
                        config != null && config.isMediumEnable(),
                        config != null && config.isHighEnable()
                );

                Account account = Account.builder()
                        .accountId(accBean.getId())
                        .accountName(accBean.getName())
                        .identifier(accBean.getIdentifier())
                        .privateKey(accBean.getPrivateKey())
                        .publicKey(accBean.getPublicKey())
                        .updatedTime(accBean.getUpdatedTime() != null
                                ? Timestamp.valueOf(accBean.getUpdatedTime()).getTime()
                                : null)
                        .timezoneMilli(timezoneBean.getOffset())
                        .timeZoneString(timezoneBean.getTimeZoneId())
                        .status(accBean.getStatus())
                        .dateFormat("YYYY-MM-DD")
                        .timeFormat("HH:mm")
                        .tags(tagsList)
                        .closingWindow(config != null ? config.getClosingWindow() : 0)
                        .maxDataBreaks(config != null ? config.getMaxDataBreaks() : 0)
                        .thresholdSeverity(thresholdSeverity)
                        .build();

                account.getUserNameFromIdentifier(accBean.getLastModifiedBy(), keyCloakAuthService);
                accountsList.add(account);
            }

            final String lowerCaseSearchTerm = searchTerm.trim().toLowerCase();
            Predicate<Account> accountFilterPredicate = account ->
                    (account.getAccountName() != null && account.getAccountName().toLowerCase().contains(lowerCaseSearchTerm)) ||
                            (account.getIdentifier() != null && account.getIdentifier().toLowerCase().contains(lowerCaseSearchTerm));

            Map<String, Comparator<Account>> accountSortComparators = new HashMap<>();
            accountSortComparators.put("accountName", Comparator.comparing(Account::getAccountName, Comparator.nullsLast(String.CASE_INSENSITIVE_ORDER)));

            return PaginationSortingFilteringUtils.applyPaginationSortingFiltering(
                    accountsList,
                    pageable,
                    searchTerm,
                    accountFilterPredicate,
                    accountSortComparators
            );

        } catch (ControlCenterException e) {
            log.error("Error in fetching accounts list for user: {}", userId, e);
            throw new DataProcessingException("Error in fetching accounts details: " + e.getMessage());
        } catch (Exception e) {
            log.error("An unexpected error occurred during account processing for user: {}", userId, e);
            throw new DataProcessingException("An internal error occurred while processing accounts.");
        }
    }
}