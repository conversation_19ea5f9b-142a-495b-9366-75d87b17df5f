package com.heal.controlcenter.businesslogic;

import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.dao.mysql.*;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.AgentCommandsPojo;
import com.heal.controlcenter.util.CommonUtils;
import com.heal.controlcenter.util.DateTimeUtil;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.heal.controlcenter.util.Constants.AGENT_TYPE;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@PrepareForTest({GetAgentCommandsBL.class})
class GetAgentCommandsBLTest {

    @InjectMocks
    GetAgentCommandsBL getAgentCommandsBL;

    @Mock
    CommonUtils commonUtils;
    @Mock
    AccountsDao accountsDao;
    @Mock
    ControllerDao controllerDao;
    @Mock
    MasterDataDao masterDataDao;
    @Mock
    CommandDataDao commandDataDao;
    @Mock
    AgentDao agentDao;
    @Mock
    DateTimeUtil dateTimeUtil;

    String[] requestParams = new String[4];
    UtilityBean<List<String>> mockUtilityBean = null;
    AccountBean account = new AccountBean();
    ViewTypesBean jimAgentType = new ViewTypesBean();
    List<CommandDetailsBean> commandDetails = new ArrayList<>();
    AgentModeConfigBean agentModeConfigBean = new AgentModeConfigBean();
    List<CommandArgumentBean> commandArgumentBeans = new ArrayList<>();

    @BeforeEach
    void setUp() throws ControlCenterException {
        account.setId(1);

        CommandDetailsBean commandDetailsBean = new CommandDetailsBean();
        commandDetailsBean.setId(5);
        commandDetailsBean.setName("Auto");
        commandDetailsBean.setIdentifier("JIMAutoModeDetails");
        commandDetails.add(commandDetailsBean);

        CommandArgumentBean commandArgumentBean = new CommandArgumentBean();
        commandArgumentBean.setValue("0");
        commandArgumentBean.setDefaultValue("0");
        commandArgumentBean.setArgumentKey("snapshot-count");
        commandArgumentBean.setCommandId(10);
        commandArgumentBeans.add(commandArgumentBean);
        commandArgumentBean = new CommandArgumentBean();
        commandArgumentBean.setValue("0");
        commandArgumentBean.setDefaultValue("0");
        commandArgumentBean.setArgumentKey("snapshot-duration");
        commandArgumentBean.setCommandId(11);
        commandArgumentBeans.add(commandArgumentBean);
        commandArgumentBean = new CommandArgumentBean();
        commandArgumentBean.setValue("false");
        commandArgumentBean.setDefaultValue("false");
        commandArgumentBean.setArgumentKey("exceptions");
        commandArgumentBean.setCommandId(12);
        commandArgumentBeans.add(commandArgumentBean);
        commandArgumentBean = new CommandArgumentBean();
        commandArgumentBean.setValue("Medium");
        commandArgumentBean.setDefaultValue("Medium");
        commandArgumentBean.setArgumentKey("collection-mode");
        commandArgumentBean.setCommandId(9);
        commandArgumentBeans.add(commandArgumentBean);
        commandArgumentBean = new CommandArgumentBean();
        commandArgumentBean.setValue("90");
        commandArgumentBean.setDefaultValue("90");
        commandArgumentBean.setArgumentKey("jvm_cpu");
        commandArgumentBean.setCommandId(16);
        commandArgumentBeans.add(commandArgumentBean);
        commandArgumentBean = new CommandArgumentBean();
        commandArgumentBean.setValue("90");
        commandArgumentBean.setDefaultValue("90");
        commandArgumentBean.setArgumentKey("jvm_mem");
        commandArgumentBean.setCommandId(17);
        commandArgumentBeans.add(commandArgumentBean);

        jimAgentType.setSubTypeName("JIMAgent");
        jimAgentType.setTypeName("Agent");
        jimAgentType.setTypeId(1);
        jimAgentType.setSubTypeId(2);

        requestParams[0] = "mockAuthToken";
        requestParams[1] = "mockAccountIdentifier";
        requestParams[2] = "66";
        requestParams[3] = "JIMAgent";

        List<String> pojoObject = new ArrayList<>();
        pojoObject.add(requestParams[2]);
        pojoObject.add(requestParams[3]);

        mockUtilityBean = UtilityBean.<List<String>>builder()
                .pojoObject(pojoObject)
                .account(account)
                .requestParams(Map.of(
                        "Authorization", "mockAuthToken",
                        "accountIdentifier", "mockAccountIdentifier"
                ))
                .metadata(Map.of(
                        "userId", "mockUserId"
                ))
                .build();

        lenient().when(masterDataDao.getViewTypesFromMstTypeAndSubTypeName(AGENT_TYPE, mockUtilityBean.getPojoObject().get(1))).thenReturn(jimAgentType);
    }

    @AfterEach
    void tearDown() {
        requestParams = null;
        commandDetails = null;
        commandArgumentBeans = null;
    }

    @Test
    void clientValidations_EmptyAuthorizationToken() {
        requestParams[0] = "";
        requestParams[1] = "mockAccountIdentifier";

        String expectedMessage = "ClientException : Invalid authorization token";
        ClientException requestException = assertThrows(ClientException.class, () ->
                getAgentCommandsBL.clientValidation(null, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidations_EmptyAccountIdentifier() {
        requestParams[0] = "mockAuthToken";
        requestParams[1] = "";

        String expectedMessage = "ClientException : Invalid account identifier";
        ClientException requestException = assertThrows(ClientException.class, () ->
                getAgentCommandsBL.clientValidation(null, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidations_ErrorWhileFetchingUserId() throws Exception {
        String expectedMessage = "ClientException : Error occurred while fetching userId from authorization token";
        when(commonUtils.getUserId("mockAuthToken")).thenThrow(ControlCenterException.class);
        ClientException requestException = assertThrows(ClientException.class, () ->
                getAgentCommandsBL.clientValidation(null, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidations_AuthKeyInvalid() throws Exception {
        String expectedMessage = "ClientException : Error occurred while fetching userId from authorization token";
        when(commonUtils.getUserId("mockAuthToken")).thenReturn(any());
        ClientException requestException = assertThrows(ClientException.class, () ->
                getAgentCommandsBL.clientValidation(null, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidations_Success() throws Exception {
        String userId = "mockUserId";
        when(commonUtils.getUserId("mockAuthToken")).thenReturn(userId);
        UtilityBean<List<String>> utilityBean = getAgentCommandsBL.clientValidation(null, requestParams);
        assertEquals(utilityBean.getMetadata().get("userId"), userId);
    }

    @Test
    void serverValidation_Success() throws Exception {
        when(accountsDao.getAccountByIdentifier("mockAccountIdentifier")).thenReturn(account);
        when(controllerDao.getServiceById(anyInt(), anyInt())).thenReturn(new ControllerBean());
        getAgentCommandsBL.serverValidation(mockUtilityBean);
    }

    @Test
    void serverValidation_InvalidAccountIdentifier() {
        String expectedMessage = "ServerException : Account with identifier [mockAccountIdentifier] is unavailable";
        when(accountsDao.getAccountByIdentifier("mockAccountIdentifier")).thenReturn(any());
        ServerException requestException = assertThrows(ServerException.class, () ->
                getAgentCommandsBL.serverValidation(mockUtilityBean));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void serverValidation_InvalidServiceId() {
        String expectedMessage = "ServerException : ServiceId [66] is unavailable for account [mockAccountIdentifier]";
        when(accountsDao.getAccountByIdentifier("mockAccountIdentifier")).thenReturn(account);
        when(controllerDao.getServiceById(anyInt(), anyInt())).thenReturn(any());
        ServerException requestException = assertThrows(ServerException.class, () ->
                getAgentCommandsBL.serverValidation(mockUtilityBean));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void process_Success() throws Exception {
        when(commandDataDao.getCommandDetailsByAgentType(anyInt())).thenReturn(commandDetails);
        when(agentDao.getAgentModeConfig(anyInt(), anyInt(), anyInt())).thenReturn(agentModeConfigBean);
        when(agentDao.getServiceCommandArguments(anyInt(), anyInt(), anyInt())).thenReturn(commandArgumentBeans);
        when(dateTimeUtil.getCurrentTimestampInGMT()).thenReturn(Timestamp.valueOf("2021-12-02 02:02:02"));

        AgentCommandsPojo data = getAgentCommandsBL.process(mockUtilityBean);
        assertEquals("JIMAgent", data.getCommandName());
    }

    @Test
    void process_Success_Null_AgentModeConfig() throws Exception {
        when(commandDataDao.getCommandDetailsByAgentType(anyInt())).thenReturn(commandDetails);
        when(agentDao.getAgentModeConfig(anyInt(), anyInt(), anyInt())).thenReturn(null);
        when(agentDao.getServiceCommandArguments(anyInt(), anyInt(), anyInt())).thenReturn(commandArgumentBeans);
        when(dateTimeUtil.getCurrentTimestampInGMT()).thenReturn(Timestamp.valueOf("2021-12-02 02:02:02"));

        AgentCommandsPojo data = getAgentCommandsBL.process(mockUtilityBean);
        assertEquals("JIMAgent", data.getCommandName());
    }

    @Test
    void process_failure_NoCommands() {
        List<CommandDetailsBean> commandDetailsBeans = new ArrayList<>();

        String expectedMessage = "DataProcessingException : No commands found to process the request.";
        when(commandDataDao.getCommandDetailsByAgentType(anyInt())).thenReturn(commandDetailsBeans);
        DataProcessingException requestException = assertThrows(DataProcessingException.class, () ->
                getAgentCommandsBL.process(mockUtilityBean));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }
}
