//package com.heal.controlcenter.businesslogic;
//
//import com.heal.configuration.pojos.*;
//import com.heal.controlcenter.beans.*;
//import com.heal.controlcenter.dao.mysql.AccountsDao;
//import com.heal.controlcenter.dao.mysql.ControllerDao;
//import com.heal.controlcenter.dao.mysql.ServiceConfigurationDao;
//import com.heal.controlcenter.dao.redis.ServiceRepo;
//import com.heal.controlcenter.exception.ClientException;
//import com.heal.controlcenter.exception.ControlCenterException;
//import com.heal.controlcenter.exception.ServerException;
//import com.heal.controlcenter.pojo.AccountServiceKey;
//import com.heal.controlcenter.pojo.ServiceSuppPersistenceConfigPojo;
//import com.heal.controlcenter.util.*;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.mockito.*;
//
//import java.util.*;
//
//import static org.junit.jupiter.api.Assertions.*;
//import static org.mockito.Mockito.*;
//
//class UpdateServiceSuppPersistenceBLTest {
//
//    @InjectMocks
//    private UpdateServiceAnomalyConfigBL updateServiceAnomalyBL;
//
//    @Mock
//    private CommonUtils commonUtils;
//
//    @Mock
//    private AccountsDao accountsDao;
//
//    @Mock
//    private ControllerDao controllerDao;
//
//    @Mock
//    private UserValidationUtil userValidationUtil;
//
//    @Mock
//    private ServiceConfigurationDao serviceConfigurationDao;
//
//    @Mock
//    private ServiceRepo serviceRepo;
//
//    @Mock
//    private DateTimeUtil dateTimeUtil;
//
//    @Spy
//    private ClientValidationUtils clientValidationUtils;
//
//    @Mock
//    private ServerValidationUtils serverValidationUtils;
//
//    public static final String REASON_IT_IS_EITHER_NULL_OR_EMPTY = " Reason: It is either NULL or empty";
//
//    @BeforeEach
//    void setUp() {
//        MockitoAnnotations.openMocks(this);
//    }
//
//    @Test
//    void clientValidation_NullAuthKey_ThrowsClientException() {
//        Map<String, ServiceSuppPersistenceConfigPojo> serviceConfigDetails = new HashMap<>();
//        String[] params = {null, "accountIdentifier", "serviceIdentifier"};
//
//        ClientException exception = assertThrows(ClientException.class, () ->
//                updateServiceAnomalyBL.clientValidation(serviceConfigDetails, params));
//        assertEquals("ClientException : " + UIMessages.AUTH_KEY_INVALID, exception.getMessage());
//    }
//
//    @Test
//    void clientValidation_EmptyAccountIdentifier_ThrowsClientException() {
//        Map<String, ServiceSuppPersistenceConfigPojo> serviceConfigDetails = new HashMap<>();
//        String[] params = {"authKey", "", "serviceIdentifier"};
//
//        ClientException exception = assertThrows(ClientException.class, () ->
//                updateServiceAnomalyBL.clientValidation(serviceConfigDetails, params));
//        assertEquals("ClientException : " + UIMessages.ACCOUNT_IDENTIFIER_INVALID, exception.getMessage());
//    }
//
//    @Test
//    void clientValidation_EmptyServiceIdentifier_ThrowsClientException() {
//        Map<String, ServiceSuppPersistenceConfigPojo> serviceConfigDetails = new HashMap<>();
//        String[] params = {"authKey", "accountIdentifier", ""};
//
//        ClientException exception = assertThrows(ClientException.class, () ->
//                updateServiceAnomalyBL.clientValidation(serviceConfigDetails, params));
//        assertEquals("ClientException : " + UIMessages.SERVICE_IDENTIFIER_INVALID, exception.getMessage());
//    }
//
//    @Test
//    void clientValidation_EmptyServiceConfigDetails_ThrowsClientException() throws Exception {
//        Map<String, ServiceSuppPersistenceConfigPojo> serviceConfigDetails = new HashMap<>();
//        String[] params = {"authKey", "accountIdentifier", "serviceIdentifier"};
//        when(commonUtils.getUserId("authKey")).thenReturn("userId");
//
//        ClientException exception = assertThrows(ClientException.class, () ->
//                updateServiceAnomalyBL.clientValidation(serviceConfigDetails, params));
//        assertEquals("ClientException : Request object should contain service configuration details.", exception.getMessage());
//    }
//
//    @Test
//    void clientValidation_ValidInput_ReturnsUtilityBean() throws Exception {
//        Map<String, ServiceSuppPersistenceConfigPojo> serviceConfigDetails = new HashMap<>();
//        serviceConfigDetails.put("key", new ServiceSuppPersistenceConfigPojo());
//        String[] params = {"authKey", "accountIdentifier", "serviceIdentifier"};
//
//        UtilityBean<Map<String, ServiceSuppPersistenceConfigPojo>> result =
//                updateServiceAnomalyBL.clientValidation(serviceConfigDetails, params);
//
//        assertNotNull(result);
//        assertEquals(serviceConfigDetails, result.getPojoObject());
//    }
//
//    @Test
//    void serverValidation_ValidInput_ReturnsAccountServiceKey() throws ServerException {
//        HashMap<String, String> requestParamsMap = new HashMap<>();
//        requestParamsMap.put(Constants.AUTH_KEY, "authKey");
//        requestParamsMap.put(Constants.ACCOUNT_IDENTIFIER, "accountIdentifier");
//        requestParamsMap.put(Constants.SERVICE_IDENTIFIER, "serviceIdentifier");
//
//        Account account = new Account();
//        account.setId(1);
//        account.setIdentifier("accountIdentifier");
//
//        when(serverValidationUtils.authKeyValidation(Mockito.anyString())).thenReturn("testUser");
//        when(serverValidationUtils.accountValidation(Mockito.anyString())).thenReturn(account);
//        when(serverValidationUtils.userAccessDetailsValidation(Mockito.anyString(), any())).thenReturn(new com.heal.configuration.entities.UserAccessDetails());
//        when(serverValidationUtils.serviceValidation(anyString(), anyString(), anyString(), any())).thenReturn(Service.builder().id(1).build());
//
//        List<ServiceSuppPersistenceConfigurationBean> serviceConfigList = new ArrayList<>();
//        ServiceSuppPersistenceConfigurationBean newBean = new ServiceSuppPersistenceConfigurationBean();
//        newBean.setId(2);
//        serviceConfigList.add(newBean);
//        when(serviceConfigurationDao.getServiceConfiguration(anyInt(), anyInt())).thenReturn(serviceConfigList);
//
//        Map<String, ServiceSuppPersistenceConfigPojo> serviceConfigDetails = new HashMap<>();
//        ServiceSuppPersistenceConfigPojo configPojo = new ServiceSuppPersistenceConfigPojo();
//        configPojo.setServiceConfigId(2);
//        serviceConfigDetails.put("key", configPojo);
//        UtilityBean<Map<String, ServiceSuppPersistenceConfigPojo>> result = updateServiceAnomalyBL.serverValidation(UtilityBean.<Map<String, ServiceSuppPersistenceConfigPojo>>builder()
//                .userId("userId")
//                .pojoObject(serviceConfigDetails)
//                .requestParams(requestParamsMap)
//                .build());
//
//        assertNotNull(result);
//        assertEquals("serviceIdentifier", result.getRequestParams().get(Constants.SERVICE_IDENTIFIER));
//    }
//
//    @Test
//    void process_ValidInput_Success() throws Exception {
//        HashMap<String, String> requestParamsMap = new HashMap<>();
//        requestParamsMap.put(Constants.AUTH_KEY, "authKey");
//        requestParamsMap.put(Constants.ACCOUNT_IDENTIFIER, "accountIdentifier");
//        requestParamsMap.put(Constants.SERVICE_IDENTIFIER, "serviceIdentifier");
//        requestParamsMap.put(Constants.SERVICE_ID, "2");
//        requestParamsMap.put(Constants.ACCOUNT_ID, "1");
//
//        Account accountBean = new Account();
//        accountBean.setIdentifier("accountIdentifier");
//        AccountServiceKey key = new AccountServiceKey(accountBean, "authKey", 2);
//        Map<String, ServiceSuppPersistenceConfigPojo> serviceConfigDetailsMap = new HashMap<>();
//        ServiceSuppPersistenceConfigPojo configPojo = new ServiceSuppPersistenceConfigPojo();
//        configPojo.setServiceConfigId(1);
//        configPojo.setLowPersistence(10);
//        configPojo.setMediumPersistence(20);
//        configPojo.setHighPersistence(30);
//        configPojo.setLowSuppression(40);
//        configPojo.setMediumSuppression(50);
//        configPojo.setHighSuppression(60);
//        configPojo.setLowEnable(true);
//        configPojo.setMediumEnable(true);
//        configPojo.setHighEnable(true);
//        serviceConfigDetailsMap.put(Constants.OPERATOR_LESS_THAN, configPojo);
//        serviceConfigDetailsMap.put(Constants.OPERATOR_GREATER_THAN, configPojo);
//        BasicEntity serviceEntity = new BasicEntity();
//        serviceEntity.setId(2);
//        serviceEntity.setIdentifier("serviceIdentifier");
//
//        Service service = new Service();
//        service.setId(2);
//        ServiceConfiguration serviceConfiguration = new ServiceConfiguration();
//        AnomalyConfiguration anomalyConfiguration = new AnomalyConfiguration();
//        anomalyConfiguration.setEndCollectionInterval(58);
//        serviceConfiguration.setAnomalyConfiguration(anomalyConfiguration);
//
//        ServiceConfiguration serviceConfiguration2 = new ServiceConfiguration();
//        AnomalyConfiguration anomalyConfiguration2 = new AnomalyConfiguration();
//        anomalyConfiguration2.setEndCollectionInterval(80);
//        serviceConfiguration2.setAnomalyConfiguration(anomalyConfiguration2);
//
//        service.setServiceConfigurations(Arrays.asList(serviceConfiguration, serviceConfiguration2));
//
//        when(commonUtils.getUserId("authKey")).thenReturn("userId");
//        when(dateTimeUtil.getTimeInGMT(anyLong())).thenReturn(String.valueOf(System.currentTimeMillis()));
//        when(serviceRepo.getAllServicesDetails(anyString())).thenReturn(Collections.singletonList(serviceEntity));
//        when(serviceRepo.getServiceConfigurationByIdentifier(anyString(), anyString())).thenReturn(service);
//        when(serviceRepo.getAllServicesDetails(anyString())).thenReturn(Arrays.asList(serviceEntity));
//
//        assertDoesNotThrow(() -> updateServiceAnomalyBL.process(UtilityBean.<Map<String, ServiceSuppPersistenceConfigPojo>>builder()
//                .userId("userId")
//                .pojoObject(serviceConfigDetailsMap)
//                .requestParams(requestParamsMap)
//                .build()));
//
//        verify(serviceConfigurationDao, times(1)).updateServiceSuppPersistenceConfig(anyList());
//    }
//
//}
