package com.heal.controlcenter.businesslogic;

import com.heal.configuration.entities.UserAccessDetails;
import com.heal.configuration.pojos.*;
import com.heal.configuration.pojos.opensearch.ServiceKpiThresholds;
import com.heal.controlcenter.beans.CompInstClusterDetailsBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.utility.ThresholdSyncService;
import com.heal.controlcenter.dao.mysql.KPIDao;
import com.heal.controlcenter.dao.mysql.ServiceDao;
import com.heal.controlcenter.dao.mysql.entity.KpiDetailsBean;
import com.heal.controlcenter.dao.mysql.entity.ServiceKpiThreshold;
import com.heal.controlcenter.dao.opensearch.ServiceOpensearchRepo;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.OperationTypeEnum;
import com.heal.controlcenter.pojo.StaticThresholdRules;
import com.heal.controlcenter.pojo.ThresholdType;
import com.heal.controlcenter.service.CommonDataService;
import com.heal.controlcenter.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class PostServiceStaticThresholdBL implements BusinessLogic<List<StaticThresholdRules>, UtilityBean<List<StaticThresholdRules>>, List<StaticThresholdRules>> {
    @Autowired
    CommonUtils commonUtils;
    @Autowired
    ServiceDao serviceDao;
    @Autowired
    DateTimeUtil dateTimeUtil;
    @Autowired
    ServiceOpensearchRepo serviceOpensearchRepoOpenSearch;
    @Autowired
    CacheWrapper cacheWrapper;
    @Autowired
    ClientValidationUtils clientValidationUtils;
    @Autowired
    ServerValidationUtils serverValidationUtils;
    @Autowired
    CommonDataService commonDataService;
    @Autowired
    KPIDao kpiDao;
    @Autowired
    ThresholdSyncService thresholdSyncService;

    @Override
    public UtilityBean<List<StaticThresholdRules>> clientValidation(List<StaticThresholdRules> sorRuleList, String... requestParams) throws ClientException {
        String authKey = requestParams[0];
        clientValidationUtils.nullOrEmptyCheck(authKey, UIMessages.AUTH_KEY_INVALID);

        String accountIdentifier = requestParams[1];
        clientValidationUtils.nullOrEmptyCheck(accountIdentifier, UIMessages.ACCOUNT_IDENTIFIER_INVALID);

        String serviceIdentifier = requestParams[2];
        clientValidationUtils.nullOrEmptyCheck(serviceIdentifier, UIMessages.SERVICE_IDENTIFIER_INVALID);

        String kpiType = requestParams[3].trim();
        clientValidationUtils.nullOrEmptyCheck(kpiType, UIMessages.KPI_TYPE_INVALID);

        String thresholdType = requestParams[4].trim();
        if (thresholdType.trim().isEmpty() || !thresholdType.equalsIgnoreCase(ThresholdType.STATIC.getType())) {
            log.error("Invalid thresholdType. Reason: thresholdType is undefined in the request.");
            throw new ClientException(UIMessages.THRESHOLD_TYPE_INVALID);
        }

        if (sorRuleList == null || sorRuleList.isEmpty()) {
            log.error("Received empty list of availability Rule objects");
            throw new ClientException("Static thresholds are unavailable");
        }
        Set<StaticThresholdRules> validSet = new HashSet<>(sorRuleList);
        if (sorRuleList.size() != validSet.size()) {
            log.error("duplicate static thresholds are defined for the same KPI");
            throw new ClientException("duplicate static thresholds are defined for the same KPI");
        }

        HashMap<String, String> requestParamsMap = new HashMap<>();
        requestParamsMap.put(Constants.ACCOUNT_IDENTIFIER, requestParams[1]);
        requestParamsMap.put(Constants.SERVICE_IDENTIFIER, requestParams[2]);
        requestParamsMap.put(Constants.KPI_TYPE, kpiType);
        requestParamsMap.put(Constants.THRESHOLD_TYPE, thresholdType);
        requestParamsMap.put(Constants.AUTH_KEY, authKey);
        return UtilityBean.<List<StaticThresholdRules>>builder().pojoObject(sorRuleList).requestParams(requestParamsMap).build();
    }

    @Override
    public UtilityBean<List<StaticThresholdRules>> serverValidation(UtilityBean<List<StaticThresholdRules>> utilityBean) throws ServerException {
        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
        String serviceIdentifier = utilityBean.getRequestParams().get(Constants.SERVICE_IDENTIFIER);
        String kpiType = utilityBean.getRequestParams().get(Constants.KPI_TYPE).trim();
        String authKey = utilityBean.getRequestParams().get(Constants.AUTH_KEY);

        Map<String, Object> metadata = new HashMap<>();

        // Basic user, account, and service validation
        String userId = serverValidationUtils.authKeyValidation(authKey);
        metadata.put(Constants.USER_ID, userId);

        Account account = serverValidationUtils.accountValidation(accountIdentifier);
        metadata.put(Constants.ACCOUNT, account);

        UserAccessDetails userAccessDetails = serverValidationUtils.userAccessDetailsValidation(userId, accountIdentifier);
        Service service = serverValidationUtils.serviceValidation(userId, accountIdentifier, serviceIdentifier, userAccessDetails);
        metadata.put(Constants.SERVICE, service);


        // Populate required ViewTypes into metadata
        metadata.putAll(populateViewTypesMetadata(kpiType));

        // 1. Fetch all valid KPIs for this service to validate against.
        Set<Integer> validKpiIdsForService;
        try {
            validKpiIdsForService = getValidKpiIdsForService(account.getId(), service.getId(), (ViewTypes) metadata.get(kpiType));
        } catch (HealControlCenterException e) {
            throw new ServerException(e.getMessage());
        }
        if (validKpiIdsForService.isEmpty() && !utilityBean.getPojoObject().isEmpty()) {
            log.warn("No valid KPIs of type '{}' found for service [{}], but thresholds were provided.", kpiType, service.getIdentifier());
            throw new ServerException("No matching KPIs found for this service to apply thresholds.");
        }

        // 2. Fetch all existing thresholds for this service from Percona ONCE.
        Map<String, List<ServiceKpiThreshold>> existingThresholdsMap;
        try {
            existingThresholdsMap = serviceDao.getAllKpiThresholds(account.getId(), service.getId())
                    .stream()
                    .collect(Collectors.groupingBy(st -> st.getKpiId() + "#" + st.getApplicableTo() + "#" + st.getKpiAttribute()));
        } catch (HealControlCenterException e) {
            log.error("Error fetching existing thresholds for serviceId {}", service.getId(), e);
            throw new ServerException("Failed to retrieve existing threshold configuration.");
        }

        // 3. Validate incoming rules against valid KPIs and update dataId for existing ones.
        List<StaticThresholdRules> validatedRules = validateAndPrepareRules(utilityBean.getPojoObject(), validKpiIdsForService,
                existingThresholdsMap, metadata, (ViewTypes) metadata.get(kpiType));

        utilityBean.setPojoObject(validatedRules);
        utilityBean.setMetadata(metadata);
        return utilityBean;
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<StaticThresholdRules> process(UtilityBean<List<StaticThresholdRules>> bean) throws DataProcessingException {
        try {
            Account account = (Account) bean.getMetadata().get(Constants.ACCOUNT);
            Service service = (Service) bean.getMetadata().get(Constants.SERVICE);
            Timestamp currentTime = dateTimeUtil.getCurrentTimestampInGMT();

            // 1. Prepare all database changes by classifying rules into add/update lists
            DbChanges dbChanges = prepareDatabaseChanges(bean, currentTime);

            // 2. Execute database operations
            executeDatabaseWrites(dbChanges);

            // 3. Synchronize changes with OpenSearch
            syncToOpenSearch(account.getIdentifier(), service.getIdentifier(), dbChanges);

            // 4. Asynchronously update Redis cache
            ViewTypes low = (ViewTypes) bean.getMetadata().get(Constants.THRESHOLD_SEVERITY_TYPE_LOW);
            ViewTypes medium = (ViewTypes) bean.getMetadata().get(Constants.THRESHOLD_SEVERITY_TYPE_MEDIUM);
            ViewTypes high = (ViewTypes) bean.getMetadata().get(Constants.THRESHOLD_SEVERITY_TYPE_HIGH);

            thresholdSyncService.updateServiceKpiThresholdsInRedis(account.getIdentifier(), service.getIdentifier(), bean.getPojoObject(),
                    dbChanges.timestamp(), low.getSubTypeId(), medium.getSubTypeId(), high.getSubTypeId());
            return bean.getPojoObject();
        } catch (Exception e) {
            log.error("Exception encountered while processing thresholds. Details: ", e);
            throw new DataProcessingException(e.getMessage());
        }
    }

    /**
     * A simple record to hold the collections of database changes.
     */
    private record DbChanges(List<ServiceKpiThreshold> toAdd, List<ServiceKpiThreshold> toUpdate,
                             Map<StaticThresholdRules, List<ServiceKpiThreshold>> updateRulesList, Timestamp timestamp) {
    }

    /**
     * Iterates through the input rules and classifies them into lists for database insertion or updates.
     *
     * @return A DbChanges object containing the prepared lists.
     */
    private DbChanges prepareDatabaseChanges(UtilityBean<List<StaticThresholdRules>> bean, Timestamp currentTime) {
        List<ServiceKpiThreshold> toAdd = new ArrayList<>();
        Map<StaticThresholdRules, List<ServiceKpiThreshold>> updateRulesList = new HashMap<>();

        Account account = (Account) bean.getMetadata().get(Constants.ACCOUNT);
        Service service = (Service) bean.getMetadata().get(Constants.SERVICE);
        String userId = (String) bean.getMetadata().get(Constants.USER_ID);

        ViewTypes kpi = (ViewTypes) bean.getMetadata().get(bean.getRequestParams().get(Constants.KPI_TYPE));
        ViewTypes lowThreshold = (ViewTypes) bean.getMetadata().get(Constants.THRESHOLD_SEVERITY_TYPE_LOW);
        ViewTypes mediumThreshold = (ViewTypes) bean.getMetadata().get(Constants.THRESHOLD_SEVERITY_TYPE_MEDIUM);
        ViewTypes highThreshold = (ViewTypes) bean.getMetadata().get(Constants.THRESHOLD_SEVERITY_TYPE_HIGH);
        Map<String, List<ViewTypes>> allViewTypesIdMap = cacheWrapper.getAllViewTypesIdMap();

        for (StaticThresholdRules rule : bean.getPojoObject()) {
            List<ServiceKpiThreshold> addServiceKpiThresholds = new ArrayList<>();
            List<ServiceKpiThreshold> updatedServiceKpiThresholds = new ArrayList<>();

            if (rule.getLowThreshold() != null) {
                ServiceKpiThreshold threshold = populateServiceKpiThreshold(rule, account.getId(), service.getId(), currentTime, userId, kpi, rule.getLowThreshold(), lowThreshold.getSubTypeId(), allViewTypesIdMap);
                if (rule.getLowThreshold().getDataId() == 0) addServiceKpiThresholds.add(threshold);
                else updatedServiceKpiThresholds.add(threshold);
            }
            if (rule.getWarningThreshold() != null) {
                ServiceKpiThreshold threshold = populateServiceKpiThreshold(rule, account.getId(), service.getId(), currentTime, userId, kpi, rule.getWarningThreshold(), mediumThreshold.getSubTypeId(), allViewTypesIdMap);
                if (rule.getWarningThreshold().getDataId() == 0) addServiceKpiThresholds.add(threshold);
                else updatedServiceKpiThresholds.add(threshold);
            }
            if (rule.getErrorThreshold() != null) {
                ServiceKpiThreshold threshold = populateServiceKpiThreshold(rule, account.getId(), service.getId(), currentTime, userId, kpi, rule.getErrorThreshold(), highThreshold.getSubTypeId(), allViewTypesIdMap);
                if (rule.getErrorThreshold().getDataId() == 0) addServiceKpiThresholds.add(threshold);
                else updatedServiceKpiThresholds.add(threshold);
            }

            toAdd.addAll(addServiceKpiThresholds);
            if (!updatedServiceKpiThresholds.isEmpty()) {
                updateRulesList.put(rule, updatedServiceKpiThresholds);
            }
        }

        List<ServiceKpiThreshold> toUpdate = updateRulesList.values().stream().flatMap(List::stream).toList();
        return new DbChanges(toAdd, toUpdate, updateRulesList, currentTime);
    }

    /**
     * Executes the database insert and update operations.
     */
    private void executeDatabaseWrites(DbChanges dbChanges) throws HealControlCenterException {
        if (!dbChanges.toAdd().isEmpty()) {
            int[] insertedIds = serviceDao.addThreshold(dbChanges.toAdd());
            log.info("Bulk inserted {} new threshold values.", insertedIds.length);
        }
        if (!dbChanges.toUpdate().isEmpty()) {
            int[] updatedIds = serviceDao.updateThreshold(dbChanges.toUpdate());
            log.info("Bulk updated {} existing threshold values.", updatedIds.length);
        }
    }

    /**
     * Handles synchronizing threshold changes with OpenSearch.
     */
    private void syncToOpenSearch(String accountId, String serviceId, DbChanges dbChanges) throws HealControlCenterException {
        log.info("Syncing thresholds to OpenSearch for service [{}] with account [{}]", serviceId, accountId);
        Timestamp syncTime = new Timestamp(dateTimeUtil.getCurrentTimestampInGMT().getTime() + 60000);

        // Bulk update existing thresholds in OpenSearch
        if (!dbChanges.updateRulesList().isEmpty()) {
            List<Map<String, String>> updateKeys = dbChanges.updateRulesList().keySet().stream()
                    .map(c -> Map.of("kpiId", c.getKpiId(), "applicableTo", c.getKpiLevel()))
                    .toList();

            Map<String, List<ServiceKpiThresholds>> osResultMap = serviceOpensearchRepoOpenSearch
                    .getServiceLevelThresholdDetailsBulk(accountId, serviceId, updateKeys);

            List<Map.Entry<StaticThresholdRules, ServiceKpiThresholds>> updateEntries = dbChanges.updateRulesList().keySet().stream()
                    .map(rule -> {
                        String key = rule.getKpiId() + "#" + rule.getKpiLevel();
                        ServiceKpiThresholds osThreshold = osResultMap.getOrDefault(key, Collections.emptyList()).stream().findFirst().orElse(null);
                        return new AbstractMap.SimpleEntry<>(rule, osThreshold);
                    }).collect(Collectors.toList());

            serviceOpensearchRepoOpenSearch.updateThresholdsBulk(accountId, serviceId, updateEntries, syncTime);
        }

        // Bulk insert new thresholds into OpenSearch
        List<StaticThresholdRules> rulesToInsert = dbChanges.updateRulesList().keySet().stream()
                .filter(StaticThresholdRules::isGenerateAnomaly)
                .toList();
        if (!rulesToInsert.isEmpty()) {
            serviceOpensearchRepoOpenSearch.insertBulkServiceKpiThresholdsIntoOS(accountId, serviceId, rulesToInsert, syncTime, "");
        }
        log.info("Successfully synced thresholds to OpenSearch for service [{}] with account [{}]", serviceId, accountId);
    }

    /**
     * Fetches all valid KPI IDs for a given service and KPI type.
     * This logic is adapted from GetServiceStaticThresholdBL for direct and efficient use here.
     */
    private Set<Integer> getValidKpiIdsForService(int accountId, int serviceId, ViewTypes kpiViewType) throws HealControlCenterException {
        List<CompInstClusterDetailsBean> serviceCompInstances = commonDataService.getComponentClusterList(serviceId, accountId);
        if (serviceCompInstances.isEmpty()) {
            log.warn("No component instances found for serviceId [{}].", serviceId);
            return Collections.emptySet();
        }

        // Fetch all KPI details once and group them for efficient lookup
        Map<String, List<KpiDetailsBean>> kpiDetailsMap = kpiDao.getAllKpiDetailsKpiList().stream()
                .collect(Collectors.groupingBy(kpi -> kpi.getCommonVersionId() + "#" + kpi.getComponentId()));

        // Map component instances to their relevant KPIs, filter by type, and collect their IDs.
        return serviceCompInstances.stream()
                .map(compInstance -> {
                    String key = compInstance.getCommonVersionId() + "#" + compInstance.getCompId();
                    return kpiDetailsMap.getOrDefault(key, Collections.emptyList());
                })
                .flatMap(Collection::stream)
                .filter(kpi -> kpi.getTypeId() == kpiViewType.getSubTypeId())
                .map(KpiDetailsBean::getId)
                .collect(Collectors.toSet());
    }

    /**
     * Validates each incoming threshold rule. It checks if the KPI is valid for the service
     * and updates the rule with the existing database ID if a threshold already exists.
     */
    private List<StaticThresholdRules> validateAndPrepareRules(List<StaticThresholdRules> incomingRules, Set<Integer> validKpiIdsForService,
                                                               Map<String, List<ServiceKpiThreshold>> existingThresholdsMap,
                                                               Map<String, Object> metadata, ViewTypes kpiType) throws ServerException {

        ViewTypes lowThreshold = (ViewTypes) metadata.get(Constants.THRESHOLD_SEVERITY_TYPE_LOW);
        ViewTypes mediumThreshold = (ViewTypes) metadata.get(Constants.THRESHOLD_SEVERITY_TYPE_MEDIUM);
        ViewTypes highThreshold = (ViewTypes) metadata.get(Constants.THRESHOLD_SEVERITY_TYPE_HIGH);

        for (StaticThresholdRules rule : incomingRules) {
            // Perform basic validation on the rule object itself
            rule.validate(kpiType.getSubTypeName());
            if (!rule.getErrorMessage().isEmpty()) {
                log.error("Invalid static threshold rule provided:{}. Error:{}", rule, rule.getErrorMessage());
                throw new ServerException("Invalid static threshold rule provided. Please check the input JSON.");
            }

            // 1. Check if the KPI is valid for this service
            int kpiId = Integer.parseInt(rule.getKpiId());
            if (!validKpiIdsForService.contains(kpiId)) {
                String message = String.format("kpiId:%s is not valid for this service", rule.getKpiId());
                log.error(message);
                throw new ServerException(message);
            }

            // 2. Check for existing thresholds and update dataId if found
            String key = rule.getKpiId() + "#" + rule.getKpiLevel() + "#" + rule.getKpiAttribute();
            List<ServiceKpiThreshold> existingThresholds = existingThresholdsMap.get(key);

            if (existingThresholds == null || existingThresholds.isEmpty()) {
                log.debug("No existing thresholds found for key:{}. This will be a new entry.", key);
                continue;
            }

            log.debug("Found existing thresholds for key:{}. Updating data IDs.", key);

            for (ServiceKpiThreshold existing : existingThresholds) {
                if (existing.getThresholdSeverityId() == lowThreshold.getSubTypeId() && rule.getLowThreshold() != null) {
                    rule.getLowThreshold().setDataId(existing.getId());
                } else if (existing.getThresholdSeverityId() == mediumThreshold.getSubTypeId() && rule.getWarningThreshold() != null) {
                    rule.getWarningThreshold().setDataId(existing.getId());
                } else if (existing.getThresholdSeverityId() == highThreshold.getSubTypeId() && rule.getErrorThreshold() != null) {
                    rule.getErrorThreshold().setDataId(existing.getId());
                }
            }
        }

        return incomingRules;
    }

    /**
     * Populates the metadata map with required ViewTypes, throwing a ServerException if any are missing.
     */
    private Map<String, Object> populateViewTypesMetadata(String kpiType) throws ServerException {
        Map<String, Object> metadata = new HashMap<>();

        Map<String, List<ViewTypes>> viewTypes = cacheWrapper.getAllViewTypesIdMap();

        ViewTypes kpi = commonUtils.getViewTypeByNameAndSubType(viewTypes, Constants.KPI_TYPE, kpiType);
        if (kpi == null || (!kpi.getSubTypeName().equals(Constants.CORE_KPI_TYPE) && !kpi.getSubTypeName().equals(Constants.AVAIL_KPI_TYPE))) {
            log.error("Invalid KPI type [{}]. Reason: KPI type should be one of Availability or Core.", kpiType);
            throw new ServerException(UIMessages.INVALID_KPI_TYPE);
        }
        metadata.put(kpiType, kpi);

        // Helper to reduce boilerplate
        Function<String, ViewTypes> getThresholdSeverity = (subType) -> commonUtils.getViewTypeByNameAndSubType(viewTypes, Constants.THRESHOLD_SEVERITY_TYPE, subType);
        Function<String, ViewTypes> getOperationType = (subType) -> commonUtils.getViewTypeByNameAndSubType(viewTypes, Constants.OPERATIONS_TYPE, subType);

        metadata.put(Constants.THRESHOLD_SEVERITY_TYPE_LOW, Optional.ofNullable(getThresholdSeverity.apply(Constants.THRESHOLD_SEVERITY_TYPE_LOW))
                .orElseThrow(() -> new ServerException("Low threshold severity type unavailable")));
        metadata.put(Constants.THRESHOLD_SEVERITY_TYPE_MEDIUM, Optional.ofNullable(getThresholdSeverity.apply(Constants.THRESHOLD_SEVERITY_TYPE_MEDIUM))
                .orElseThrow(() -> new ServerException("Medium threshold severity type unavailable")));
        metadata.put(Constants.THRESHOLD_SEVERITY_TYPE_HIGH, Optional.ofNullable(getThresholdSeverity.apply(Constants.THRESHOLD_SEVERITY_TYPE_HIGH))
                .orElseThrow(() -> new ServerException("High threshold severity type unavailable")));

        metadata.put(Constants.OPERATIONS_TYPE_LESSER_THAN, Optional.ofNullable(getOperationType.apply(Constants.OPERATIONS_TYPE_LESSER_THAN))
                .orElseThrow(() -> new ServerException("Lesser-than operation type unavailable")));
        metadata.put(Constants.OPERATIONS_TYPE_GREATER_THAN, Optional.ofNullable(getOperationType.apply(Constants.OPERATIONS_TYPE_GREATER_THAN))
                .orElseThrow(() -> new ServerException("Greater-than operation type unavailable")));
        metadata.put(Constants.OPERATIONS_TYPE_NOT_BETWEEN, Optional.ofNullable(getOperationType.apply(Constants.OPERATIONS_TYPE_NOT_BETWEEN))
                .orElseThrow(() -> new ServerException("Not-between operation type unavailable")));

        return metadata;
    }

    private ServiceKpiThreshold populateServiceKpiThreshold(StaticThresholdRules updateSorRule, int accountId, int serviceId, Timestamp echoMilli, String userId, ViewTypes kpiType, ThresholdConfig thresholds, int thresholdSeverityTypeId, Map<String, List<ViewTypes>> allViewTypesIdMap) {
        ServiceKpiThreshold updateThresholdBean = new ServiceKpiThreshold();
        updateThresholdBean.setThresholdSeverityId(thresholdSeverityTypeId);
        updateThresholdBean.setId(updateSorRule.getDataId());
        updateThresholdBean.setKpiId(Integer.parseInt(updateSorRule.getKpiId()));
        updateThresholdBean.setAccountId(accountId);
        updateThresholdBean.setServiceId(serviceId);
        updateThresholdBean.setUpdatedTime(echoMilli);
        updateThresholdBean.setKpiAttribute(updateSorRule.getKpiAttribute());
        updateThresholdBean.setUserDetailsId(userId);
        updateThresholdBean.setApplicableTo(updateSorRule.getKpiLevel());
        updateThresholdBean.setCreatedTime(echoMilli);
        updateThresholdBean.setStartTime(echoMilli);
        updateThresholdBean.setId(thresholds.getDataId());
        updateThresholdBean.setStatus(thresholds.getStatus());
        updateThresholdBean.setCoverageWindow(updateSorRule.getCoverageWindow());

        updateThresholdBean.setDefinedBy(Constants.THRESHOLD_DEFINED_BY_USER);
        if (kpiType.getSubTypeName().equals(Constants.CORE_KPI_TYPE) && thresholds.getOperationType() != null) {
            log.debug("CORE kpi type provided");
            Double max = thresholds.getMax();
            Double min = thresholds.getMin();
            ViewTypes operationType = commonUtils.getViewTypeByNameAndSubType(allViewTypesIdMap, Constants.OPERATIONS_TYPE, thresholds.getOperationType());
            updateThresholdBean.setOperationTypeId(operationType.getSubTypeId());
            if (max != null && min != null && max > min && operationType.getSubTypeName().equals(OperationTypeEnum.NOT_BETWEEN.getType())) {
                updateThresholdBean.setMaxThreshold(max);
                updateThresholdBean.setMinThreshold(min);
            } else if (min != null) {
                updateThresholdBean.setMinThreshold(min);
            }
        } else if (kpiType.getSubTypeName().equals(Constants.AVAIL_KPI_TYPE)) {
            ViewTypes operationType = commonUtils.getViewTypeByNameAndSubType(allViewTypesIdMap, Constants.AVAILABILITY_OPERATIONS_TYPE, thresholds.getOperationType());
            updateThresholdBean.setOperationTypeId(operationType.getSubTypeId());
        }
        return updateThresholdBean;
    }

    private List<KpiViolationConfig> buildKPIViolationConfigObject(Timestamp echoMilli, StaticThresholdRules staticThresholdRule, Map<String, List<KpiViolationConfig>> existingKpiViolationConfigMap, int lowThresholdSeverityTypeId, int mediumThresholdSeverityTypeId, int highThresholdSeverityTypeId) {
        SimpleDateFormat formatter = new SimpleDateFormat(Constants.DATE_TIME);
        formatter.setTimeZone(TimeZone.getTimeZone("UTC"));
        List<KpiViolationConfig> kpiViolationConfigs = new ArrayList<>();
        if (staticThresholdRule.getLowThreshold() != null) {
            KpiViolationConfig lowThresholdConfig = getKpiViolationConfig(echoMilli, staticThresholdRule, formatter, staticThresholdRule.getLowThreshold());
            lowThresholdConfig.setThresholdSeverityId(lowThresholdSeverityTypeId);
            lowThresholdConfig.setThresholdSeverity(Constants.THRESHOLD_SEVERITY_TYPE_LOW);
            kpiViolationConfigs.add(lowThresholdConfig);
        }

        if (staticThresholdRule.getWarningThreshold() != null) {
            KpiViolationConfig warningThresholdConfig = getKpiViolationConfig(echoMilli, staticThresholdRule, formatter, staticThresholdRule.getWarningThreshold());
            warningThresholdConfig.setThresholdSeverityId(mediumThresholdSeverityTypeId);
            warningThresholdConfig.setThresholdSeverity(Constants.THRESHOLD_SEVERITY_TYPE_MEDIUM);
            kpiViolationConfigs.add(warningThresholdConfig);
        }

        if (staticThresholdRule.getErrorThreshold() != null) {
            KpiViolationConfig errorThresholdConfig = getKpiViolationConfig(echoMilli, staticThresholdRule, formatter, staticThresholdRule.getErrorThreshold());
            errorThresholdConfig.setThresholdSeverityId(highThresholdSeverityTypeId);
            errorThresholdConfig.setThresholdSeverity(Constants.THRESHOLD_SEVERITY_TYPE_HIGH);
            kpiViolationConfigs.add(errorThresholdConfig);
        }

        List<KpiViolationConfig> existingKpiViolationConfig = new ArrayList<>();
        for (Map.Entry<String, List<KpiViolationConfig>> entry : existingKpiViolationConfigMap.entrySet()) {
            existingKpiViolationConfig.addAll(entry.getValue());
        }

        return extractAndUpdateUpdatedKpiViolationConfigs(kpiViolationConfigs, existingKpiViolationConfig);
    }

    private KpiViolationConfig getKpiViolationConfig(Timestamp echoMilli, StaticThresholdRules staticThresholdRule, SimpleDateFormat formatter, ThresholdConfig thresholdConfigType) {
        return KpiViolationConfig.builder()
                .startTime(formatter.format(echoMilli.getTime()))
                .attributeValue(staticThresholdRule.getKpiAttribute())
                .kpiId(Integer.parseInt(staticThresholdRule.getKpiId()))
                .applicableTo(staticThresholdRule.getKpiLevel())
                .definedBy(staticThresholdRule.isUserDefinedSOR() ? Constants.THRESHOLD_DEFINED_BY_USER : Constants.THRESHOLD_DEFINED_BY_SYSTEM)
                .coverageWindow(staticThresholdRule.getCoverageWindow())
                .minThreshold(thresholdConfigType.getMin())
                .maxThreshold(thresholdConfigType.getMax())
                .operation(thresholdConfigType.getOperationType())
                .status(thresholdConfigType.getStatus())
                .build();
    }

    private List<KpiViolationConfig> extractAndUpdateUpdatedKpiViolationConfigs(List<KpiViolationConfig> kpiViolationConfigs, List<KpiViolationConfig> existingKpiViolationConfig) {
        List<KpiViolationConfig> finalKpiViolationConfigs;
        if (existingKpiViolationConfig.isEmpty()) {
            return kpiViolationConfigs;
        } else {
            Map<String, KpiViolationConfig> configMap = new HashMap<>();
            for (KpiViolationConfig existingConfig : existingKpiViolationConfig) {
                String key = existingConfig.getKpiId() + "#" + existingConfig.getAttributeValue() + "#" + existingConfig.getThresholdSeverityId() + "#" + existingConfig.getApplicableTo();
                configMap.put(key, existingConfig);
            }
            for (KpiViolationConfig newConfig : kpiViolationConfigs) {
                String key = newConfig.getKpiId() + "#" + newConfig.getAttributeValue() + "#" + newConfig.getThresholdSeverityId() + "#" + newConfig.getApplicableTo();
                configMap.put(key, newConfig);
            }
            finalKpiViolationConfigs = new ArrayList<>(configMap.values());
        }

        return finalKpiViolationConfigs;
    }
}
