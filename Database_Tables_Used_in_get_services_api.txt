Database Tables Used in ServiceDetailsBL.java

Here's the complete list of database tables accessed by the ServiceDetailsBL.java class:
Core Tables:

    controller - Main table for services and applications
    tag_mapping - Stores tag mappings for various entities
    tag_details - Stores tag definitions and metadata
    connection_details - Stores service connections/dependencies
    component_instance (comp_instance) - Component instance details
    component_cluster_mapping - Maps components to clusters
    account - Account information
 
Master Data Tables:

    mst_type - Master type definitions
    mst_sub_type - Master sub-type definitions
    mst_timezone - Timezone master data
    mst_component - Master component definitions
    mst_component_type - Component type master data
    mst_component_version - Component version information
    mst_kpi_details - KPI definitions
    mst_producer_type - Producer type definitions

View Tables:

    view_application_service_mapping - Application-service mapping view
    view_cluster_services - Cluster-service mapping view
    view_component_instance - Component instance view
    view_types - Types view for master data
    view_common_version_kpis - Common version KPIs view

Command and Status Tables:

    command_trigger - Command trigger details
    agent_commands_triggered - Triggered agent commands
    command_details - Command definitions

User and Access Tables:

    user_attributes - User attribute information
    installation_attributes - Installation configuration

Transaction Tables:

    Transaction table (referenced via Constants.TXN_TABLE)

Redis Tables/Collections:

    Maintenance details (accessed via ConnectionsRepo)

Tables accessed through specific methods:

    application_notification_mapping - Application notification mappings
    user_notification_mapping - User notification mappings
    application_percentiles - Application percentile data
    comp_instance_kpi_details - Component instance KPI details
    comp_instance_attribute_values - Component instance attributes

The class primarily focuses on retrieving service information, their relationships, component instances, agent status, timezone details, layer information, transaction counts, and maintenance status. It heavily uses the controller table as the main source for services and applications, with extensive use of tag_mapping for categorization and connection_details for service dependencies.
