package com.heal.controlcenter.controller;

import com.heal.controlcenter.beans.AccountServiceValidationBean;
import com.heal.controlcenter.beans.ServiceBean;
import com.heal.controlcenter.businesslogic.AddAccountServicesBL;
import com.heal.controlcenter.businesslogic.PutAccountServicesBL;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.BasicUserDetails;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.GetAccountServicesBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.pojo.ResponsePojo;
import com.heal.controlcenter.pojo.ServiceListPage;
import com.heal.controlcenter.pojo.ServicePojo;
import com.heal.controlcenter.pojo.ServiceDeleteRequestPojo;
import com.heal.controlcenter.util.JsonFileParser;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import com.heal.controlcenter.businesslogic.DeleteAccountServicesBL;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class AccountServicesControllerTest {

    @Mock
    private GetAccountServicesBL getAccountServicesBL;

    @Mock
    private JsonFileParser headersParser;

    @InjectMocks
    private AccountServiceController accountController;
    @Mock
    private AddAccountServicesBL addAccountServicesBL;

    @Mock
    private PutAccountServicesBL putAccountServicesBL;

    @Mock
    private DeleteAccountServicesBL deleteAccountServiceBL;

    private final String USER_ID = "test-user";

    @Test
    void testGetAccountServices_Success() throws Exception {
        // Arrange
        String accountIdentifier = "test-account";
        Pageable pageable = PageRequest.of(0, 10);
        String searchTerm = "test";
        BasicUserDetails userDetails = new BasicUserDetails();
        userDetails.setUserIdentifier(USER_ID);

        Page<ServiceListPage> mockPage = new PageImpl<>(Collections.singletonList(new ServiceListPage()));
        UtilityBean<Object> clientValidationBean = UtilityBean.builder().metadata(new HashMap<>()).build();
        UtilityBean<AccountServiceValidationBean> serverValidationBean = UtilityBean.<AccountServiceValidationBean>builder().build();

        when(getAccountServicesBL.clientValidation(null, accountIdentifier, searchTerm)).thenReturn(clientValidationBean);
        when(getAccountServicesBL.serverValidation(any(UtilityBean.class))).thenReturn(serverValidationBean);
        when(getAccountServicesBL.process(any(UtilityBean.class))).thenReturn(mockPage);
        when(headersParser.loadHeaderConfiguration()).thenReturn(new HttpHeaders());

        // Act
        ResponseEntity<ResponsePojo<Page<ServiceListPage>>> response = accountController.getAccountServices(accountIdentifier, pageable, searchTerm, userDetails);

        // Assert
        assertNotNull(response);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals("success", response.getBody().getMessage());
        assertEquals(mockPage, response.getBody().getData());
    }

    @Test
    void testGetAccountServices_ClientException() throws Exception {
        // Arrange
        String accountIdentifier = "test-account";
        Pageable pageable = PageRequest.of(0, 10);
        String searchTerm = "test";
        BasicUserDetails userDetails = new BasicUserDetails();
        userDetails.setUserIdentifier(USER_ID);

        when(getAccountServicesBL.clientValidation(null, accountIdentifier, searchTerm)).thenThrow(new ClientException("Invalid client data"));

        // Act & Assert
        assertThrows(Exception.class, () -> accountController.getAccountServices(accountIdentifier, pageable, searchTerm, userDetails));
    }

    @Test
    @DisplayName("Add service successfully")
    void addService_success() throws Exception {
        ServicePojo servicePojo = ServicePojo.builder().name("Test Service").build();
        Map<String, Object> metadata = new HashMap<>();
        BasicUserDetails userDetails = new BasicUserDetails();
        userDetails.setUserIdentifier(USER_ID);
        UtilityBean<List<ServicePojo>> utilityBean = UtilityBean.<List<ServicePojo>>builder()
                .pojoObject(Collections.singletonList(servicePojo))
                .metadata(metadata)
                .build();
        UtilityBean<List<ServiceBean>> updatedUtilityBean = UtilityBean.<List<ServiceBean>>builder()
                .pojoObject(Collections.singletonList(new ServiceBean()))
                .metadata(metadata)
                .build();

        when(addAccountServicesBL.clientValidation(any(), any())).thenReturn(utilityBean);
        when(addAccountServicesBL.serverValidation(any())).thenReturn(updatedUtilityBean);
        when(addAccountServicesBL.process(any())).thenReturn(Collections.emptyList());

        ResponseEntity<ResponsePojo<List<com.heal.configuration.pojos.IdPojo>>> response =
                accountController.addServices("account-identifier", Collections.singletonList(servicePojo), userDetails);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals("Service(s) added/updated successfully", response.getBody().getMessage());
    }

    @Test
    @DisplayName("Add service with client exception")
    void addService_clientException() throws Exception {
        ServicePojo servicePojo = ServicePojo.builder().name("Test Service").build();
        BasicUserDetails userDetails = new BasicUserDetails();
        userDetails.setUserIdentifier(USER_ID);
        when(addAccountServicesBL.clientValidation(any(), any())).thenThrow(new ClientException("Client error"));

        assertThrows(Exception.class, () ->
                accountController.addServices("account-identifier", Collections.singletonList(servicePojo), userDetails));
    }

    @Test
    @DisplayName("Add service with server exception")
    void addService_serverException() throws Exception {
        ServicePojo servicePojo = ServicePojo.builder().name("Test Service").build();
        Map<String, Object> metadata = new HashMap<>();
        BasicUserDetails userDetails = new BasicUserDetails();
        userDetails.setUserIdentifier(USER_ID);
        UtilityBean<List<ServicePojo>> utilityBean = UtilityBean.<List<ServicePojo>>builder()
                .pojoObject(Collections.singletonList(servicePojo))
                .metadata(metadata)
                .build();

        when(addAccountServicesBL.clientValidation(any(), any())).thenReturn(utilityBean);
        when(addAccountServicesBL.serverValidation(any())).thenThrow(new ServerException("Server error"));

        assertThrows(Exception.class, () ->
                accountController.addServices("account-identifier", Collections.singletonList(servicePojo), userDetails));
    }

    @Test
    @DisplayName("Add service with data processing exception")
    void addService_dataProcessingException() throws Exception {
        ServicePojo servicePojo = ServicePojo.builder().name("Test Service").build();
        Map<String, Object> metadata = new HashMap<>();
        BasicUserDetails userDetails = new BasicUserDetails();
        userDetails.setUserIdentifier(USER_ID);
        UtilityBean<List<ServicePojo>> utilityBean = UtilityBean.<List<ServicePojo>>builder()
                .pojoObject(Collections.singletonList(servicePojo))
                .metadata(metadata)
                .build();
        UtilityBean<List<ServiceBean>> updatedUtilityBean = UtilityBean.<List<ServiceBean>>builder()
                .pojoObject(Collections.singletonList(new ServiceBean()))
                .metadata(metadata)
                .build();

        when(addAccountServicesBL.clientValidation(any(), any())).thenReturn(utilityBean);
        when(addAccountServicesBL.serverValidation(any())).thenReturn(updatedUtilityBean);
        when(addAccountServicesBL.process(any())).thenThrow(new DataProcessingException("Data processing error"));

        assertThrows(Exception.class, () ->
                accountController.addServices("account-identifier", Collections.singletonList(servicePojo), userDetails));
    }

    @Test
    @DisplayName("Update service successfully")
    void updateService_success() throws Exception {
        ServicePojo servicePojo = ServicePojo.builder().name("Test Service").build();
        Map<String, Object> metadata = new HashMap<>();
        BasicUserDetails userDetails = new BasicUserDetails();
        userDetails.setUserIdentifier(USER_ID);
        UtilityBean<List<ServicePojo>> utilityBean = UtilityBean.<List<ServicePojo>>builder()
                .pojoObject(Collections.singletonList(servicePojo))
                .metadata(metadata)
                .build();
        UtilityBean<List<ServiceBean>> updatedUtilityBean = UtilityBean.<List<ServiceBean>>builder()
                .pojoObject(Collections.singletonList(new ServiceBean()))
                .metadata(metadata)
                .build();

        when(putAccountServicesBL.clientValidation(any(), any())).thenReturn(utilityBean);
        when(putAccountServicesBL.serverValidation(any())).thenReturn(updatedUtilityBean);
        when(putAccountServicesBL.process(any())).thenReturn(Collections.emptyList());

        ResponseEntity<ResponsePojo<List<com.heal.configuration.pojos.IdPojo>>> response =
                accountController.updateServices("account-identifier", Collections.singletonList(servicePojo), userDetails);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals("Service updated successfully", response.getBody().getMessage());
    }

    @Test
    @DisplayName("Update service with client exception")
    void updateService_clientException() throws Exception {
        ServicePojo servicePojo = ServicePojo.builder().name("Test Service").build();
        BasicUserDetails userDetails = new BasicUserDetails();
        userDetails.setUserIdentifier(USER_ID);
        when(putAccountServicesBL.clientValidation(any(), any())).thenThrow(new ClientException("Client error"));

        assertThrows(Exception.class, () ->
                accountController.updateServices("account-identifier", Collections.singletonList(servicePojo), userDetails));
    }

    @Test
    @DisplayName("Update service with server exception")
    void updateService_serverException() throws Exception {
        ServicePojo servicePojo = ServicePojo.builder().name("Test Service").build();
        Map<String, Object> metadata = new HashMap<>();
        BasicUserDetails userDetails = new BasicUserDetails();
        userDetails.setUserIdentifier(USER_ID);
        UtilityBean<List<ServicePojo>> utilityBean = UtilityBean.<List<ServicePojo>>builder()
                .pojoObject(Collections.singletonList(servicePojo))
                .metadata(metadata)
                .build();

        when(putAccountServicesBL.clientValidation(any(), any())).thenReturn(utilityBean);
        when(putAccountServicesBL.serverValidation(any())).thenThrow(new ServerException("Server error"));

        assertThrows(Exception.class, () ->
                accountController.updateServices("account-identifier", Collections.singletonList(servicePojo), userDetails));
    }

    @Test
    @DisplayName("Update service with data processing exception")
    void updateService_dataProcessingException() throws Exception {
        ServicePojo servicePojo = ServicePojo.builder().name("Test Service").build();
        Map<String, Object> metadata = new HashMap<>();
        BasicUserDetails userDetails = new BasicUserDetails();
        userDetails.setUserIdentifier(USER_ID);
        UtilityBean<List<ServicePojo>> utilityBean = UtilityBean.<List<ServicePojo>>builder()
                .pojoObject(Collections.singletonList(servicePojo))
                .metadata(metadata)
                .build();
        UtilityBean<List<ServiceBean>> updatedUtilityBean = UtilityBean.<List<ServiceBean>>builder()
                .pojoObject(Collections.singletonList(new ServiceBean()))
                .metadata(metadata)
                .build();

        when(putAccountServicesBL.clientValidation(any(), any())).thenReturn(utilityBean);
        when(putAccountServicesBL.serverValidation(any())).thenReturn(updatedUtilityBean);
        when(putAccountServicesBL.process(any())).thenThrow(new DataProcessingException("Data processing error"));

        assertThrows(Exception.class, () ->
                accountController.updateServices("account-identifier", Collections.singletonList(servicePojo), userDetails));
    }

    @Test
    @DisplayName("Delete service successfully")
    void deleteService_success() throws Exception {
        String accountIdentifier = "test-account";
        ServiceDeleteRequestPojo deleteRequestPojo = ServiceDeleteRequestPojo.builder()
                .serviceIdentifiers(Collections.singletonList("service-123"))
                .hardDelete(false)
                .build();
        BasicUserDetails userDetails = new BasicUserDetails();
        userDetails.setUserIdentifier(USER_ID);

        UtilityBean<ServiceDeleteRequestPojo> clientValidatedBean = UtilityBean.<ServiceDeleteRequestPojo>builder()
                .pojoObject(deleteRequestPojo)
                .metadata(new HashMap<>())
                .build();
        UtilityBean<List<ServiceBean>> serverValidatedBean = UtilityBean.<List<ServiceBean>>builder()
                .pojoObject(Collections.singletonList(new ServiceBean()))
                .metadata(new HashMap<>())
                .build();

        when(deleteAccountServiceBL.clientValidation(any(ServiceDeleteRequestPojo.class), any(String.class))).thenReturn(clientValidatedBean);
        when(deleteAccountServiceBL.serverValidation(any(UtilityBean.class))).thenReturn(serverValidatedBean);
        when(deleteAccountServiceBL.process(any(UtilityBean.class))).thenReturn("Service(s) deleted successfully");

        ResponseEntity<ResponsePojo<String>> response = null;
        response = accountController.deleteServices(accountIdentifier, deleteRequestPojo, userDetails);

        assertNotNull(response);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals("Service(s) deleted successfully", response.getBody().getMessage());
    }

    @Test
    @DisplayName("Delete service with client exception")
    void deleteService_clientException() throws Exception {
        String accountIdentifier = "test-account";
        ServiceDeleteRequestPojo deleteRequestPojo = ServiceDeleteRequestPojo.builder()
                .serviceIdentifiers(Collections.singletonList("service-123"))
                .hardDelete(false)
                .build();
        BasicUserDetails userDetails = new BasicUserDetails();
        userDetails.setUserIdentifier(USER_ID);

        when(deleteAccountServiceBL.clientValidation(any(ServiceDeleteRequestPojo.class), any(String.class))).thenThrow(new ClientException("Client error during deletion"));

        assertThrows(Exception.class, () -> accountController.deleteServices(accountIdentifier, deleteRequestPojo, userDetails));
    }

    @Test
    @DisplayName("Delete service with server exception")
    void deleteService_serverException() throws Exception {
        String accountIdentifier = "test-account";
        ServiceDeleteRequestPojo deleteRequestPojo = ServiceDeleteRequestPojo.builder()
                .serviceIdentifiers(Collections.singletonList("service-123"))
                .hardDelete(false)
                .build();
        BasicUserDetails userDetails = new BasicUserDetails();
        userDetails.setUserIdentifier(USER_ID);

        UtilityBean<ServiceDeleteRequestPojo> clientValidatedBean = UtilityBean.<ServiceDeleteRequestPojo>builder()
                .pojoObject(deleteRequestPojo)
                .metadata(new HashMap<>())
                .build();

        when(deleteAccountServiceBL.clientValidation(any(ServiceDeleteRequestPojo.class), any(String.class))).thenReturn(clientValidatedBean);
        when(deleteAccountServiceBL.serverValidation(any(UtilityBean.class))).thenThrow(new ServerException("Server error during deletion"));

        assertThrows(Exception.class, () -> accountController.deleteServices(accountIdentifier, deleteRequestPojo, userDetails));
    }

    @Test
    @DisplayName("Delete service with data processing exception")
    void deleteService_dataProcessingException() throws Exception {
        String accountIdentifier = "test-account";
        ServiceDeleteRequestPojo deleteRequestPojo = ServiceDeleteRequestPojo.builder()
                .serviceIdentifiers(Collections.singletonList("service-123"))
                .hardDelete(false)
                .build();
        BasicUserDetails userDetails = new BasicUserDetails();
        userDetails.setUserIdentifier(USER_ID);

        UtilityBean<ServiceDeleteRequestPojo> clientValidatedBean = UtilityBean.<ServiceDeleteRequestPojo>builder()
                .pojoObject(deleteRequestPojo)
                .metadata(new HashMap<>())
                .build();
        UtilityBean<List<ServiceBean>> serverValidatedBean = UtilityBean.<List<ServiceBean>>builder()
                .pojoObject(Collections.singletonList(new ServiceBean()))
                .metadata(new HashMap<>())
                .build();

        when(deleteAccountServiceBL.clientValidation(any(ServiceDeleteRequestPojo.class), any(String.class))).thenReturn(clientValidatedBean);
        when(deleteAccountServiceBL.serverValidation(any(UtilityBean.class))).thenReturn(serverValidatedBean);
        when(deleteAccountServiceBL.process(any(UtilityBean.class))).thenThrow(new DataProcessingException("Data processing error during deletion"));

        assertThrows(Exception.class, () -> accountController.deleteServices(accountIdentifier, deleteRequestPojo, userDetails));
    }
}