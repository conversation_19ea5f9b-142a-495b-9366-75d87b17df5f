package com.heal.controlcenter.businesslogic;

import com.heal.controlcenter.beans.AccountBean;
import com.heal.controlcenter.beans.SMTPDetailsBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.dao.mysql.MasterDataDao;
import com.heal.controlcenter.dao.mysql.NotificationsDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.SMTPDetailsPojo;
import com.heal.controlcenter.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;

@Slf4j
@Service
public class GetEmailConfigurationsBL implements BusinessLogic<Object, Integer, SMTPDetailsPojo> {

    @Autowired
    CommonUtils commonUtils;
    @Autowired
    AccountsDao accountDao;
    @Autowired
    NotificationsDao notificationsDao;
    @Autowired
    MasterDataDao masterDataDao;
    @Autowired
    AECSBouncyCastleUtil aecsBouncyCastleUtil;
    @Autowired
    ClientValidationUtils clientValidationUtils;
    @Autowired
    ServerValidationUtils serverValidationUtils;

    @Override
    public UtilityBean<Object> clientValidation(Object requestBody, String... requestParams) throws ClientException {
        String authKey = requestParams[0];
        clientValidationUtils.authKeyValidation(authKey);

        String accountIdentifier = requestParams[1];
        clientValidationUtils.accountIdentifierValidation(accountIdentifier);

        HashMap<String, String> requestParamsMap = CommonUtils.buildRequestParams(authKey, accountIdentifier);

        return UtilityBean.builder()
                .requestParams(requestParamsMap)
                .build();
    }

    @Override
    public Integer serverValidation(UtilityBean<Object> utilityBean) throws ServerException {
        String authKey = utilityBean.getRequestParams().get(Constants.AUTH_KEY);
        serverValidationUtils.authKeyValidation(authKey);

        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);

        AccountBean account = accountDao.getAccountByIdentifier(accountIdentifier);
        if (account == null) {
            String message = String.format(UIMessages.ACCOUNT_IDENTIFIER_UNAVAILABLE, accountIdentifier);
            log.error(message);
            throw new ServerException(message);
        }

        return account.getId();
    }

    @Override
    public SMTPDetailsPojo process(Integer accountId) throws DataProcessingException {
        String security = "";
        SMTPDetailsBean smtpDetailsBean = notificationsDao.getSMTPDetails(accountId);

        if (smtpDetailsBean == null) {
            log.error("SMTP details not found for accountId [{}].", accountId);
            return null;
        }

        String encryptedString = smtpDetailsBean.getPassword();
        try {
            smtpDetailsBean.setPassword(decryptBCECAndEncryptAECS(encryptedString));
        } catch (HealControlCenterException e) {
            throw new DataProcessingException(e.getMessage());
        }
        if (smtpDetailsBean.getSecurityId() > 0) {
            security = masterDataDao.getMstSubTypeBySubTypeId(smtpDetailsBean.getSecurityId()).getSubTypeName();
        }

        return SMTPDetailsPojo.builder()
                .id(smtpDetailsBean.getId())
                .address(smtpDetailsBean.getAddress())
                .port(smtpDetailsBean.getPort())
                .username(smtpDetailsBean.getUsername())
                .password(smtpDetailsBean.getPassword())
                .fromRecipient(smtpDetailsBean.getFromRecipient())
                .security(security)
                .build();
    }

    private String decryptBCECAndEncryptAECS(String input) throws HealControlCenterException {
        String plainTxt;
        try {
            plainTxt = commonUtils.decryptInBCEC(input);
        } catch (Exception e) {
            log.error("Exception encountered while decrypting the password. Details: {}", e.getMessage());
            throw new HealControlCenterException("Error occurred while decrypting the password from the database.");
        }
        try {
            return aecsBouncyCastleUtil.encrypt(plainTxt);
        } catch (Exception e) {
            log.error("Exception encountered while encrypting the password. Details: {}", e.getMessage());
            throw new HealControlCenterException("Error occurred while encrypting the password from the database.");
        }
    }
}
