package com.heal.controlcenter.dao.mysql;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

@Slf4j
@Repository
public class CompInstanceDao {

    @Autowired
    JdbcTemplate jdbcTemplate;

    public Set<Integer> getAgentIdForService(int serviceId, int accountId) {
        String query = "SELECT acim.agent_id agentId FROM component_cluster_mapping c, comp_instance ci, view_cluster_services vcs, agent_comp_instance_mapping acim " +
                "WHERE c.cluster_id = vcs.id and c.comp_instance_id = ci.id and c.comp_instance_id=acim.comp_instance_id and ci.status = 1 " +
                "and vcs.service_id=? and ci.account_id=?";
        try {
            return new HashSet<>(jdbcTemplate.query(query, new BeanPropertyRowMapper<>(Integer.class), serviceId, accountId));
        } catch (Exception e) {
            log.error("Error occurred while fetching agent details for service Id: [{}]. Details: {}", serviceId, e.getMessage());
        }
        return Collections.emptySet();
    }

    public Set<Integer> getAgentIdForServiceFromTagMapping(int serviceId, int accountId) {
        String query = "SELECT distinct object_id FROM tag_mapping tm " +
                "WHERE tm.account_id=? and tm.object_ref_table='agent' and tm.tag_key=?";
        try {
            return new HashSet<>(jdbcTemplate.query(query, new BeanPropertyRowMapper<>(Integer.class), accountId, serviceId));
        } catch (Exception e) {
            log.error("Error occurred while fetching agent details for service Id: [{}]. Details: {}", serviceId, e.getMessage());
        }
        return Collections.emptySet();
    }
}
