package com.heal.controlcenter.businesslogic;

import com.heal.configuration.pojos.Account;
import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.dao.mysql.*;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.KeyCloakUserDetails;
import com.heal.controlcenter.pojo.ServiceListPage;
import com.heal.controlcenter.util.ClientValidationUtils;
import com.heal.controlcenter.util.CommonUtils;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.KeyCloakAuthService;
import com.heal.controlcenter.util.ServerValidationUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class GetAccountServicesBLTest {

    @InjectMocks
    private GetAccountServicesBL getAccountServicesBL;

    @Mock
    private ClientValidationUtils clientValidationUtils;
    @Mock
    private ServerValidationUtils serverValidationUtils;
    @Mock
    private ControllerDao controllerDao;
    @Mock
    private ConnectionDetailsDao connectionDetailsDao;
    @Mock
    private UserDao userDao;
    @Mock
    private MasterDataDao masterDataDao;
    @Mock
    private KeyCloakAuthService keyCloakAuthService;
    @Mock
    private AccountServiceDao accountServiceDao;

    private Account account;
    private UtilityBean<Object> clientValidationBean;
    private UtilityBean<AccountServiceValidationBean> serverValidationBean;

    @BeforeEach
    void setUp() {
        account = new Account();
        account.setId(1);
        account.setIdentifier("test-account");
        account.setName("Test Account");

        clientValidationBean = UtilityBean.builder()
                .requestParams(CommonUtils.buildRequestParams(null, account.getIdentifier()))
                .build();

        AccountServiceValidationBean validationBean = AccountServiceValidationBean.builder()
                .account(account)
                .build();

        serverValidationBean = UtilityBean.<AccountServiceValidationBean>builder()
                .pojoObject(validationBean)
                .metadata(Map.of(Constants.USER_ID_KEY, "test-user"))
                .requestParams(new HashMap<>(Map.of(Constants.ACCOUNT_IDENTIFIER, account.getIdentifier(), Constants.SEARCH_TERM_KEY, "")))
                .pageable(PageRequest.of(0, 10))
                .build();
    }

    @Test
    void clientValidation_Success() throws ClientException {
        String accountIdentifier = "test-account";
        String searchTerm = "test-search";
        doNothing().when(clientValidationUtils).accountIdentifierValidation(accountIdentifier);

        UtilityBean<Object> result = getAccountServicesBL.clientValidation(null, accountIdentifier, searchTerm);

        assertNotNull(result);
        assertEquals(accountIdentifier, result.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER));
        assertEquals(searchTerm, result.getRequestParams().get(Constants.SEARCH_TERM_KEY));
        verify(clientValidationUtils).accountIdentifierValidation(accountIdentifier);
    }

    @Test
    void clientValidation_NullSearchTerm() throws ClientException {
        String accountIdentifier = "test-account";
        doNothing().when(clientValidationUtils).accountIdentifierValidation(accountIdentifier);

        UtilityBean<Object> result = getAccountServicesBL.clientValidation(null, accountIdentifier, null);

        assertEquals("", result.getRequestParams().get(Constants.SEARCH_TERM_KEY));
    }

    @Test
    void clientValidation_EmptySearchTerm() throws ClientException {
        String accountIdentifier = "test-account";
        doNothing().when(clientValidationUtils).accountIdentifierValidation(accountIdentifier);

        UtilityBean<Object> result = getAccountServicesBL.clientValidation(null, accountIdentifier, "   ");

        assertEquals("", result.getRequestParams().get(Constants.SEARCH_TERM_KEY));
    }

    @Test
    void clientValidation_InvalidAccountIdentifier_ThrowsClientException() throws ClientException {
        String invalidIdentifier = "";
        doThrow(new ClientException("Invalid account identifier")).when(clientValidationUtils).accountIdentifierValidation(invalidIdentifier);

        assertThrows(ClientException.class, () -> getAccountServicesBL.clientValidation(null, invalidIdentifier));
    }

    @Test
    void serverValidation_Success() throws ServerException {
        when(serverValidationUtils.accountValidation(anyString())).thenReturn(account);

        UtilityBean<AccountServiceValidationBean> result = getAccountServicesBL.serverValidation(clientValidationBean);

        assertNotNull(result);
        assertEquals(account, result.getPojoObject().getAccount());
        verify(serverValidationUtils).accountValidation(account.getIdentifier());
    }

    @Test
    void serverValidation_AccountNotFound_ThrowsServerException() throws ServerException {
        when(serverValidationUtils.accountValidation(anyString())).thenThrow(new ServerException("Account not found"));

        assertThrows(ServerException.class, () -> getAccountServicesBL.serverValidation(clientValidationBean));
    }

    @Test
    void process_Success() throws DataProcessingException, ControlCenterException {
        ViewApplicationServiceMappingBean mappingBean = new ViewApplicationServiceMappingBean();
        mappingBean.setServiceId(101);
        mappingBean.setApplicationId(201);
        when(userDao.getUserAccessibleApplicationsServices(anyString(), anyString())).thenReturn(List.of(mappingBean));

        ControllerBean serviceBean = new ControllerBean();
        serviceBean.setId(101);
        serviceBean.setName("test-service");
        serviceBean.setIdentifier("service-identifier");
        serviceBean.setStatus(1);
        serviceBean.setCreatedTime("2023-01-01 10:00:00");
        serviceBean.setUpdatedTime("2023-01-01 10:00:00");
        serviceBean.setLastModifiedBy("user-id");
        when(controllerDao.getServicesList(anyInt(), anyString(), anyList(), any(Pageable.class))).thenReturn(List.of(serviceBean));
        when(controllerDao.getServicesListCount(anyInt(), anyString(), anyList())).thenReturn(1);

        when(connectionDetailsDao.getConnectionsByAccountId(anyInt())).thenReturn(Collections.emptyList());
        when(masterDataDao.getCompInstanceDetails(anyInt())).thenReturn(Collections.emptyList());

        KeyCloakUserDetails userDetails = new KeyCloakUserDetails();
        userDetails.setUsername("test-user");
        when(keyCloakAuthService.getKeycloakUserDataFromId(anyString())).thenReturn(userDetails);

        Page<ServiceListPage> result = getAccountServicesBL.process(serverValidationBean);

        assertEquals(1, result.getTotalElements());
        assertEquals(1, result.getContent().size());
        assertEquals("test-service", result.getContent().get(0).getName());
        verify(userDao).getUserAccessibleApplicationsServices("test-user", "test-account");
        verify(controllerDao).getServicesList(1, "", List.of(101), serverValidationBean.getPageable());
    }

    @Test
    void process_NoAccessibleServices() throws DataProcessingException, ControlCenterException {
        when(userDao.getUserAccessibleApplicationsServices(anyString(), anyString())).thenReturn(Collections.emptyList());

        Page<ServiceListPage> result = getAccountServicesBL.process(serverValidationBean);

        assertTrue(result.isEmpty());
        assertEquals(0, result.getTotalElements());
    }

    @Test
    void process_NoServicesFound() throws DataProcessingException, ControlCenterException {
        ViewApplicationServiceMappingBean mappingBean = new ViewApplicationServiceMappingBean();
        mappingBean.setServiceId(101);
        mappingBean.setApplicationId(201);
        when(userDao.getUserAccessibleApplicationsServices(anyString(), anyString())).thenReturn(List.of(mappingBean));
        when(controllerDao.getServicesList(anyInt(), anyString(), anyList(), any(Pageable.class))).thenReturn(Collections.emptyList());
        when(controllerDao.getServicesListCount(anyInt(), anyString(), anyList())).thenReturn(0);

        Page<ServiceListPage> result = getAccountServicesBL.process(serverValidationBean);

        assertTrue(result.isEmpty());
        assertEquals(0, result.getTotalElements());
    }

    @Test
    void process_DaoThrowsException() throws ControlCenterException {
        when(userDao.getUserAccessibleApplicationsServices(anyString(), anyString())).thenThrow(new ControlCenterException("DB error"));

        assertThrows(DataProcessingException.class, () -> getAccountServicesBL.process(serverValidationBean));
    }

    @Test
    void process_WithSearchTerm() throws DataProcessingException, ControlCenterException {
        serverValidationBean.getRequestParams().put(Constants.SEARCH_TERM_KEY, "search");
        ViewApplicationServiceMappingBean mappingBean = new ViewApplicationServiceMappingBean();
        mappingBean.setServiceId(101);
        mappingBean.setApplicationId(201);
        when(userDao.getUserAccessibleApplicationsServices(anyString(), anyString())).thenReturn(List.of(mappingBean));
        when(controllerDao.getServicesList(anyInt(), eq("search"), anyList(), any(Pageable.class))).thenReturn(Collections.emptyList());

        getAccountServicesBL.process(serverValidationBean);

        verify(controllerDao).getServicesList(anyInt(), eq("search"), anyList(), any(Pageable.class));
    }

    @Test
    void process_NoAccessibleServiceIds() throws DataProcessingException, ControlCenterException {
        ViewApplicationServiceMappingBean mappingBean = new ViewApplicationServiceMappingBean();
        mappingBean.setServiceId(null); // Null service ID
        mappingBean.setApplicationId(201);
        when(userDao.getUserAccessibleApplicationsServices(anyString(), anyString())).thenReturn(List.of(mappingBean));

        Page<ServiceListPage> result = getAccountServicesBL.process(serverValidationBean);

        assertTrue(result.isEmpty());
        assertEquals(0, result.getTotalElements());
    }
}