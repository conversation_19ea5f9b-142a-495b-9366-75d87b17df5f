package com.heal.controlcenter.service;

import com.heal.controlcenter.beans.CompInstClusterDetailsBean;
import com.heal.controlcenter.dao.mysql.MasterDataDao;
import com.heal.controlcenter.pojo.TagMappingDetails;
import com.heal.controlcenter.util.Constants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class CommonDataService {

    @Autowired
    private MasterDataDao masterDataDao;

    public List<CompInstClusterDetailsBean> getComponentClusterList(int serviceId, int accountId) {
        List<CompInstClusterDetailsBean> componentInstanceLists = getAllInstancesByService(serviceId, accountId);
        if (componentInstanceLists.isEmpty()) {
            return new ArrayList<>();
        }

        return componentInstanceLists.stream()
                .filter(inst -> inst.getIsCluster() == 1)
                .collect(Collectors.toList());
    }

    private List<CompInstClusterDetailsBean> getAllInstancesByService(int serviceId, int accountId) {
        List<TagMappingDetails> tagMappings = masterDataDao.getTagMappingDetails(accountId);
        if (tagMappings.isEmpty()) {
            return new ArrayList<>();
        }

        Set<Integer> instIds = tagMappings.stream()
                .filter(t -> t.getTagId() == 1)
                .filter(t -> t.getTagKey().equals(String.valueOf(serviceId)))
                .filter(t -> t.getObjectRefTable().equalsIgnoreCase(Constants.COMP_INSTANCE_TABLE))
                .map(TagMappingDetails::getObjectId).collect(Collectors.toSet());
        if (instIds.isEmpty()) {
            return new ArrayList<>();
        }

        List<CompInstClusterDetailsBean> componentInstanceLists = masterDataDao.getCompInstanceDetails(accountId);
        if (componentInstanceLists.isEmpty()) {
            return new ArrayList<>();
        }

        return componentInstanceLists.stream()
                .filter(inst -> instIds.contains(inst.getInstanceId()))
                .collect(Collectors.toList());
    }
}
