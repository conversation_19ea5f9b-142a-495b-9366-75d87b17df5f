package com.heal.controlcenter.dao.mysql;

import com.heal.controlcenter.beans.ConnectionDetailsBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;

@Slf4j
@Repository
public class ConnectionDetailsDao {

    @Autowired
    JdbcTemplate jdbcTemplate;

    public List<ConnectionDetailsBean> getConnectionsByAccountId(Integer accountId) {
        String query = "select cd.id, source_id sourceId, source_ref_object sourceRefObject, destination_id destinationId, " +
                "destination_ref_object destinationRefObject, cd.created_time createdTime, cd.updated_time updatedTime, " +
                "cd.account_id accountId, cd.user_details_id userDetailsId, is_discovery isDiscovery, c1.name sourceName, c1.identifier sourceIdentifier, " +
                "c2.name destinationName, c2.identifier destinationIdentifier from connection_details cd, controller c1, controller c2 " +
                "where c1.id=source_id and c2.id=destination_id and cd.account_id = ?";
        try {
            log.debug("getting connections.");
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(ConnectionDetailsBean.class), accountId);
        } catch (EmptyResultDataAccessException e) {
            log.info("Service connections unavailable for account [{}]", accountId);
        } catch (Exception e) {
            log.error("Error occurred while fetching connections for accountId [{}]. Details: ", accountId, e);
        }
        return Collections.emptyList();
    }
}
