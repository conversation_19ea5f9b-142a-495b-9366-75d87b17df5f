package com.heal.controlcenter.swagger;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Schema definitions for error responses used in Swagger documentation.
 * These schemas represent the actual structure returned by the ExceptionHandler.
 */
public class ErrorResponseSchemas {

    @Schema(name = "ErrorResponse", description = "Standard error response format for client errors (400)")
    public static class ErrorResponse {
        @Schema(description = "Error message describing what went wrong", example = "Invalid account identifier format")
        public String message;

        @Schema(description = "Error details containing timestamp, status, and path information",
                example = "{\"timestamp\":\"2024-01-15T10:30:00Z\",\"status\":400,\"error\":[\"Invalid account identifier format\"],\"type\":\"ClientException\",\"path\":\"uri=/accounts/invalid-id/applications\",\"message\":\"Invalid account identifier format\"}")
        public ErrorData data;

        @Schema(description = "HTTP status", example = "BAD_REQUEST")
        public String responseStatus;
    }

    @Schema(name = "UnauthorizedResponse", description = "Unauthorized error response for authentication failures (401)")
    public static class UnauthorizedResponse {
        @Schema(description = "Error message describing authentication failure", example = "Invalid or expired authentication token")
        public String message;

        @Schema(description = "Error details containing timestamp, status, and path information",
                example = "{\"timestamp\":\"2024-01-15T10:30:00Z\",\"status\":401,\"error\":[\"Invalid or expired authentication token\"],\"type\":\"ServerException\",\"path\":\"uri=/accounts/123/applications\",\"message\":\"Invalid or expired authentication token\"}")
        public ErrorData data;

        @Schema(description = "HTTP status", example = "UNAUTHORIZED")
        public String responseStatus;
    }

    @Schema(name = "ServerErrorResponse", description = "Server error response for internal errors (500)")
    public static class ServerErrorResponse {
        @Schema(description = "Error message describing internal server error", example = "An unexpected error occurred while processing the request")
        public String message;

        @Schema(description = "Error details containing timestamp, status, and path information",
                example = "{\"timestamp\":\"2024-01-15T10:30:00Z\",\"status\":500,\"error\":[\"An unexpected error occurred while processing the request\"],\"type\":\"DataProcessingException\",\"path\":\"uri=/accounts/123/applications\",\"message\":\"An unexpected error occurred while processing the request\"}")
        public ErrorData data;

        @Schema(description = "HTTP status", example = "INTERNAL_SERVER_ERROR")
        public String responseStatus;
    }

    @Schema(name = "ErrorData", description = "Detailed error information included in error responses")
    public static class ErrorData {
        @Schema(description = "Timestamp when the error occurred", example = "2024-01-15T10:30:00.123Z")
        public String timestamp;

        @Schema(description = "HTTP status code", example = "400")
        public int status;

        @Schema(description = "List of error messages providing details about what went wrong")
        public java.util.List<String> error;

        @Schema(description = "Type of exception that caused the error", example = "ClientException")
        public String type;

        @Schema(description = "Request path where the error occurred", example = "uri=/accounts/invalid-id/applications")
        public String path;

        @Schema(description = "Primary error message", example = "Invalid account identifier format")
        public String message;
    }
}
