package com.heal.controlcenter.dao.mysql;

import com.heal.configuration.pojos.PairData;
import com.heal.configuration.pojos.PersistenceSuppressionConfiguration;
import com.heal.configuration.pojos.ServiceConfiguration;
import com.heal.controlcenter.beans.ControllerBean;
import com.heal.controlcenter.beans.TagDetailsBean;
import com.heal.controlcenter.beans.TagMappingBean;
import com.heal.controlcenter.beans.ViewTypesBean;
import com.heal.controlcenter.pojo.RulesHelperPojo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.Statement;
import java.util.Collections;
import java.util.List;

@Slf4j
@Repository
public class AccountServiceDao {

    @Autowired
    JdbcTemplate jdbcTemplate;

    public List<ViewTypesBean> getMstSubTypeByTypeId(int typeId) {
        String query = "SELECT type AS typeName, typeid AS typeId, name AS subTypeName, subtypeid AS subTypeId " +
                "FROM view_types WHERE typeid = ?";
        try {
            log.debug("Querying for view types with typeId: {}", typeId);
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(ViewTypesBean.class), typeId);
        } catch (Exception e) {
            log.error("Error fetching master type info from 'view_types' for typeId [{}].", typeId, e);
        }
        return Collections.emptyList();
    }

    public TagDetailsBean getTagDetails(String name, int accountId) {
        String query = "SELECT id, name, tag_type_id AS tagTypeId, is_predefined AS isPredefined, ref_table AS refTable, " +
                "created_time AS createdTime, updated_time AS updatedTime, account_id AS accountId, user_details_id AS userDetailsId, " +
                "ref_where_column_name AS refWhereColumnName, ref_select_column_name AS refSelectColumnName " +
                "FROM tag_details WHERE name = ? AND account_id IN (1, ?)";
        try {
            log.debug("Querying tag_details for name: {} and accountId: {}", name, accountId);
            return jdbcTemplate.queryForObject(query,
                    new BeanPropertyRowMapper<>(TagDetailsBean.class),
                    name, accountId);
        } catch (Exception e) {
            log.error("Error fetching tag_details for name: {} and accountId: {}", name, accountId, e);
            return null;
        }
    }

    public int editControllerName(String name, String updatedTime, int id) {
        String query = "UPDATE controller SET name = ?, updated_time = ? WHERE id = ?";
        try {
            log.debug("Updating controller name for id: {} to '{}'", id, name);
            return jdbcTemplate.update(query, name, updatedTime, id);
        } catch (Exception e) {
            log.error("Failed to update controller name for id: {}", id, e);
            return -1;
        }
    }


    public int addController(ControllerBean controllerBean) {
        String query = "INSERT INTO controller (name, identifier, account_id, user_details_id, created_time, updated_time, controller_type_id) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?)";

        KeyHolder keyHolder = new GeneratedKeyHolder();

        try {
            jdbcTemplate.update(connection -> {
                PreparedStatement ps = connection.prepareStatement(query, Statement.RETURN_GENERATED_KEYS);
                ps.setString(1, controllerBean.getName());
                ps.setString(2, controllerBean.getIdentifier());
                ps.setInt(3, controllerBean.getAccountId());
                ps.setString(4, String.valueOf(controllerBean.getId()));
                ps.setString(5, controllerBean.getCreatedTime());
                ps.setString(6, controllerBean.getUpdatedTime());
                ps.setInt(7, controllerBean.getControllerTypeId());
                return ps;
            }, keyHolder);

            Number key = keyHolder.getKey();
            return (key != null) ? key.intValue() : -1;

        } catch (Exception e) {
            log.error("Error inserting controller: {}", controllerBean.getIdentifier(), e);
            return -1;
        }
    }


    public Integer getTagMappingId(int tagId, int objectId, String objectRefTable,
                                   String tagKey, String tagValue, int accountId) {
        String query = "SELECT id FROM tag_mapping WHERE tag_id = ? AND object_id = ? AND object_ref_table = ? " +
                "AND tag_key = ? AND tag_value = ? AND account_id = ?";
        try {
            log.debug("Querying tag_mapping for tagId: {}, objectId: {}, objectRefTable: {}, tagKey: {}, tagValue: {}, accountId: {}",
                    tagId, objectId, objectRefTable, tagKey, tagValue, accountId);
            return jdbcTemplate.queryForObject(query, Integer.class, tagId, objectId, objectRefTable, tagKey, tagValue, accountId);
        } catch (Exception e) {
            log.warn("No tag mapping found or error occurred for tagId: {}, objectId: {}, objectRefTable: {}, tagKey: {}, tagValue: {}, accountId: {}",
                    tagId, objectId, objectRefTable, tagKey, tagValue, accountId, e);
            return null;
        }
    }

    public int addTagMappingDetails(TagMappingBean bean) {
        final String query = """
        INSERT INTO tag_mapping (
            tag_id, object_id, object_ref_table, tag_key, tag_value,
            created_time, updated_time, account_id, user_details_id
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    """;

        KeyHolder keyHolder = new GeneratedKeyHolder();

        try {
            int rows = jdbcTemplate.update(connection -> {
                PreparedStatement ps = connection.prepareStatement(query, Statement.RETURN_GENERATED_KEYS);
                ps.setInt(1, bean.getTagId());
                ps.setInt(2, bean.getObjectId());
                ps.setString(3, bean.getObjectRefTable());
                ps.setString(4, bean.getTagKey());
                ps.setString(5, bean.getTagValue());
                ps.setString(6, bean.getCreatedTime());
                ps.setString(7, bean.getUpdatedTime());
                ps.setInt(8, bean.getAccountId());
                ps.setString(9, bean.getUserDetailsId());
                return ps;
            }, keyHolder);

            if (rows > 0 && keyHolder.getKey() != null) {
                int generatedId = keyHolder.getKey().intValue();
                log.debug("Inserted tag_mapping row with generated ID: {}", generatedId);
                return generatedId;
            } else {
                log.warn("Tag mapping insert did not return a generated ID. Bean: {}", bean);
                return -1;
            }

        } catch (Exception e) {
            log.error("Error inserting tag_mapping for objectId: {}, tagId: {}",
                    bean.getObjectId(), bean.getTagId(), e);
            return -1;
        }
    }

    public List<RulesHelperPojo> getRulesHelperPojo(int accountId, int serviceId) {
        String query = """
        SELECT 
            r.id AS id,
            r.name AS name,
            r.is_enabled AS enabled,
            r.`order` AS `order`,
            r.rule_type_id AS ruleTypeId,
            r.is_default AS isDefault,
            r.max_tags AS maxTags,
            r.discovery_status AS discoveryEnabled,
            tp.id AS tcpId,
            tp.initial_pattern AS tcpInitialPattern,
            tp.last_pattern AS tcpLastPattern,
            tp.length AS tcpLength,
            hp.id AS httpId,
            hp.first_uri_segments AS httpFirstUriSegments,
            hp.last_uri_segments AS httpLastUriSegments,
            hp.complete_uri AS httpCompleteURI,
            hp.payload_type_id AS httpPayloadTypeId,
            tm.tag_key AS concernedConfigId
        FROM controller c, tag_mapping tm, rules r
        LEFT JOIN tcp_patterns tp ON tp.rule_id = r.id
        LEFT JOIN http_patterns hp ON hp.rule_id = r.id
        WHERE tm.tag_id = 1
          AND tm.object_ref_table = 'rules'
          AND r.id = tm.object_id
          AND c.id = tm.tag_key
          AND c.account_id = ?
          AND tm.tag_key = ?
        """;

        try {
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(RulesHelperPojo.class), accountId, serviceId);
        } catch (Exception e) {
            log.error("Failed to fetch RulesHelperPojo for accountId [{}] and serviceId [{}]", accountId, serviceId, e);
            return Collections.emptyList();
        }
    }

    public List<PairData> getDataBeans(int ruleId, int httpPatternId) {
        String query = """
        SELECT id, pair_type_id AS pairTypeId, pair_key AS pairKey, pair_value AS pairValue 
        FROM http_pair_data 
        WHERE rule_id = ? AND http_pattern_id = ?
    """;

        try {
            log.debug("Fetching PairData for ruleId: {}, httpPatternId: {}", ruleId, httpPatternId);
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(PairData.class), ruleId, httpPatternId);
        } catch (Exception e) {
            log.error("Error fetching PairData for ruleId: {}, httpPatternId: {}", ruleId, httpPatternId, e);
            return Collections.emptyList();
        }
    }

    public ServiceConfiguration getServiceConfigurationFromDb(int serviceId, int applicationId) {
        String configQuery = """
        SELECT service_id, user_details_id, created_time, updated_time, jaeger_service_id, 
               closing_window, max_data_breaks, low_enable, medium_enable, high_enable
        FROM service_configuration
        WHERE service_id = ?
    """;

        String suppressionQuery = """
        SELECT start_collection_interval, end_collection_interval, low_persistence, low_suppression,
               medium_persistence, medium_suppression, high_persistence, high_suppression,
               created_time, updated_time, user_details_id
        FROM persistence_suppression_configuration
        WHERE application_id = ?
    """;

        try {
            ServiceConfiguration config = jdbcTemplate.queryForObject(configQuery, (rs, rowNum) -> {
                ServiceConfiguration sc = new ServiceConfiguration();
                sc.setServiceId(rs.getInt("service_id"));
                sc.setLastModifiedBy(rs.getString("user_details_id"));
                sc.setCreatedTime(rs.getString("created_time"));
                sc.setUpdatedTime(rs.getString("updated_time"));
                sc.setJaegerServiceId(rs.getString("jaeger_service_id"));
                sc.setClosingWindow(rs.getInt("closing_window"));
                sc.setMaxDataBreaks(rs.getInt("max_data_breaks"));
                sc.setLowEnable(rs.getBoolean("low_enable"));
                sc.setMediumEnable(rs.getBoolean("medium_enable"));
                sc.setHighEnable(rs.getBoolean("high_enable"));
                return sc;
            }, serviceId);

            List<PersistenceSuppressionConfiguration> suppressions = jdbcTemplate.query(suppressionQuery, (rs, rowNum) -> {
                PersistenceSuppressionConfiguration psc = new PersistenceSuppressionConfiguration();
                psc.setStartCollectionInterval(rs.getInt("start_collection_interval"));
                psc.setEndCollectionInterval(rs.getInt("end_collection_interval"));
                psc.setLowPersistence(rs.getInt("low_persistence"));
                psc.setLowSuppression(rs.getInt("low_suppression"));
                psc.setMediumPersistence(rs.getInt("medium_persistence"));
                psc.setMediumSuppression(rs.getInt("medium_suppression"));
                psc.setHighPersistence(rs.getInt("high_persistence"));
                psc.setHighSuppression(rs.getInt("high_suppression"));
                psc.setCreatedTime(rs.getString("created_time"));
                psc.setUpdatedTime(rs.getString("updated_time"));
                psc.setUserDetailsId(rs.getString("user_details_id"));
                return psc;
            }, applicationId);

            config.setPersistenceSuppressionConfigurations(suppressions);
            return config;

        } catch (Exception e) {
            log.error("Error fetching service configuration for serviceId: {} and applicationId: {}", serviceId, applicationId, e);
            return null;
        }
    }


}
